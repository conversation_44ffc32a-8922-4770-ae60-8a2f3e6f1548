{"初识1-10": 1e-06, "初识单双": 1e-06, "感知数与量": 2.0742154280143537e-05, "画图游戏": 1e-06, "数与量的对应(1-5)": 8.296861712057415e-05, "数与量的对应(1-10)": 1e-06, "大小比较": 1e-06, "判断草地大小": 1e-06, "比多少问题进阶": 1e-06, "比多少推理": 1e-06, "多少比较": 1e-06, "感受数量的比较": 6.222646284043061e-05, "一一对应感受多少": 2.0742154280143537e-05, "甜度比较": 1e-06, "等量代换比轻重": 2.0742154280143537e-05, "两种物品的重量比较": 1e-06, "三种物品的重量比较": 2.0742154280143537e-05, "比高矮": 1e-06, "间接比长短": 1e-06, "判断路线长短": 1e-06, "生活中的长度": 1e-06, "绳子比长短": 1e-06, "数格子比长短": 1e-06, "直接比长短": 1e-06, "认读写数(1-5)": 4.1484308560287074e-05, "比较大小(1-5)": 0.00018667938852129183, "比较综合应用(1~5)": 1e-06, "不等式谜(1-5)": 1e-06, "认识\">\"\"<\"和\"=\"(5以内)": 1e-06, "一个数比另一个数多几或少几": 1e-06, "用一一对应的方法比较大小": 1e-06, "排队问题(问\"有\"或\"第\")": 1e-06, "排序": 0.0011200763311277509, "判断事情先后顺序": 1e-06, "数的顺序(1-5)": 6.222646284043061e-05, "0的认识": 6.222646284043061e-05, "6-9的认识": 0.00024890585136172244, "6-9的比大小": 0.00014519507996100476, "6-9的读写": 1e-06, "6-10的含义": 1e-06, "10以内数的顺序": 0.00024890585136172244, "6-10的大小关系": 1e-06, "比较大小(1-10)": 1e-06, "不等式谜(1-10)": 4.1484308560287074e-05, "打包比较(一对多比较)": 1e-06, "认识\">\"\"<\"和\"=\"(10以内)": 1e-06, "10以内数的比大小": 0.0006222646284043061, "跳格子(不同方向跳格子)": 1e-06, "10以内数的应用": 1e-06, "6~9数的顺序": 1e-06, "9以内的基数与序数": 0.000269648005641866, "排队问题单主\"第\"\"第\"问题(10以内)": 1e-06, "区分\"第几\"和\"有几\"": 1e-06, "区分基数与序数(10以内)": 1e-06, "11-20各数的读写": 0.00014519507996100476, "11~19的读写和大小比较": 1e-06, "计数器认数(20以内)": 4.1484308560287074e-05, "11~19的认识和组成": 1e-06, "十几就是十和几": 1e-06, "11-20各数的认识": 4.1484308560287074e-05, "不同数位上数字的含义": 1e-06, "小棒摆数": 1e-06, "数的组成（20以内）": 0.00037335877704258366, "20以内数的比大小": 0.00037335877704258366, "11-20各数的顺序": 0.000269648005641866, "区分基数与序数(11-20)": 4.1484308560287074e-05, "分组数的应用(100以内)": 1e-06, "跳格子(几个几个跳格子)": 1e-06, "分组数": 0.00035261662276244013, "相邻的整十数(100以內)": 1e-06, "根据计数器读数、写数(100以内)": 0.0004563273941631578, "先写数再比较大小(100以内)": 0.00014519507996100476, "数的大小比较(100以内)": 0.0004770695484433013, "连续数(100以内)": 0.0010371077140071767, "估数(100以内)": 0.0001659372342411483, "描述数之间的大小关系(多得多...)(万以内)": 0.00037335877704258366, "数的相对大小关系(100以内)": 0.001389724336769617, "按数的特点分类(100以内)": 0.00014519507996100476, "由数位上数的关系写数(100以內)": 0.0014519507996100475, "枚举法解数的拆分": 1e-06, "100以内数的组成": 0.006616747215365788, "100以内数的写法": 0.0001659372342411483, "图形表示数(100以内)": 1e-06, "再添多少就是100": 8.296861712057415e-05, "最值问题(组数)": 1e-06, "百数表": 0.001161560639688038, "数数(数到1000)": 2.0742154280143537e-05, "估计(1000以内)": 8.296861712057415e-05, "读数和写数(1000以内)": 0.0003318744684822966, "数的组成(1000以内)": 0.0006637489369645932, "数数(数到10000)": 0.00018667938852129183, "读数和写数(10000以内)": 0.0013482400282093297, "数的组成(10000以内)": 0.0007052332455248803, "组数问题(数字和已知)": 1e-06, "组数问题(组三位数)": 0.0002903901599220095, "组数问题(组四位数)": 0.000850428325485885, "方框中填数字(万以内数的比大小)": 0.0004355852398830143, "两数比大小(万以内)": 0.0004770695484433013, "多数比大小(万以内)": 0.0004770695484433013, "数轴上表示数(万以内)": 0.0002281636970815789, "求一个数接近哪个整百数或几百几十数": 4.1484308560287074e-05, "十万的认识-数数": 1e-06, "大数的写法": 1e-06, "读出、写出计数器上的数(亿以内)": 0.00018667938852129183, "改正错误并正确读写(亿以内)": 1e-06, "根据条件写数(亿以内)": 8.296861712057415e-05, "亿以内数的读法": 0.0002281636970815789, "亿以内数的写法": 0.00012445292568086122, "整万数的读法和写法": 2.0742154280143537e-05, "按要求组最大数或最小数(亿以内)": 0.0001659372342411483, "十万的认识-组成": 2.0742154280143537e-05, "数的组成(万以上)": 2.0742154280143537e-05, "数的组成(亿以内)": 0.00012445292568086122, "根据读0个数的组数问题(亿以内)": 0.0001659372342411483, "读出、写出计数器上的数(亿以上)": 1e-06, "亿以上数的读法": 8.296861712057415e-05, "亿以上数的写法": 0.00018667938852129183, "按要求组最大数或最小数(亿以上)": 0.00010371077140071768, "根据条件写数(亿以上)": 6.222646284043061e-05, "根据读0个数的组数问题(亿以上)": 8.296861712057415e-05, "10的再认识": 1e-06, "认识计数单位十万、百万、千万、亿": 4.1484308560287074e-05, "认识十万以上的计数单位": 1e-06, "认识含亿级的数位顺序表": 0.0002903901599220095, "含万级数位顺序表的应用": 0.00010371077140071768, "数位、数级和数位顺序表的认识": 4.1484308560287074e-05, "十进制计数法": 0.00020742154280143537, "比较大小的灵活运用(亿以内)": 8.296861712057415e-05, "比较大小的灵活运用(亿以上)": 1e-06, "两个数比较大小(亿以内)": 0.00020742154280143537, "两个数比较大小(亿以上)": 1e-06, "多个大数比较大小(亿以内)": 4.1484308560287074e-05, "多个数比较大小(亿以上)": 1e-06, "感知十万的大小": 1e-06, "自然数的产生": 1e-06, "认识自然数": 0.0003318744684822966, "认识数字": 0.00010371077140071768, "以\"亿\"作单位的改写与取近似数(小数)": 1e-06, "整万数的改写": 0.0006637489369645932, "整亿数的改写": 0.0001659372342411483, "求亿以内数的近似数": 0.00020742154280143537, "求亿以上数的近似数": 1e-06, "区分近似数与准确数": 4.1484308560287074e-05, "通过近似数反推原数(亿以上)": 1e-06, "求万以内数的近似数": 0.001389724336769617, "运用\"四舍五入\"法解决问题": 1e-06, "因数的运用-": 0.0008089440169255979, "因数和倍数的意义及关系": 0.0024475742050569372, "找出一个数的因数": 0.0013482400282093297, "运用因数解决实际问题": 0.0013274978739291864, "4的倍数特征": 0.00037335877704258366, "找出一个数的倍数-": 0.00031113231420215305, "从条件出发解决倍数问题": 2.0742154280143537e-05, "解决一个数的因数是另一个数的倍数的实际问题": 6.222646284043061e-05, "一个数的因数是另一个数的倍数": 0.0001659372342411483, "运用倍数解决实际问题": 0.0002281636970815789, "因数与倍数综合-": 0.0009748812511667462, "2、5的倍数特征": 0.0035054240733442574, "2的倍数特征": 0.0004355852398830143, "5的倍数特征": 0.0003318744684822966, "解决有关2、5的倍数的实际问题": 0.00014519507996100476, "解决有关2的倍数的实际问题": 0.00014519507996100476, "解决有关5的倍数的实际问题": 0.0003318744684822966, "3的倍数特征（考法需整合）": 0.0021779261994150714, "解决有关3的倍数的实际问题": 0.0001659372342411483, "2、3倍数的运用": 0.0007052332455248803, "2、5、3倍数的运用（一）": 0.0013067557196490428, "3、5倍数的运用": 0.0006222646284043061, "解决有关2、3的倍数的实际问题": 0.00010371077140071768, "解决有关2、5、3的倍数的实际问题": 0.00035261662276244013, "解决有关3、5的倍数的实际问题": 0.00018667938852129183, "9的倍数特征": 0.0006222646284043061, "偶数的认识（辨析、判断、组偶数）": 1e-06, "奇数的认识（辨析、判断、推理）": 1e-06, "奇数与偶数的运用": 0.0006637489369645932, "运算结果奇偶性综合运用-": 0.001203044948248325, "差的奇偶性": 0.0001659372342411483, "和的奇偶性": 0.0012445292568086122, "积的奇偶性": 0.000580780319844019, "合数的认识": 0.00031113231420215305, "质数的认识": 0.0010371077140071767, "因、倍、质、合、奇、偶数综合运用": 0.0008296861712057415, "质合综合运用（辨析、推理）": 1e-06, "运用质数的特征解决组合质数的问题": 0.0006222646284043061, "质数与合数解决实际问题": 0.0002903901599220095, "分解质因数": 0.00024890585136172244, "分解质因数及其应用": 0.00010371077140071768, "运用推理法解决质数积的问题": 2.0742154280143537e-05, "公因数和最大公因数的意义": 0.0011200763311277509, "求两个数公因数/最大公因数的方法": 0.0015349194167306216, "认识互质数": 0.000269648005641866, "用求公因数的方法解决实际问题": 0.003484681919064114, "公倍数和最小公倍数的意义": 0.0015349194167306216, "求两个数的最小公倍数的方法": 0.0004770695484433013, "求两个数的最小公倍数的特殊情况": 0.001161560639688038, "用公倍数、最小公倍数解决复杂的实际问题": 0.0013067557196490428, "用公倍数、最小公倍数解决简单的实际问题": 0.0023438634336562197, "用最小公倍数解决拼瓷砖问题": 0.00018667938852129183, "运用最小公倍数解决植树问题": 0.00018667938852129183, "倍的含义": 0.00012445292568086122, "解决几倍多几和少几的问题": 0.00010371077140071768, "与倍相关的应用": 0.0001659372342411483, "运用画图法解决倍数问题": 0.00014519507996100476, "小数的认识": 0.07894463919022629, "小数": 1e-06, "小数的意义(两位及以上小数)": 0.0011200763311277509, "小数的意义(一位小数)": 1e-06, "用分数和小数表示阴影部分": 0.002281636970815789, "在数线上表示小数": 0.0012237871025284686, "带单位的小数比较": 0.0016801144966916263, "求存期": 0.0001659372342411483, "长方体棱长的认识及运用-": 0.0010163655597270331, "合数的运用9": 6.222646284043061e-05, "质数的运用": 0.0003941009313227272, "哥德巴赫猜想的运用-": 0.00024890585136172244, "数的认识及分类（含负数）-": 0.0004978117027234449, "考试成绩中的实际问题（正负数）-": 0.0001659372342411483, "用正负数解决其它实际问题-": 0.00041484308560287074, "加法问题结构": 0.00018667938852129183, "求一个数比另一个数多（少）几": 0.000539296011283732, "求比一个数多（少）几的数": 0.0008296861712057415, "线段图法解决问题": 0.00020742154280143537, "减法问题结构": 0.00010371077140071768, "提出问题并解决问题（100以内数）": 0.000539296011283732, "税率的意义-": 0.0001659372342411483, "运用乘、除法各部分间的关系巧解算式": 0.00012445292568086122, "加、减、乘、除各部分间的关系的计算与应用": 0.0007467175540851673, "用含有括号的四则混合运算解决实际问题": 0.0015971458795710522, "直尺测量(毫米、厘米、分米)": 4.1484308560287074e-05, "钱数问题": 0.00041484308560287074, "付款与找零": 0.000580780319844019, "购物预算（钱数已知）": 0.0004355852398830143, "队列问题": 0.00018667938852129183, "最省钱问题": 0.0010163655597270331, "四个方向描述简单的行走路线": 0.0004978117027234449, "涂色的小正方体个数": 0.0006637489369645932, "运用乘法交换律和结合律解决实际问题": 0.0012652714110887556, "乘法运算定律的综合运用": 0.0006222646284043061, "乘法运算定律和除法的运算性质的综合运用": 0.001078592022567464, "运用除法的运算性质解决实际问题": 0.0010578498682873203, "倍数的运用-": 8.296861712057415e-05, "按要求分图形": 0.0001659372342411483, "等分(按指定的份数平均分)": 0.000850428325485885, "两位数乘两位数，积的位数": 0.0006222646284043061, "连乘的计算与应用": 0.0013689821824894733, "连除的计算与应用": 0.001389724336769617, "方向变化推理题（四个方向）": 0.0008711704797660285, "方向变化推理题（八个方向）": 0.00018667938852129183, "填算符（除数是一位数的除法）": 2.0742154280143537e-05, "除法意义的理解与运用（移后删）": 0.0007259753998050237, "表内乘法、除法解决问题（1-6）": 0.00024890585136172244, "商中间有0的一位数除法的实际问题": 0.0009541390968866026, "除法计算与运用(2-6)": 0.0006637489369645932, "几百几十（几千几百）数除以一位数的口算运用": 0.00024890585136172244, "油桶问题（除数是一位数的除法）": 0.0001659372342411483, "长方体和正方体体积综合": 0.0010371077140071767, "等体积变形运用（长正方体）": 0.0010578498682873203, "正方体底面积": 0.00018667938852129183, "长方体底面积": 0.0004563273941631578, "正方体棱长变化的运用": 0.0006637489369645932, "运用观察法解决组合立体图形的问题": 0.0001659372342411483, "求容积": 0.0004978117027234449, "长正方体拼接问题": 0.0004978117027234449, "长正方体拼结的表面积问题": 0.0009333969426064591, "长/正方体挖取部分的体积问题-（考法需重放）": 0.0009956234054468898, "长方体和正方体棱长综合-": 0.0006844910912447367, "灵活选择估算策略解决问题": 0.0012237871025284686, "数小正方体的数量，并求添加几个小正方体后能成为大正方体": 1e-06, "逻辑推理题": 6.222646284043061e-05, "复式统计表中开放性问题": 0.00035261662276244013, "质、合、奇、偶数综合运用": 0.0013067557196490428, "因、倍、奇、偶数综合运用": 0.0007467175540851673, "十几减9的计算方法": 0.00012445292568086122, "理解加法与减法之间的互逆关系（十几减9）": 0.0001659372342411483, "被减数、减数和差中的规律": 0.00014519507996100476, "理解加法与减法之间的互逆关系（十几减8、7、6）": 0.00018667938852129183, "理解加法与减法之间的互逆关系（十几减5、4、3、2）": 6.222646284043061e-05, "十几减5、4、3、2的实际问题": 0.0004770695484433013, "火柴棒游戏（20以内）": 2.0742154280143537e-05, "十几减7、6的应用": 8.296861712057415e-05, "补砖块问题": 0.0003318744684822966, "图形识别与匹配": 0.00024890585136172244, "巧用加法运算定律和减法的运算性质": 0.00031113231420215305, "求纳税额": 0.0012445292568086122, "十几减8的计算方法": 0.00012445292568086122, "想加算减(十几减7、6)": 1e-06, "想加算减（十几减5、4、3、2）": 2.0742154280143537e-05, "破十法(十几减5、4、3、2)": 4.1484308560287074e-05, "平十法(十几减5、4、3、2)": 1e-06, "求收入额-": 4.1484308560287074e-05, "税率有关的其它实际问题": 0.00024890585136172244, "求税后额": 0.00018667938852129183, "求税前额": 0.00010371077140071768, "用同样的平面图形进行拼组": 0.000539296011283732, "分率、分数量比较（如1/4与1/4米）-": 0.0014104664910497605, "其他倍数特征": 0.0003318744684822966, "图形算式问题": 0.00020742154280143537, "错中求解（十几减几）": 4.1484308560287074e-05, "公因数与最大公因数的运用-": 0.0002281636970815789, "公倍数与最小公倍数的运用": 0.0010578498682873203, "加、减、乘、除运算定律/性质的综合运用": 0.0013067557196490428, "两位数乘两位数的笔算(有进位)的实际问题": 0.0021779261994150714, "积末尾0的个数（两位数乘两位数笔算）": 6.222646284043061e-05, "两位数乘两位数的笔算(不进位)的应用": 0.0007467175540851673, "整十、整百、整千数除以一位数的应用": 0.0004978117027234449, "两位数除以一位数的口算除法的应用": 0.00041484308560287074, "自然数的运用": 0.00014519507996100476, "探究周长（面积）相等的图形面积（周长）变化的情况": 0.0004563273941631578, "求所剩图形的面积与周长问题": 8.296861712057415e-05, "探究面积": 8.296861712057415e-05, "长方体、正方体表面积综合（考法需整合）": 0.000269648005641866, "长方体侧面积": 8.296861712057415e-05, "正方体侧面积": 2.0742154280143537e-05, "运用乘法运算定律简算": 0.0013689821824894733, "真、假、带分数的综合运用": 0.0014934351081703347, "真分数的运用": 0.001203044948248325, "假分数的运用": 0.0005185538570035884, "定义新运算": 0.0002903901599220095, "等高等体积的圆柱和圆锥-": 0.00014519507996100476, "等底等体积的圆柱与圆锥-": 0.0003941009313227272, "等底等高的圆柱和圆锥-": 0.0009748812511667462, "和公因数、公倍数有关的新定义问题": 2.0742154280143537e-05, "用逆推法解决年龄问题": 0.00012445292568086122, "等积变形问题（含圆柱）-": 0.0003318744684822966, "算式比大小（十几减8）": 6.222646284043061e-05, "移多补少（十几减几）": 6.222646284043061e-05, "三位数除以一位数的笔算的应用（被除数首位能整除）": 1e-06, "竖切圆锥体积的计算": 0.00020742154280143537, "圆锥展开图与表面积": 2.0742154280143537e-05, "推理前几天或后几天是几月几日": 0.00024890585136172244, "在钟面上画相应的时间": 0.00012445292568086122, "解决有关小数大小比较的实际问题（一位小数）": 0.0014519507996100475, "分段计算费用问题": 0.00031113231420215305, "轴对称图形的初步应用": 0.0004355852398830143, "正方体棱长的认识及运用-": 0.00012445292568086122, "不计算判断结果": 0.0004978117027234449, "钟表上分针的旋转": 0.0001659372342411483, "根据平移的方向和距离解决问题": 0.00035261662276244013, "运用对称解决剪纸问题": 0.00018667938852129183, "根据所给数字组数写满足要求的算式": 0.00031113231420215305, "多位数乘一位数的错中求解问题": 6.222646284043061e-05, "两位数乘一位数的口算(有进位)的应用": 0.00012445292568086122, "几百几十数乘一位数的口算(有进位)的应用": 0.0002903901599220095, "两位数乘整百数的口算及应用": 0.00014519507996100476, "根据小数的意义进行单位换算(元角分/长度/质量)": 0.0002903901599220095, "隐藏的差倍问题（两位数除以一位数）": 0.00018667938852129183, "小数与单位换算(综合)": 0.0022401526622555018, "由涂色情况反求体积-": 0.0001659372342411483, "加法、乘法交换律和结合律的综合应用": 0.00020742154280143537, "一个数添（少）几是另一个数": 0.00012445292568086122, "数的表征": 0.0003318744684822966, "100以内数的读法": 8.296861712057415e-05, "用固定数珠子在计数器上表示数": 0.00018667938852129183, "几十几添上一个数是多少": 4.1484308560287074e-05, "认识100": 0.0001659372342411483, "计数器上增添珠子后表示的数": 0.0001659372342411483, "数的排列规律（100以内）": 0.000269648005641866, "数的大小比较的应用": 0.0004355852398830143, "加减法(整十数加一位数及相应的减法)的应用": 0.0006015224741241625, "探索珠子的个数与摆出的数的个数之间的关系": 0.00031113231420215305, "数的顺序（100以内）的应用": 0.00018667938852129183, "整十数加、减整十数的应用": 0.00020742154280143537, "用数的组成解决问题": 0.0006637489369645932, "口算两位数加整十数(不进位)的应用": 0.00018667938852129183, "错中求解(乘法分配律)": 0.0008711704797660285, "组数问题": 0.00012445292568086122, "小数的数位顺序表与计数单位综合应用": 0.0012237871025284686, "根据读0个数的组数问题(小数)": 0.0007467175540851673, "重叠问题（100以内）": 6.222646284043061e-05, "逻辑推理": 4.1484308560287074e-05, "利用加减混合运算解决实际问题": 8.296861712057415e-05, "低级单位换算高级单位": 0.0010993341768476073, "高级单位换算低级单位": 0.0006222646284043061, "根据小数的意义进行元角分的单位换算": 0.00024890585136172244, "根据小数的意义进行长度单位的换算": 0.0007467175540851673, "漏看小数点写数问题": 0.0006015224741241625, "小数的读法(一位小数)": 0.0008711704797660285, "小数的读法(两位及以上)": 0.0026964800564186595, "小数的读法和写法(两位及以上)": 0.0006015224741241625, "小数的写法(两位及以上)": 0.0009333969426064591, "纯循环小数和混循环小数": 4.1484308560287074e-05, "循环小数的简写": 0.0003318744684822966, "循环小数的意义": 0.00037335877704258366, "循环小数比大小": 0.0008089440169255979, "循环小数与周期问题": 6.222646284043061e-05, "小数的化简": 0.000891912634046172, "小数的性质": 0.008089440169255979, "运用小数的性质解决问题": 0.0011823027939681814, "小数的计数单位": 0.0028001908278193774, "小数的数位顺序表": 0.001472692953890191, "小数的组成": 0.0018875360394930618, "按要求写数": 0.001203044948248325, "按要求写小数与组数问题": 0.0003318744684822966, "小数组数问题": 0.000539296011283732, "利用小数点移动引起小数大小的变化解决实际问题": 0.0034224554562236834, "认识小数点移动的规律": 0.00024890585136172244, "小数点向右移动与小数的大小变化(小数乘法)": 1e-06, "小数点移动规律的逆用": 0.0014519507996100475, "小数点移动规律的正用": 0.005247765032876314, "小数的大小比较（一位小数）": 0.0015349194167306216, "多个小数的大小比较": 0.0018667938852129182, "多个小数的大小比较(元角分)": 1e-06, "方框里填最大最小数(小数比大小)": 0.0008296861712057415, "解决有关小数大小比较的实际问题": 0.001742340959532057, "两个小数的大小比较": 0.0004978117027234449, "小数化成分数": 0.0006430067826844496, "分数化成小数": 0.001514177262450478, "小数的改写": 0.0018253095766526312, "以\"万\"作单位的改写与取近似数(小数)": 1e-06, "改写与近似数": 2.0742154280143537e-05, "根据近似数求原数问题(小数)": 0.0042728837817095685, "用\"四舍五入\"法取循环小数的近似值": 1e-06, "分数的各部分名称以及读写法": 4.1484308560287074e-05, "认识几分之一": 0.00018667938852129183, "平均分与分数-": 0.0016386301881313394, "分数的意义": 0.008773931260500715, "几分之一的应用": 1e-06, "认识几分之几": 0.0004978117027234449, "认识一个物体的几分之几": 1e-06, "认识整体的几分之几(整体是多个物体)": 1e-06, "分数单位的意义": 0.0015349194167306216, "单位\"1\"的意义": 1e-06, "分数与除法的转化": 0.0008296861712057415, "几分之一的大小比较": 0.00020742154280143537, "同分母分数的大小比较": 0.00041484308560287074, "分数比大小综合(同分母/同分子)": 1e-06, "利用分数墙找相等的分数": 1e-06, "分数墙的应用": 6.222646284043061e-05, "分数的大小比较": 0.003318744684822966, "根据分数大小比较解决实际问题(同分母/同分子)": 1e-06, "利用分数墙比较两个数的大小": 1e-06, "通分法比较分数大": 1e-06, "运用分数通分比较大小来解决问题": 0.002323121279376076, "假分数的意义和特征": 0.000580780319844019, "认识带分数": 0.0007467175540851673, "真分数的意义和特征": 0.0002281636970815789, "按要求写分数": 0.0001659372342411483, "运用推理法和尝试法求分数问题": 0.000269648005641866, "带分数与假分数互化": 0.00035261662276244013, "假分数、整数、带分数互化": 0.0004563273941631578, "分数基本性质": 0.0021779261994150714, "分数的基本性质应用": 0.0006844910912447367, "根据分数基本性质还原分数": 0.00020742154280143537, "运用分数的基本性质进行改写": 0.0005600381655638754, "约分的意义和方法": 0.001203044948248325, "通分": 1e-06, "通分的意义和方法": 0.0009126547883263156, "最简分数的意义": 0.0021156997365746407, "比较分数与小数的大小": 0.0018667938852129182, "分数与小数互化的应用": 0.00037335877704258366, "倒数的概念": 0.00020742154280143537, "求一个数的倒数": 0.000269648005641866, "百分数的意义": 0.0013482400282093297, "百分数在具体情景中的含义": 8.296861712057415e-05, "成数的意义": 0.000850428325485885, "求合格率（删）": 1e-06, "分数与百分数": 1e-06, "百分数的读法和写法": 4.1484308560287074e-05, "含百分数的大小比较": 0.00018667938852129183, "百分数和分数、小数的互化": 0.000580780319844019, "百分数化分数": 8.296861712057415e-05, "百分数化小数": 4.1484308560287074e-05, "分数化百分数": 4.1484308560287074e-05, "小数化百分数": 4.1484308560287074e-05, "温度高低的比较": 0.00020742154280143537, "温度中的负数": 0.0006637489369645932, "0的认识和理解": 2.0742154280143537e-05, "正负数的读法和写法": 0.0006222646284043061, "成绩中的负数": 0.00018667938852129183, "海拔高度的表示": 0.0006637489369645932, "楼层中的负数": 0.00014519507996100476, "时区时差中的负数": 0.00020742154280143537, "收支中的负数": 0.00024890585136172244, "位置中的负数": 0.000539296011283732, "一个数据两种理解（正负数）-": 1e-06, "用正、负数表示相反意义的量-": 0.00014519507996100476, "数图结合解决负数的实际问题": 0.00010371077140071768, "正数与负数的概念、意义": 0.0001659372342411483, "质量中的负数": 0.0007052332455248803, "借助直线上的点体会正负数": 1e-06, "用直线上的点表示正、负数": 0.0010578498682873203, "正、负数的大小比较": 0.0004978117027234449, "1-5的分与合解决实际问题": 0.00012445292568086122, "1-5的分与合的简单应用": 1e-06, "1-5的分与合": 4.1484308560287074e-05, "加法的含义(1-5)": 1e-06, "加法横式谜(1-5)": 2.0742154280143537e-05, "看图列加法算式(1-5)": 1e-06, "1~5的计算": 1e-06, "1~5加法的含义": 2.0742154280143537e-05, "加法计算(1-5)": 0.00018667938852129183, "加法算式各部分名称(1-5)": 1e-06, "加法应用(1-5)": 0.00024890585136172244, "减法的含义(1-5)": 1e-06, "减法横式谜(1-5)": 1e-06, "看图列减法算式(1-5)": 1e-06, "1-5减法的含义": 6.222646284043061e-05, "1-5的减法": 1e-06, "减法计算(1-5)": 8.296861712057415e-05, "减法算式各部分名称": 1e-06, "减法计算的实际应用(1-5)": 8.296861712057415e-05, "0的加减法的实际应用": 1e-06, "得数为0的减法": 0.00018667938852129183, "算式中有0的加减法": 0.00012445292568086122, "6、7的分合的应用": 1e-06, "6、7的分合与计算": 1e-06, "6的加减法": 1e-06, "6、7的减法的实际应用": 0.00014519507996100476, "6、7的加减法的计算": 1e-06, "6 、7的加减法的应用": 0.0004978117027234449, "8、9的分合的应用": 0.00041484308560287074, "8、9的分合与计算": 1e-06, "8、9的加减法的计算": 0.00018667938852129183, "9以内的加减法应用": 1e-06, "发现问题并用多种方法解决": 1e-06, "8、9的加减法的应用": 0.00041484308560287074, "看图列式计算(求部分)": 1e-06, "求一共(1-10)": 1e-06, "数字游戏(9以内)": 1e-06, "图文算式(10以内)": 0.00010371077140071768, "文字应用题(求部分)": 2.0742154280143537e-05, "6-10的拆分": 1e-06, "6-10的拆分(应用)": 1e-06, "6-10的分与合": 1e-06, "分与合提升": 1e-06, "求部分(1-10)": 1e-06, "数的拆分(6~10)": 1e-06, "10的分与合": 0.0003318744684822966, "10的计算": 0.00020742154280143537, "10的加减法的应用": 0.0002903901599220095, "按群计数": 1e-06, "好玩的\"抢10\"": 1e-06, "推算(10)": 1e-06, "计算(10)": 1e-06, "加法计算(6-10)": 1e-06, "减法计算(6-10)": 1e-06, "看图列加法算式(6-10)": 1e-06, "多个数的多步运算(连加连减1-10)": 1e-06, "看图列式(10以内的数)": 1e-06, "连加计算(9以内)": 1e-06, "连加计算(10以内)": 0.0001659372342411483, "连减计算(9以内)": 1e-06, "连减的应用（10以内）": 0.0002281636970815789, "补全问题并解答(10以内)": 1e-06, "填数游戏(多步运算)": 1e-06, "加减综合应用(1~5)": 1e-06, "多个数的多步运算(加减混合1-10不进退位)": 1e-06, "加减混合计算(10以内)": 0.00018667938852129183, "解决问题(列综合算式)": 1e-06, "加减混合运用(10以内加减混合)": 0.00024890585136172244, "摸牌游戏(10以内)": 1e-06, "有隐含条件的问题": 1e-06, "10以内的连加连减与加减混合": 1e-06, "加法表(10以内)": 1e-06, "10以内加法表": 1e-06, "加法表和减法表(0-5)": 1e-06, "减法表(10以内)": 1e-06, "10以内减法表": 2.0742154280143537e-05, "9以内的加、减法表": 1e-06, "十几加几的不进位加法的应用": 0.000269648005641866, "十几减几的不退位减法": 0.0003941009313227272, "与10相关的计算": 6.222646284043061e-05, "不进位不退位的加减法(11~19)": 0.00014519507996100476, "看图列式(十几减9)": 6.222646284043061e-05, "平十法(十几减9)": 2.0742154280143537e-05, "破十法(十几减9)": 4.1484308560287074e-05, "数墙（20以内）": 1e-06, "减号后面的数不变": 1e-06, "平十法(十几减7、6）": 1e-06, "破十法(十几减7、6）": 1e-06, "解决含有多余条件的实际问题": 0.0010578498682873203, "求还差多少": 2.0742154280143537e-05, "十几减8的实际问题": 0.0004770695484433013, "十几减9的实际问题": 0.000580780319844019, "减号前面的数不变": 1e-06, "看图列式(十几减5、4、3、2)": 1e-06, "十几减5、4、3、2的计算": 0.000269648005641866, "减法表(20以内退位减)": 1e-06, "9加几": 0.00035261662276244013, "8、7、6加几": 0.0004563273941631578, "8加几的进位加法": 0.0004563273941631578, "7、6、5加几的进位加法": 1e-06, "7、6加几": 1e-06, "5、4、3、2加几": 8.296861712057415e-05, "加法表(20以内进位)": 1e-06, "加法位置原理(20以内)": 1e-06, "加法表及应用(20以内进位)": 1e-06, "摸牌游戏": 1e-06, "十几加几和十几减几的应用": 1e-06, "猜数游戏": 1e-06, "加减混合连加连减(20以内进位)": 4.1484308560287074e-05, "加减混合连加连减(20以内退位)": 0.00012445292568086122, "11~20加减法的简单应用": 1e-06, "比多比少问题(20以内)": 1e-06, "求补多少一样多": 1e-06, "求相差几": 1e-06, "加减法(整十数加一位数及相应的减法)解决实际问题": 0.0009126547883263156, "口算(整十数加一位数及相应的减法)": 0.0002281636970815789, "整十数加、减整十数解决实际问题": 0.00020742154280143537, "口算整十数加、减整十数": 6.222646284043061e-05, "口算两位数加一位数(不进位)": 0.00035261662276244013, "错题改正(两位数加一位数、整十数)": 8.296861712057415e-05, "口算加法的综合应用(两位数加两位数/一位数/整十数)": 1e-06, "口算两位数加整十数(不进位)": 6.222646284043061e-05, "口算两位数减一位数(不退位)": 0.0001659372342411483, "错题改正(两位数减一位数、整十数)": 2.0742154280143537e-05, "口算两位数减整十数的应用": 0.0005600381655638754, "口算两位数减整十数(不退位)": 0.00012445292568086122, "笔算两位数加两位数(不进位加)": 0.00037335877704258366, "口算两位数加两位数(不进位)": 0.00012445292568086122, "笔算两位数减两位数(不退位减)": 6.222646284043061e-05, "口算两位数减两位数(不退位)": 6.222646284043061e-05, "口算两位数加一位数(进位)": 0.00037335877704258366, "口算两位数减一位数(退位)": 0.0002903901599220095, "比多少应用题(一步)(100以内)": 0.00014519507996100476, "笔算两位数加两位数(进位加)": 0.00024890585136172244, "加法应用题(100以内)": 0.0003941009313227272, "口算两位数加两位数(进位加)": 6.222646284043061e-05, "笔算两位数减两位数(退位减)": 0.00031113231420215305, "减法应用题(100以内)": 0.00010371077140071768, "口算两位数减两位数(退位减)": 6.222646284043061e-05, "数学游戏": 1e-06, "笔算(连加)": 2.0742154280143537e-05, "连加口算的计算与应用": 0.0002281636970815789, "连加应用题": 0.00010371077140071768, "笔算(连减)的计算与应用": 1e-06, "连减解决实际问题（100以内）": 0.001078592022567464, "连减应用题": 0.00014519507996100476, "加减法流程图(100以内口算)": 1e-06, "比多少应用题(两步)": 4.1484308560287074e-05, "笔算(加减混合)": 0.00014519507996100476, "从条件出发解决有关加减法运算的实际应用题": 1e-06, "加减混合应用题": 0.0002903901599220095, "口算(加减混合)": 0.00024890585136172244, "连续两问综合应用题": 2.0742154280143537e-05, "列等式(100以内)": 1e-06, "数墙(100以内)": 1e-06, "几百几十数的加法(笔算)": 0.0002281636970815789, "几百几十数的加法(口算)": 0.00014519507996100476, "几百几十数的加法解决问题": 0.0002281636970815789, "几百几十数的减法(笔算)": 0.00012445292568086122, "几百几十数的减法(口算)": 2.0742154280143537e-05, "几百几十数的减法解决问题": 0.00012445292568086122, "整百、整千数、几百几十数的加减法": 0.0007259753998050237, "两位数加两位数的口算(不进位)": 0.00010371077140071768, "两位数加两位数的口算(进位)": 0.000269648005641866, "两位数加两位数的口算的应用题": 0.00024890585136172244, "两位数减两位数的口算(不退位)": 4.1484308560287074e-05, "两位数减两位数的口算(退位)": 0.00010371077140071768, "两位数减两位数的口算的应用题": 0.000269648005641866, "三位数加两、三位数(不进位)的计算": 0.0002281636970815789, "三位数加两、三位数(不进位)的应用": 0.00010371077140071768, "三位数加两、三位数(一次进位)的计算": 0.0003941009313227272, "三位数加两、三位数(一次进位)的应用": 0.00010371077140071768, "三位数加两、三位数(连续进位)的计算": 0.00018667938852129183, "三位数加两、三位数(连续进位)的应用": 8.296861712057415e-05, "三位数减两、三位数(不退位)的计算": 0.00010371077140071768, "三位数减两、三位数(不退位)的应用": 2.0742154280143537e-05, "三位数减两、三位数(一次退位)的计算": 0.00024890585136172244, "三位数减两、三位数(一次退位)的应用": 0.00012445292568086122, "三位数减两、三位数(连续退位)的计算": 6.222646284043061e-05, "三位数减两、三位数(连续退位)的应用": 4.1484308560287074e-05, "三位数减两、三位数(连续退位，中间有0)的计算": 2.0742154280143537e-05, "三位数减两、三位数(连续退位，中间有0)的应用": 2.0742154280143537e-05, "解决起点为0的有关里程表的实际问题": 1e-06, "借助直观图和线段图解决起点非0的里程问题": 1e-06, "三位数加两、三位数的验算": 6.222646284043061e-05, "三位数减三位数的验算": 0.00014519507996100476, "三位数连加运算": 6.222646284043061e-05, "三位数连加的简单应用": 0.00014519507996100476, "三位数连减应用": 2.0742154280143537e-05, "三位数连减运算": 1e-06, "几百几十数的加减混合运算解决问题": 0.00012445292568086122, "两位数加减混合运算应用题": 6.222646284043061e-05, "三位数加减混合运算": 0.0007052332455248803, "最大、最小的几位数问题": 0.00018667938852129183, "比较数的大小解决实际问题": 0.0002281636970815789, "珠子摆数解决实际问题": 0.00012445292568086122, "摸硬币游戏": 2.0742154280143537e-05, "组成比例（选比、补数、互倒、因数）": 1e-06, "图与比例(国旗、正长三平)": 1e-06, "比较小数大小的方法": 0.0027379643649789467, "长度/面积/体积/容积中的负数": 2.0742154280143537e-05, "选用一种图形作单位来测量面积": 0.0014312086453299039, "探索面积与周长的关系（方格纸）": 0.00014519507996100476, "小数比大小的应用": 0.0006430067826844496, "图形剪切后的面积与周长": 8.296861712057415e-05, "辨析情景中分数大小（单位1不同）-": 0.0004355852398830143, "1平方厘米小正方形的拼接问题（无图）": 0.0009956234054468898, "分数与单位换算（假/带）-": 0.00010371077140071768, "分数比大小（真、假、带、整）-": 0.00041484308560287074, "长（正）方形裁剪后的面积问题": 0.00031113231420215305, "分数与除法关系的应用-": 0.003484681919064114, "分数量的理解（如3/4千克）-": 0.001203044948248325, "长方形与正方形互化后的面积问题": 0.00037335877704258366, "真假带分数认识综合-": 0.0019497625023334924, "用长（正）方形进行拼组解决面积问题": 0.001161560639688038, "单位换算（分数）-": 0.0007674597083653108, "长（正）方形的剪拼（涉及单位换算）": 0.00020742154280143537, "长方形与正方形综合应用解决实际问题": 0.00010371077140071768, "已知面积，求长（宽）或边长（涉及单位换算）": 0.00014519507996100476, "露出部分推整体": 0.0006637489369645932, "隐藏的差倍问题（三位数除以一位数）": 0.00020742154280143537, "隐藏的和倍问题（两位数除以一位数）": 0.00018667938852129183, "隐藏的和倍问题（三位数除以一位数）": 0.00041484308560287074, "两位数乘两位数估算解决实际问题": 0.0004978117027234449, "分数的产生": 0.0001659372342411483, "统计表的选择": 0.00010371077140071768, "单式统计表": 4.1484308560287074e-05, "添加分数单位后，变成某数": 0.000269648005641866, "画平面图": 4.1484308560287074e-05, "含字母的分数问题-": 0.0007674597083653108, "组数问题（除数是一位数的除法）": 0.0002903901599220095, "口算两位数加两位数(不进位加)的应用": 2.0742154280143537e-05, "两位数加一位数(不进位)的实际问题": 0.0001659372342411483, "图文算式（100以内的加减法）": 0.000269648005641866, "口算两位数加整十数(不进位)解决实际问题": 0.000269648005641866, "根据条件推理比例": 0.00024890585136172244, "口算两位数加一位数(不进位)的应用": 6.222646284043061e-05, "倒数与比例-": 0.0004978117027234449, "连加解决实际问题": 0.0007882018626454544, "解比例在平面图形中的应用-": 0.00010371077140071768, "两位数加两位数(不进位)解决实际问题": 0.00014519507996100476, "口算两位数加一位数(进位)的应用": 0.0004978117027234449, "两位数加一位数(进位)解决实际问题": 0.0006015224741241625, "数轴与分数-": 0.00014519507996100476, "运用小数点移动规律计算": 0.0011408184854078945, "用比例基本性质求值（解比例）-": 0.0006430067826844496, "口算两位数减整十数解决实际问题": 0.00020742154280143537, "两位数减一位数（退位）的应用": 0.00041484308560287074, "口算两位数减一位数(不退位)解决实际问题": 0.000269648005641866, "连减的计算与应用": 0.0003318744684822966, "两位数减两位数(不退位)的应用": 0.00010371077140071768, "两位数减两位数(不退位)解决实际问题": 0.00012445292568086122, "口算两位数减一位数(不退位)的应用": 0.00018667938852129183, "两位数减两位数(退位减)的应用": 0.00024890585136172244, "两位数除以一位数(被除数首位能被整除)的应用": 0.00018667938852129183, "加减混合运算解决实际问题（100以内）": 6.222646284043061e-05, "解决小数与单位换算的实际问题": 0.0016386301881313394, "图文算式（万以内）": 2.0742154280143537e-05, "两位数除以一位数的笔算(有余数)解决实际问题": 6.222646284043061e-05, "由表中数据探究两量的比例关系（正比例）-": 0.0006015224741241625, "由表中数据探究两量的比例关系（反比例）-": 0.001389724336769617, "从分数角度认识整数-": 0.0004770695484433013, "比例判断综合-": 0.004832921947273444, "谚语中的数学": 0.00018667938852129183, "七巧板的变形问题（如四巧板）": 2.0742154280143537e-05, "同一物体多个比例尺问题（一物两图）-": 0.00024890585136172244, "求实际周长、面积问题-": 0.0008089440169255979, "比例尺与行程综合应用题（相遇时间、出发时间、速度差、慢车速度）-": 1e-06, "求鸽巢数量-": 0.000580780319844019, "求鸽子总数-": 0.0006844910912447367, "解决与小数近似数有关的实际问题": 0.0005600381655638754, "把较大数改写成用\"万\"或\"亿\"作单位的数(小数)": 1e-06, "方格纸上问题综合-": 2.0742154280143537e-05, "两位数乘一位数的口算(不进位)的应用": 1e-06, "不规则图形的拼接问题": 2.0742154280143537e-05, "照样子写计算过程": 6.222646284043061e-05, "笔算两位数加两位数(不进位加)的应用": 0.00020742154280143537, "平行底面切圆锥相关问题-": 2.0742154280143537e-05, "笔算两位数加一位数(不进位)的应用": 1e-06, "笔算两位数加两位数(进位加)（删）": 1e-06, "笔算两位数加两位数(进位加)的应用": 0.00024890585136172244, "笔算两位数加两位数(进位加)解决实际问题": 0.0003941009313227272, "两位数减两/一位数（不）退位的综合问题": 1e-06, "笔算两位数减两位数(不退位减)的应用（删）": 1e-06, "笔算两位数减两位数(不退位减)解决实际问题（删）": 1e-06, "笔算两位数减两位数(退位减)（删）": 1e-06, "笔算两位数减两位数(退位减)解决实际问题": 0.00035261662276244013, "被除数、除数、商之间的关系（除数是一位数）": 0.00012445292568086122, "结合复式统计表提出问题": 4.1484308560287074e-05, "笔算(连加)解决实际问题": 8.296861712057415e-05, "加法竖式谜（100以内）": 0.0003318744684822966, "减法竖式谜（100以内）": 0.00010371077140071768, "一个数比某数多（少）几，求某数": 8.296861712057415e-05, "用小棒摆平面图形": 0.00018667938852129183, "组合平面图形计数": 0.0001659372342411483, "谁比谁多/少几分之几的理解-": 8.296861712057415e-05, "十几减几的不退位减法解决实际问题": 4.1484308560287074e-05, "通过分数基本性质变化后比较大小-": 0.0007882018626454544, "求分数与除法的实际问题（结果用假/带分数表示）-": 0.00024890585136172244, "分数基本性质解决含字母的分数问题-": 8.296861712057415e-05, "用分数基本性质解决分数值仍不变的问题-": 0.0014934351081703347, "用分数基本性质推理求分数-": 0.0001659372342411483, "求因数、公因数、最大公因数问题-": 0.000850428325485885, "将分数化为最简分数-": 0.0006015224741241625, "运用最简分数推理求值（加减的数/字母）-": 0.00041484308560287074, "求最大公因数、最小公倍数-": 0.002530542822177511, "含字母的分数通分中的推理问题-": 0.00018667938852129183, "与最小公倍数有关的推理问题-": 0.000539296011283732, "圆柱中的排水法（多个物体）-": 0.00010371077140071768, "周期问题（三位数除以一位数）": 2.0742154280143537e-05, "长方体棱长的实际问题-": 2.0742154280143537e-05, "用反比例解决行程问题-": 0.00018667938852129183, "7-9连减与除法": 0.00024890585136172244, "正比例在行程中的应用（路程、时间、辨析、能否到）": 1e-06, "由实际/图上长度，求体积问题-": 4.1484308560287074e-05, "由题中数据探究比例关系（正比例）-": 0.0001659372342411483, "积末尾0的个数（口算）": 0.0004978117027234449, "两位数乘两位数积的规律": 8.296861712057415e-05, "两位数乘两位数的简便运算": 0.0002903901599220095, "含字母的分数运算中的推理问题-": 0.0002281636970815789, "复杂情况计算比例尺-": 2.0742154280143537e-05, "优惠问题（两位数乘两位数）": 0.00012445292568086122, "比例尺、图上距离、实际距离综合-": 0.00020742154280143537, "由题中数据探究比例关系（反比例）-": 4.1484308560287074e-05, "表中两量的不同比例关系-": 0.0001659372342411483, "由图上/实际比，求实际/图上比-": 6.222646284043061e-05, "比例尺更该后图上/实际距离的计算-": 0.00010371077140071768, "比例尺与百分数（复印问题）-": 0.00012445292568086122, "三位数除以一位数的应用（有余数）": 0.00012445292568086122, "用所给数字组最简分数-": 2.0742154280143537e-05, "分数与小数互化（计算）-": 0.0015556615710107652, "两、三位数乘一位数的笔算与应用(综合)": 1e-06, "用所给数字组真、假、带分数-": 0.00010371077140071768, "运用分数约分比大小解决实际问题-": 6.222646284043061e-05, "用所给数字组相等的分数-": 4.1484308560287074e-05, "约分后比较分数大小-": 0.00020742154280143537, "与公因数/最大公因数有关的推理-": 0.0001659372342411483, "错中求解推理分数-": 6.222646284043061e-05, "分数、小数综合推理问题-": 0.00014519507996100476, "平移、旋转在拼图中的应用-": 0.0002281636970815789, "分数与数轴-": 8.296861712057415e-05, "涂色/画图理解分数基本性质的变式-": 6.222646284043061e-05, "含字母的公因数、最大公因数问题-": 8.296861712057415e-05, "结果需约分的实际应用-": 0.00035261662276244013, "倍数、公倍数、最小公倍数-": 0.00031113231420215305, "含字母的最大公因数、最小公倍数推理问题-": 0.00010371077140071768, "与最大公因数、最小公倍数有关的推理问题-": 0.0001659372342411483, "旋转的特征": 0.00024890585136172244, "注水中的折线图问题-": 8.296861712057415e-05, "找次品的方法-": 0.00031113231420215305, "已知次品轻/重找次品（多个次品）": 0.00018667938852129183, "推理解决与正负数有关的复杂问题-": 4.1484308560287074e-05, "成数的运用-": 1e-06, "利润和成数综合求定价-": 2.0742154280143537e-05, "笔算两位数加一位数(进位)": 1e-06, "笔算两位数加一位数(进位)的应用": 4.1484308560287074e-05, "求免税额度-": 2.0742154280143537e-05, "贷款利息-": 1e-06, "先打折后提价-": 0.00010371077140071768, "先提价后打折-": 0.00010371077140071768, "求促销中的折扣-": 0.00010371077140071768, "不等式横式谜（100以内加减法）": 6.222646284043061e-05, "利率、税率综合问题-": 2.0742154280143537e-05, "笔算两位数减一位数(退位)": 4.1484308560287074e-05, "圆柱的认识-": 0.0003318744684822966, "移多补少(100以内退位)": 6.222646284043061e-05, "圆柱配底问题-": 8.296861712057415e-05, "长/正方形卷成圆柱问题（涉及底面信息、高）": 8.296861712057415e-05, "长/正方形旋转成圆柱问题（求底面量、高）": 6.222646284043061e-05, "等式横式谜（100以内笔算加减法）": 0.00014519507996100476, "笔算(连减)解决实际问题": 0.00010371077140071768, "认识圆柱的容积、体积": 4.1484308560287074e-05, "圆锥的侧面积-": 1e-06, "圆锥体积的变化-": 0.00014519507996100476, "图文算式（竖式计算）": 0.00014519507996100476, "瓶中水/沙（含圆锥的正倒放）-": 0.00018667938852129183, "三角形旋转的体积问题（含圆锥）-": 0.00031113231420215305, "组数问题（100以内加减法）": 8.296861712057415e-05, "找规律填数（两位数加减两位数）": 2.0742154280143537e-05, "推理求最简分数-": 0.0008089440169255979, "推理求原分数-": 0.0005600381655638754, "横式谜（除数是一位数的除法）": 6.222646284043061e-05, "推理法求带分数-": 0.00018667938852129183, "推理法求假分数-": 4.1484308560287074e-05, "与平年和闰年有关的实际问题": 0.0010163655597270331, "蜗牛爬井": 2.0742154280143537e-05, "已知经过时间，求开始（结束）时刻": 0.002157184045134928, "长方体切/拼正方体的棱长问题-": 1e-06, "正方体切/拼为长方体棱长问题-": 1e-06, "古代计时方法与现代时间对照": 8.296861712057415e-05, "常见时间单位": 0.00018667938852129183, "与星期有关的换算": 0.00020742154280143537, "数线上的小数（一位小数）": 0.0004770695484433013, "用分数和小数表示阴影部分（一位小数）": 0.0007467175540851673, "与最大/小因数、最大/小倍数有关的问题-": 0.00037335877704258366, "猜猜我是谁（一位小数）": 4.1484308560287074e-05, "两数之间的小数": 0.0004770695484433013, "数轴上表示数(1000以内)": 0.00020742154280143537, "填数问题（一位小数）": 0.00037335877704258366, "根据条件写数(1000以内)": 0.00018667938852129183, "由算式填合适的数（一位小数）": 2.0742154280143537e-05, "比大小（一位小数加减法）": 2.0742154280143537e-05, "根据条件写数(10000以内)": 0.00018667938852129183, "小数加减法的简算（一位小数）": 2.0742154280143537e-05, "加减算式中某部分的变化问题": 0.00024890585136172244, "小数加、减法混合运算解决问题（一位小数）": 0.0005185538570035884, "学科情境中的搭配问题": 0.0007882018626454544, "长正方体挖洞的表面积问题-": 0.0001659372342411483, "较复杂的取币值问题": 0.0005600381655638754, "高变化有关的长、正方体表面积计算-": 2.0742154280143537e-05, "高变化有关的长、正方体体积计算-": 0.0001659372342411483, "小小设计师": 0.0004563273941631578, "瓶中水（长、正方体）-": 0.0004355852398830143, "长方体体积的复杂综合应用-": 4.1484308560287074e-05, "根据除余关系求除数": 0.00041484308560287074, "利用数量关系的比较解决问题": 0.00010371077140071768, "人民币的面额的应用": 0.000269648005641866, "人民币中的移多补少": 4.1484308560287074e-05, "同分母分数连减在实际生活中的应用-": 0.00041484308560287074, "分数加减混合运算的运算顺序及应用-": 0.000269648005641866, "异分母分数连加在实际生活中的应用-": 0.000850428325485885, "含字母的同分母分数加减推理问题-": 0.00012445292568086122, "分数加减中的错中求解-": 8.296861712057415e-05, "铺砖问题（涉及单位换算）": 0.0015971458795710522, "异分母分数连减在实际生活中的应用-": 0.001514177262450478, "同分母分数连加在实际生活中的应用-": 0.0001659372342411483, "挖空（粉刷）问题": 0.00031113231420215305, "同分母分数加法的计算、运用-": 0.0010993341768476073, "同分母分数减法的计算、运用-": 0.00037335877704258366, "异分母分数加法的计算、运用-": 0.0013482400282093297, "异分母分数减法的计算、运用-": 0.000580780319844019, "改变正方形的边长，求周长的变化量": 1e-06, "异分母分数连加计算-": 0.0003941009313227272, "异分母分数连减计算-": 6.222646284043061e-05, "异分母分数加法在实际生活的应用-": 0.0007259753998050237, "同分母分数减法在实际生活的应用-": 0.00035261662276244013, "差不变原理计算面积": 4.1484308560287074e-05, "推理第几天是几月几日": 8.296861712057415e-05, "年、月、日的应用": 8.296861712057415e-05, "计算经过的年数": 0.000539296011283732, "单式折线、单式条形统计图对比-": 0.00012445292568086122, "与小时有关的单位换算": 0.00012445292568086122, "数学中的语文（与时间有关）": 0.00014519507996100476, "钟表快慢与时间计算": 6.222646284043061e-05, "敲钟问题中的推理": 2.0742154280143537e-05, "高度中的负数-": 1e-06, "水位中的负数-": 1e-06, "植树问题之量体温问题-": 2.0742154280143537e-05, "根据日历表解决问题": 0.0001659372342411483, "组成比例（购物、过隧道）-": 1e-06, "看图列式（谁比谁多/少几）": 0.00012445292568086122, "看图列式（知多/少几）": 0.00010371077140071768, "结合题意编题/故事": 6.222646284043061e-05, "和差问题（100以内数）": 6.222646284043061e-05, "正比例在工程问题中的应用-": 0.00014519507996100476, "削最大正方体的体积问题-": 0.00035261662276244013, "长/正方体切割的体积问题-": 0.00031113231420215305, "不规则几何体体积（可分割为长正方体）-": 2.0742154280143537e-05, "小数大小比较的应用（一位小数）": 0.00020742154280143537, "一位小数加、减法的应用": 0.0006222646284043061, "列方程解含1个未知数的工程问题-": 0.0001659372342411483, "平行四边形切拼成长方形": 1e-06, "提出问题并解答（一位小数的加减运算）": 0.00018667938852129183, "含字母的分数与整数相乘的推理": 6.222646284043061e-05, "含字母的分数比大小-": 6.222646284043061e-05, "比例尺与行程综合应用题（车费、行驶时间、行驶速度）-": 1e-06, "小数加、减法解决较复杂购物问题": 0.0004770695484433013, "小数加减法的运用": 0.0007259753998050237, "一位小数加减法的应用（有实际情境）": 0.0001659372342411483, "小数连加解决实际问题（一位小数）": 0.000580780319844019, "小数连减解决实际问题（一位小数）": 0.00035261662276244013, "已知两量比，求两量关系-": 0.00012445292568086122, "图形的运动": 1e-06, "含百分数的估算问题-": 1e-06, "圆柱体积有关的复杂应用（两容器）-": 2.0742154280143537e-05, "由物品填合适的人民币单位": 0.0004563273941631578, "认识常见的人民币": 0.0001659372342411483, "小数加减混合简算": 8.296861712057415e-05, "求与圆有关的阴影部分面积-": 0.00014519507996100476, "错中求解（分数乘除）-": 2.0742154280143537e-05, "有优惠的购物问题-": 1e-06, "用字母表示平面图形中的量-": 8.296861712057415e-05, "看线段图列方程-": 2.0742154280143537e-05, "铺砖问题（不涉及单位换算）": 0.0002281636970815789, "由展开图信息求圆锥体积-": 1e-06, "直角梯形旋转的体积问题（含圆锥）-": 0.00018667938852129183, "水中浸物问题（长/正方体中放圆锥）-": 1e-06, "加法算式改写为乘法算式": 0.0003941009313227272, "看图列连加的算式": 1e-06, "用乘法的意义改写算式(2-6)": 2.0742154280143537e-05, "初步认识乘法的意义": 1e-06, "乘法算式的读法及各部分名称": 8.296861712057415e-05, "根据文字列乘法算式(初步认识乘法)": 1e-06, "看图写/找乘法算式(初步认识乘法)": 1e-06, "用乘法的意义改写算式(7-9)": 4.1484308560287074e-05, "用点子图表示乘法": 2.0742154280143537e-05, "图文算式(2-5)": 2.0742154280143537e-05, "5的乘法口诀应用题": 0.00041484308560287074, "5的乘法口诀理解": 1e-06, "巩固2、5乘法口诀": 1e-06, "看图写算式(5和2)": 1e-06, "2-5的乘法应用题": 0.0001659372342411483, "填数游戏(2-6的乘法口诀)": 1e-06, "2的乘法口诀的应用": 1e-06, "编制2的乘法口诀(2x9)": 1e-06, "3的乘法口诀的应用": 1e-06, "编制3的乘法口诀": 1e-06, "2-4乘法口诀的应用题-": 0.0007259753998050237, "4的乘法口诀的应用": 1e-06, "2-4的乘法口诀理解": 1e-06, "编制2-4的乘法口诀-": 1e-06, "6的乘法口诀的理解": 1e-06, "2-6的乘法应用题": 4.1484308560287074e-05, "2-6乘法口诀的计算-": 1e-06, "有趣的乘法(表内2-6)": 2.0742154280143537e-05, "7的乘法应用题（旧版）": 1e-06, "7的乘法口诀（旧版）": 1e-06, "8的乘法应用题（旧版）": 1e-06, "8的乘法口诀（旧版）": 1e-06, "9的乘法口诀（旧版）": 1e-06, "积的特征(9的乘法)（旧版）": 1e-06, "乘加、乘减(表内)": 2.0742154280143537e-05, "乘加乘减应用题(表内混合运算)": 0.0010371077140071767, "2-5的乘加乘减应用题": 0.0003941009313227272, "表内乘加乘减应用题": 0.00018667938852129183, "9的乘加乘减应用题": 0.0004978117027234449, "简单乘法应用题(5和2)": 1e-06, "2-5的乘加、乘减计算": 0.00012445292568086122, "乘法表的应用": 1e-06, "找规律(表内乘法)": 4.1484308560287074e-05, "除法的计算与运用(7、8、9)（旧版）": 1e-06, "填乘法表(1-6)": 1e-06, "乘法口诀表": 6.222646284043061e-05, "包含分(按每几个一份平均分)": 0.0009333969426064591, "大数目物品列表平均分": 0.0005185538570035884, "连减与除法(2~5)": 1e-06, "拼图形与除法": 1e-06, "平均分的综合理解与应用": 0.0004563273941631578, "理解平均分（旧版）": 1e-06, "有隐藏条件的除法应用题(2-6)": 0.0007467175540851673, "初步认识除法": 0.000850428325485885, "连减与除法(2-6)": 0.00037335877704258366, "连减与除法(2-5)": 1e-06, "除法的计算(2~5)": 1e-06, "除法应用题(2~5)": 1e-06, "用乘法口诀求商(2-6)": 0.0016386301881313394, "图文算式(2-6)": 6.222646284043061e-05, "只能算一个除法算式的口诀(2-6)": 4.1484308560287074e-05, "用7的乘法口诀求商（旧版）": 1e-06, "用8的乘法口诀求商（旧版）": 1e-06, "用9的乘法口诀求商（旧版）": 1e-06, "用乘法口诀求商(7、8、9)（旧版）": 1e-06, "还原问题的\"火车图\"": 1e-06, "简单的还原问题(倒推型)": 1e-06, "简单的还原问题(一半还多/少)": 2.0742154280143537e-05, "简单的还原问题(一半型)": 4.1484308560287074e-05, "根据乘法口诀求商(6-9)（旧版）": 1e-06, "连乘、连除和乘除混合运算": 1e-06, "连乘、连除和乘除混合运算的应用": 1e-06, "乘除法应用题(7的口诀)": 1e-06, "乘除法的应用题(表内)": 0.0009748812511667462, "乘除应用题综合(2-5)": 1e-06, "除法应用题(7、8、9)": 0.002011988965173923, "填数游戏(表内)": 1e-06, "有多余条件的除法应用题(1-9)": 1e-06, "有隐藏条件的除法应用题(1-9)": 0.00020742154280143537, "乘除法应用题(8的口诀)": 1e-06, "乘除法的计算与应用(2-6)": 0.0011408184854078945, "列多个算式的应用题（2-6含除法）-": 0.0012860135653688992, "除法应用题(2-6)（旧版）": 1e-06, "购物方案(表内)": 2.0742154280143537e-05, "有余数除法的计算与运用": 0.0011200763311277509, "认识余数": 0.0009956234054468898, "有余数除法竖式": 0.0011823027939681814, "余数与除数的关系": 0.004791437638713157, "根据除余关系求被除数": 0.0008711704797660285, "根据除余关系求除数和被除数": 0.0001659372342411483, "根据除余关系求余数": 0.0008089440169255979, "根据除余关系求余数和被除数": 0.00018667938852129183, "表内除法竖式": 4.1484308560287074e-05, "除法竖式的计算-试商法": 0.0005185538570035884, "等余问题": 0.0003941009313227272, "有余数除法的应用题": 0.0017215988052519135, "运用三位数除以一位数的笔算解决问题（有余数）": 0.00018667938852129183, "两位数除以一位数(被除数首位能整除)有余数的笔算": 1e-06, "两位数除以一位数(被除数首位能整除)有余数的实际应用题": 1e-06, "两位数除以一位数(有余数)的笔算": 4.1484308560287074e-05, "两位数除以一位数的笔算(有余数)的应用": 2.0742154280143537e-05, "三位数除以一位数的笔算（有余数）": 1e-06, "除余关系的应用（两、三位数除以一位数）": 0.0011823027939681814, "复杂周期问题": 1e-06, "简单周期问题": 0.0019497625023334924, "解决周期相关的实际问题": 1e-06, "运用口算乘法解决实际问题": 0.0002903901599220095, "运用口算来法解决问题": 1e-06, "整十、整百、整千数乘一位数的口算": 0.0002903901599220095, "解决两位数乘一位数的实际问题": 2.0742154280143537e-05, "两位数乘一位数的口算乘法": 1e-06, "两位数乘一位数的口算(不进位)": 1e-06, "两位数乘一位数的口算(有进位)": 0.0010993341768476073, "解决两、三位数乘一位数(不进位)的实际问题": 1e-06, "两、三位数乘一位数(不进位)的笔算": 1e-06, "解决两、三位数乘一位数(不连续进位)的实际问题": 1e-06, "两、三位数乘一位数(不连续进位)的笔算": 1e-06, "解决两、三位数乘一位数(连续进位)的实际问题": 1e-06, "解决三位数乘一位数(乘数末尾有0)的实际问题": 1e-06, "两、三位数乘一位数(连续进位)的笔算": 1e-06, "有关两位数乘一位数(连续进位)的实际应用": 1e-06, "有关三位数乘一位数(连续进位)的实际应用": 0.0002281636970815789, "几百几十数乘一位数的口算的实际问题": 0.0005185538570035884, "两位数乘一位数的口算的实际问题": 0.0006637489369645932, "几百几十数乘一位数的口算(有进位)": 0.0006844910912447367, "0的相关乘法计算": 8.296861712057415e-05, "解决三位数乘一位数(乘数中间有0)的实际问题": 1e-06, "三位数乘一位数(乘数末尾有0)的笔算": 1e-06, "三位数乘一位数(乘数中间有0)的笔算": 1e-06, "判断积的末尾0的个数(三位数乘两位数)": 4.1484308560287074e-05, "积末尾或中间0的个数的问题": 0.00018667938852129183, "连乘运算": 1e-06, "关于整十、整百、整千数除以一位数末尾0的个数": 1e-06, "整十、整百、整千数除以一位数的口算": 0.0011408184854078945, "整十、整百、整千数除以一位数的实际问题": 0.0008089440169255979, "关于几百几十数(几千几百数)除以一位数末尾0的个数": 4.1484308560287074e-05, "几百几十数除以一位数的实际应用题": 1e-06, "几百几十（几千几百）数除以一位数的口算": 0.001078592022567464, "几百几十（几千几百）数除以一位数的口算解决实际问题": 0.001161560639688038, "整十数、整百数除以一位数的实际应用题": 1e-06, "两位数除以一位数(每一位都能整除)的笔算": 1e-06, "两位数除以一位数的口算": 0.0005600381655638754, "两位数除以一位数的验算": 1e-06, "运用两位数除以一位数的笔算(每一位都能整除)解决问题": 1e-06, "两位数除以一位数的口算除法的实际问题": 0.0008711704797660285, "两位数除以一位数的笔算(被除数首位能被整除)": 0.000891912634046172, "两位数除以一位数(被除数首位不能被整除)解决实际问题": 0.000850428325485885, "两位数除以一位数(被除数首位不能整除)的应用": 0.000269648005641866, "两位数除以一位数的笔算(被除数首位不能被整除)": 0.00041484308560287074, "运用三位数除以一位数的笔算(每一位都能整除)解决问题": 0.00010371077140071768, "三位数除以一位数的笔算(被除数首位能整除)": 0.000580780319844019, "三位数除以一位数(每一位都能整除)的应用": 0.00010371077140071768, "三位数除以一位数(每一位都能整除)的笔算": 0.000269648005641866, "三位数除以一位数(最高位不能整除)的笔算的应用": 0.0002903901599220095, "三位数除以一位数(最高位不能整除)的笔算": 0.0006430067826844496, "运用三位数除以一位数的笔算(最高位不能整除)解决实际问题": 0.00041484308560287074, "三位数除以一位数的笔算的实际问题（被除数首位能整除）": 0.0003318744684822966, "三位数除以一位数的应用(商是两位数)": 0.0008089440169255979, "三位数除以一位数的笔算(商是两位数)": 0.0006637489369645932, "运用三位数除以一位数的笔算解决实际问题(商是两位数)": 0.0014312086453299039, "0除以一个数(不是0)": 0.0004563273941631578, "商中间有0的一位数除法(被除数中间有0)": 0.0005185538570035884, "商末尾有0的一位数除法的应用(被除数末尾有0)": 0.0006637489369645932, "商末尾有0的一位数除法(被除数末尾有0)": 0.0003318744684822966, "根据商末尾0的情况填数": 0.0009541390968866026, "商末尾有0的一位数除法(有余数)": 0.0001659372342411483, "商末尾有0的一位数除法的实际问题": 0.00041484308560287074, "商中间有0的一位数除法(被除数中间没有0)": 0.0006430067826844496, "商中间有0的一位数除法的应用": 0.0015764037252909088, "不计算判断商是几位数（除数是一位数）": 0.0014934351081703347, "用乘法和除法两步计算解决问题": 0.0005185538570035884, "运用乘除混合运算解决问题": 0.0001659372342411483, "两位数乘整十（百）数的口算": 0.0011200763311277509, "两位数乘整十数的应用": 0.0010371077140071767, "运用两位数乘整十（百）数的口算解决实际问题": 0.0018460517309327746, "两位数乘两位数的笔算(不进位)": 0.0019497625023334924, "两位数乘两位数的笔算(不进位)的实际问题": 0.0012445292568086122, "两位数乘两位数的笔算(有进位)": 0.002468316359337081, "两位数乘两位数的笔算(有进位)的应用": 0.0012237871025284686, "用连乘解决实际问题": 0.0032980025305428223, "连乘运算应用": 2.0742154280143537e-05, "三位数乘两位数的口算(几百几十数)": 0.000269648005641866, "整百数乘整十数的口算": 2.0742154280143537e-05, "几百几十数乘整十数(不进位)的口算": 1e-06, "几百几十数乘整十数的口算": 0.00012445292568086122, "三位数乘两位数笔算": 0.0006844910912447367, "三位数乘两位数积的位数问题": 8.296861712057415e-05, "三位数乘两位数(末尾有0的乘法)": 6.222646284043061e-05, "解决三位数乘两位数(因数中间有0)实际问题": 0.0001659372342411483, "三位数乘两位数(中间有0的乘法)": 1e-06, "乘法解决实际问题": 1e-06, "解决三位数乘两位数(因数末尾有0)实际问题": 0.00012445292568086122, "解决三位数乘两位数(因数中无0)的实际问题": 0.00010371077140071768, "三位数乘两位数口算解决实际问题": 0.00010371077140071768, "口算几百几十数除以整十数": 8.296861712057415e-05, "口算除数是整十数的除法": 2.0742154280143537e-05, "用除数是整十数的除法解决简单的实际问题": 4.1484308560287074e-05, "口算整百数除以整十数": 4.1484308560287074e-05, "口算整十数除以整十数": 8.296861712057415e-05, "几百几十数除以整十数的笔算方法": 1e-06, "两位数除以整十数的笔算除法": 8.296861712057415e-05, "三位数除以整十数的笔算除法(商是两位数)": 1e-06, "三位数除以整十数商是两位数的笔算除法": 1e-06, "三位数除以整十数商是一位数的笔算除法": 4.1484308560287074e-05, "用三位数除以整十数解决简单的实际问题": 0.00014519507996100476, "判断商是几位数(除数是整十数)": 1e-06, "被除数的最值问题(三位数除以两位数的有余数除法)": 1e-06, "除数是两位数除法的实际应用": 1e-06, "三位数除以两位数(商是两位数)的笔算除法": 0.0001659372342411483, "三位数除以两位数的笔算": 4.1484308560287074e-05, "商的个位是0的笔算除法": 1e-06, "用三位数除以两位数(商是两位数)解决简单的实际问题": 0.0009748812511667462, "判断商是几位数(除数是两位数)": 0.0003318744684822966, "除数不接近整十数的试商": 0.00012445292568086122, "除数接近整十数的笔算除法解决问题(用\"四舍\"法试商)": 1e-06, "除数接近整十数的笔算除法解决问题(用\"五入\"法试商)": 1e-06, "除数接近整十数的笔算方法(用\"四舍\"法试商)": 1e-06, "除数接近整十数的笔算方法(用\"五入\"法试商)": 1e-06, "试商问题综合(商是一位数)": 0.00010371077140071768, "两位数除以两位数-试商": 1e-06, "三位数除以两位数(商是一位数)-试商": 1e-06, "被除数和除数末尾都有0的除法": 1e-06, "用连除解决实际问题": 0.004998859181514592, "用连除的知识简算(除数是两位数)": 6.222646284043061e-05, "用乘除混合解决实际问题": 0.0013689821824894733, "除加、除减(表内)": 1e-06, "除加除减应用题(表内混合运算)": 0.0010993341768476073, "根据分步运算列综合算式(含括号)(表内混合运算)": 0.0003318744684822966, "优惠策略": 0.0006430067826844496, "含有小括号的四则混合运算": 0.00041484308560287074, "够不够问题": 0.00020742154280143537, "含有括号的小数混合运算": 0.00010371077140071768, "看图列综合算式并计算": 0.00020742154280143537, "用两步计算解决问题(表内混合运算)(不含括号)": 1e-06, "求中间量比多少": 1e-06, "小括号对运算结果的影响": 4.1484308560287074e-05, "用含有括号的四则混合运算解决常见数学问题": 0.0007882018626454544, "含有中括号的四则混合运算": 0.00018667938852129183, "四则运算中的错中求解": 0.0003318744684822966, "有括号的四则混合运算": 0.0009541390968866026, "将分步算式改写成带小括号或中括号的综合算式": 0.0015971458795710522, "24点游戏": 0.00035261662276244013, "估算(100以内)": 0.0001659372342411483, "估算解决问题(100以内)": 0.00010371077140071768, "估算够不够问题(整百、整千数加减)": 0.00024890585136172244, "几百几十数加、减几百几十数的估算计算": 0.0007259753998050237, "整十、整百和整千数的加减法估算": 0.00018667938852129183, "用整百数或几百几十数的估算解决问题": 0.000580780319844019, "两、三位数乘一位数的估算": 0.0002903901599220095, "两、三位数乘一位数的估算应用": 0.0003318744684822966, "运用估算判断积的可能性": 0.00014519507996100476, "两位数乘两位数的估算": 0.005849287507000477, "运用推理法解决有关两位数乘两位数的填数问题": 0.0006637489369645932, "部分估计整体法估算": 1e-06, "基准数法估算": 1e-06, "三位数乘两位数的估算": 0.000580780319844019, "除数是一位数的估算": 0.006056709049801912, "运用除数是一位数的除法估算解决问题": 0.0019290203480533488, "两位数除两位数的估算方法": 8.296861712057415e-05, "两位数除三位数的估算方法": 0.00010371077140071768, "小数的不进位加法和不退位减法(一位小数)": 1e-06, "一位小数加法的计算(不进位)": 0.0006015224741241625, "一位小数减法的计算(不退位)": 4.1484308560287074e-05, "一位小数加法的计算(进位)": 0.0010163655597270331, "一位小数减法的计算(退位)": 4.1484308560287074e-05, "小数加法(小数部分位数不同)": 8.296861712057415e-05, "小数加法(小数部分位数相同)": 0.0002281636970815789, "小数减法(小数部分位数不同)": 0.0001659372342411483, "小数减法(小数部分位数相同)": 0.00010371077140071768, "利用小数加减法解决综合问题": 0.0033602289933832527, "小数连加应用题": 0.00037335877704258366, "小数连减应用题": 0.000539296011283732, "运用一位小数加法解决问题": 0.0017630831138122005, "运用一位小数减法解决问题": 0.0013274978739291864, "包含整数的小数加减法": 0.00018667938852129183, "小数的加减混合运算": 0.0015556615710107652, "小数加减法简算综合": 0.0020742154280143535, "小数加、减法解决较复杂的实际问题（一位小数）": 0.0006844910912447367, "小数加减法应用题": 0.005600381655638755, "用计算器计算小数加、减法": 0.00024890585136172244, "小数乘整数的实际应用": 0.0006844910912447367, "小数乘整数计算": 0.00037335877704258366, "应用小数乘整数进行单位换算": 8.296861712057415e-05, "用逆推法解决小数乘整数应用题": 4.1484308560287074e-05, "小数乘法中倍的应用": 0.0002903901599220095, "小数乘小数的计算-": 1e-06, "小数乘小数的算理、算法": 0.00014519507996100476, "小数乘小数解决实际问题": 0.0002903901599220095, "小数点的位置（小数乘小数）": 1e-06, "积与因数的大小关系（小数乘法）": 0.0003941009313227272, "根据积的近似值反求原数": 0.00018667938852129183, "积的变化规律（小数乘小数）-": 1e-06, "小数除法商中间有0": 1e-06, "小数的整数部分能被整除": 1e-06, "整数除以整数商是小数的除法9": 2.0742154280143537e-05, "除数是整数的小数除法实际问题9": 0.00037335877704258366, "小数点向左移动与小数的大小变化(小数除法)": 4.1484308560287074e-05, "小数运算解决和差倍问题": 0.0001659372342411483, "除数是小数的基本算理及算法": 6.222646284043061e-05, "判断商与1的大小关系（除数是小数）": 6.222646284043061e-05, "有余数的小数除法（除数是小数）": 1e-06, "除数是小数的小数除法实际问题": 0.00035261662276244013, "用\"四舍五入\"法求小数的近似数": 1e-06, "用\"四舍五入\"法取积的近似值（小数乘法）": 1e-06, "用\"四舍五入\"法取积的近似值的实际应用（小数乘法）": 1e-06, "商的近似数末尾有0的处理方法（小数除法）": 1e-06, "用\"四舍五入\"法取商的近似值（小数除法）": 1e-06, "用\"四舍五入\"法取商的近似值实际应用（小数除法）": 1e-06, "用“四舍五入”法解决极值问题（小数除法）": 1e-06, "进一法（小数）": 0.0004563273941631578, "进一法应用题": 0.0012860135653688992, "进一、去尾解决问题": 0.001161560639688038, "去尾法（小数）": 0.0001659372342411483, "去尾法应用题": 0.00170085665097177, "商与被除数的大小关系（除数是小数）": 0.00024890585136172244, "小数连除": 0.00014519507996100476, "小数混合运算之错中求解": 1e-06, "错中求解（小数乘小数）": 1e-06, "估算解决实际问题（小数乘法）": 1e-06, "小数乘小数的估算-": 1e-06, "小数乘加、乘减混合运算": 6.222646284043061e-05, "小数四则混合运算顺序": 0.00024890585136172244, "小数除加、除减混合运算": 4.1484308560287074e-05, "带中括号的四则混合运算的应用": 1e-06, "小数混合运算解决问题(不含除法)": 0.0001659372342411483, "混合运算解决实际问题（含小数除法）": 1e-06, "1减几分之几的计算": 0.00024890585136172244, "同分母分数加法的含义及计算方法": 0.001161560639688038, "同分母分数加、减法": 0.004086204393188277, "同分母分数减法的含义及计算方法": 0.0008089440169255979, "同分母分数连加计算": 0.0004563273941631578, "同分母分数连减计算": 0.00024890585136172244, "1减几分之几的应用": 0.0003318744684822966, "同分母分数加法在实际生活的应用": 0.0008296861712057415, "同分母分数加、减法的应用": 0.002281636970815789, "运用假设法解决填分数算式的问题": 1e-06, "异分母分数加法的计算方法": 0.0012652714110887556, "异分母分数减法的计算方法": 0.0004770695484433013, "解决问题(一半的一半应用题)": 0.002592769285017942, "异分母分数减法在实际生活的应用": 0.0014312086453299039, "分数加减混合运算": 0.0013689821824894733, "同分母分数加减混合运算": 1e-06, "同分母分数连加的计算方法": 1e-06, "同分母分数连减的计算方法": 2.0742154280143537e-05, "分数加、减混合运算解决问题": 0.0019912468108937795, "转化法": 2.0742154280143537e-05, "利用分数墙计算同分母分数加减法": 2.0742154280143537e-05, "求一个数比另一个数多(或少)几分之几": 0.00024890585136172244, "求几分之几": 1e-06, "求一个数是另一个数的几分之几": 0.0046877268673124394, "求一个数是另一个数的几分之几的实际应用": 0.0008089440169255979, "通过转换单位\"1\"求一个数是另一个数的几分之几": 1e-06, "分数乘整数的计算": 0.0002281636970815789, "分数乘整数的意义": 0.00014519507996100476, "整数乘分数的实际应用": 6.222646284043061e-05, "整数乘分数的意义": 1e-06, "分数乘分数的计算": 0.00020742154280143537, "分数乘分数的意义": 0.00020742154280143537, "分数乘分数的运用": 8.296861712057415e-05, "分数乘小数的计算": 0.00024890585136172244, "分数连乘的计算": 0.00014519507996100476, "分数乘法中因数与积的大小关系": 0.00018667938852129183, "分数乘分数的比较大小": 0.00035261662276244013, "分数与整数相乘比较大小": 1e-06, "利用整体的几分之几解决问题": 1e-06, "简单求一个数的几分之几是多少": 0.0007467175540851673, "理解一个数的几分之几是多少(分数乘法)": 1e-06, "稍复杂的求一个数的几分之几是多少": 0.0003318744684822966, "通过统一单位\"1\"求一个数的几分之几是多少": 1e-06, "连续求一个数的几分之几是多少": 0.0003941009313227272, "解决商品提价、降价问题": 8.296861712057415e-05, "解决增减幅度一致的问题": 1e-06, "求比一个数多/少几分之几的实际应用": 1e-06, "求比一个数多/少几分之几的部分": 2.0742154280143537e-05, "分数除以整数的意义": 0.00018667938852129183, "分数除以整数的计算": 0.00014519507996100476, "分数除以整数的应用": 0.0002903901599220095, "分数与除法": 6.222646284043061e-05, "一个数除以分数的计算": 0.00035261662276244013, "一个数除以分数的简单应用及错中求解": 2.0742154280143537e-05, "一个数除以分数的意义": 4.1484308560287074e-05, "一个数除以分数的应用": 0.0004355852398830143, "分数连除": 6.222646284043061e-05, "商与被除数的大小关系（分数除法）": 0.0005185538570035884, "已知一个数的几分之几是多少，求这个数（考法需整合）": 0.0004355852398830143, "已知比一个数多(少)几分之几的数是多少，求这个数": 0.0006222646284043061, "分数乘除混合运算": 0.0002281636970815789, "分数乘除混合运算的应用题": 0.0002903901599220095, "分数混合运算(不含分数除法)": 0.0001659372342411483, "分数加减乘混合运算的应用题": 0.0004770695484433013, "巧用分数乘法运算律进行简便计算": 0.00024890585136172244, "分数四则混合运算": 0.000580780319844019, "稍复杂的分数乘法解决问题": 1e-06, "四则运算的意义和计算方法": 0.0009126547883263156, "四则运算之间的关系": 1e-06, "百分数四则混合运算": 0.00031113231420215305, "运用百分比的知识填写表格": 1e-06, "求百分率": 0.001078592022567464, "求一个数是另一个数的百分之几": 0.000539296011283732, "求一个数比另一个数多/少百分之几": 0.0019082781937732052, "求一个数的百分之几是多少": 0.0008711704797660285, "解决商品提价、降价问题(百分数)": 0.00020742154280143537, "解决增减幅度一致的问题(百分数)": 4.1484308560287074e-05, "求比一个数多(或少)百分之几的数是多少": 0.00031113231420215305, "已知一个数的百分之几是多少，求这个数": 0.0009126547883263156, "已知比一个数多(或少)百分之几的数是多少，求这个数": 0.00014519507996100476, "已知原价和折扣，求便宜的钱数": 0.0005600381655638754, "折扣的意义": 0.000850428325485885, "已知原价和折扣，求现价": 0.0005600381655638754, "已知现价和折扣，求原价": 0.0006015224741241625, "利润和折扣综合求折扣": 0.0001659372342411483, "求税率": 0.00014519507996100476, "本金、利息、利率的含义": 0.0003941009313227272, "存款利息-": 0.0010371077140071767, "求利率-": 0.00024890585136172244, "求本金": 8.296861712057415e-05, "复利问题计算": 0.0002903901599220095, "求成数": 0.000539296011283732, "增/减几成的数-": 0.0001659372342411483, "求原来的量-": 0.00035261662276244013, "已知成本和利润率，求售价": 1e-06, "已知成本和售价，求利润率": 2.0742154280143537e-05, "已知售价和利润率，求成本": 8.296861712057415e-05, "用假设法解决涨幅问题": 1e-06, "折扣和成数综合": 6.222646284043061e-05, "加减法算式性质": 1e-06, "加减互逆关系(1-5)": 1e-06, "加、减法各部分间的关系的计算与运用": 0.0010578498682873203, "加法的意义": 0.0007882018626454544, "加法各部分之间的关系": 0.0008089440169255979, "加减互逆关系(20以内退位)": 1e-06, "加减互逆关系(6-10)": 1e-06, "减法的意义": 0.0007882018626454544, "减法各部分之间的关系": 0.00035261662276244013, "枚举列算式": 1e-06, "认识加减法各部分名称": 2.0742154280143537e-05, "想加算减(十几减8)": 2.0742154280143537e-05, "想加算减(十几减9)": 1e-06, "乘、除法各部分间的关系的最值问题": 0.00012445292568086122, "乘法的意义": 0.0010371077140071767, "乘法各部分之间的关系": 0.0004770695484433013, "除法的意义": 0.0008711704797660285, "除法各部分之间的关系(无余数)": 0.00041484308560287074, "除法各部分之间的关系(有余数)": 0.0006637489369645932, "根据商的取值确定一位数除数（删）": 1e-06, "根据算式各部分之间的关系解决填数问题": 1e-06, "有关0的运算": 0.00410694654746842, "不含括号的表内混合运算": 0.0003318744684822966, "不含括号的四则混合运算的运算顺序": 0.00041484308560287074, "含有两级运算的运算顺序": 0.0002903901599220095, "同级运算的运算顺序": 0.00031113231420215305, "不含括号的四则混合运算": 0.00018667938852129183, "用不含括号的三步混合运算解决实际问题": 0.0005185538570035884, "含有小括号的表内混合运算的运算顺序": 0.00035261662276244013, "含有中括号的四则混合运算的运算顺序": 0.0004770695484433013, "加减法应用(小括号)": 1e-06, "加小括号的综合问题(表内混合运算)": 0.0003318744684822966, "加小括号改变运算顺序问题": 8.296861712057415e-05, "加小括号或中括号改变运算顺序问题": 0.0009333969426064591, "加小括号问题综合": 0.00041484308560287074, "口算(小括号)": 0.0001659372342411483, "加法交换律": 0.0014104664910497605, "加法交换律和结合律的综合运用": 0.0019290203480533488, "加法结合律": 0.0006844910912447367, "运用加法运算定律解决实际问题": 0.0014519507996100475, "减法的运算性质": 0.001078592022567464, "连减巧算": 1e-06, "运用减法的运算性质解决实际问题": 0.0012860135653688992, "运用减法的运算性质简算": 0.0015971458795710522, "乘法交换律": 0.0004770695484433013, "乘法交换律和结合律的综合运用": 0.0010993341768476073, "乘法结合律": 0.0007052332455248803, "乘法分配律": 0.0016801144966916263, "乘法分配律的逆运算": 0.0004355852398830143, "乘法分配律的应用": 0.0007467175540851673, "运用除法的运算性质进行简算": 0.0004978117027234449, "除法的运算性质": 0.0009126547883263156, "小数连加简算": 0.0010163655597270331, "小数连减简算": 0.0006637489369645932, "分子是1的异分母分数加法的简便算法": 0.0004978117027234449, "分子是1的异分母分数减法的简便算法": 0.00010371077140071768, "整数加法运算定律推广到分数": 0.0023438634336562197, "分数提取公因数": 0.00014519507996100476, "应用分数乘法分配律进行简便计算": 0.0001659372342411483, "综合应用分数乘法交换、结合律进行简便计算": 8.296861712057415e-05, "运算律与简便运算": 6.222646284043061e-05, "百分数的简便运算": 1e-06, "运用乘法交换律和结合律简算": 0.0007052332455248803, "运用乘法分配律解决实际问题": 0.0026964800564186595, "运用加法运算定律简算": 0.0016801144966916263, "求一个数是另一个数的几倍": 0.001514177262450478, "倍数应用题(表内)": 1e-06, "根据倍数关系画图": 1e-06, "看图求倍数(表内)": 8.296861712057415e-05, "看图求倍数(表内2-5)": 1e-06, "求倍数应用题(2~5)": 1e-06, "求一个数的几倍是多少": 0.0010163655597270331, "看图求多倍量(2~5)": 2.0742154280143537e-05, "求多倍量应用题(2-5)": 1e-06, "有关增加或减少的倍数问题": 0.0002903901599220095, "已知一个数的几倍是多少，求这个数(2-5)": 1e-06, "已知一个数的几倍是多少，求这个数": 0.00037335877704258366, "差相等的减法算式": 2.0742154280143537e-05, "根据和的变化规律比大小": 1e-06, "和不变规律(1-9)": 1e-06, "和不变规律(20以内进位加)": 1e-06, "两个加数一增一减(100以内)": 1e-06, "积不变的规律": 4.1484308560287074e-05, "积不变的规律的应用": 0.00018667938852129183, "两个因数都变化的规律(积变化的规律)": 0.00012445292568086122, "两个因数都变化的规律的应用(积变化的规律)": 4.1484308560287074e-05, "一个因数变化的规律": 0.0001659372342411483, "一个因数变化的规律的应用": 1e-06, "运用积的变化规律解决图形问题": 0.00018667938852129183, "运用积的变化规律解决问题": 6.222646284043061e-05, "解决文字叙述问题(小数点移动规律)": 0.00170085665097177, "商不变的规律": 0.00035261662276244013, "商的变化规律": 0.0018875360394930618, "商的变化规律的实际应用": 1e-06, "商的变化规律在小数除法中的应用": 2.0742154280143537e-05, "应用商不变的规律进行简便计算": 8.296861712057415e-05, "运用商不变的规律解决实际问题": 2.0742154280143537e-05, "商不变规律中余数的变化": 0.0001659372342411483, "商的变化规律（小数除法）": 0.00020742154280143537, "算盘的应用(大数)": 6.222646284043061e-05, "算盘表示数(1000以内)": 0.00037335877704258366, "算盘上拨数(1000以内)": 0.00020742154280143537, "计算器按键损坏问题": 6.222646284043061e-05, "使用计算器计算": 1e-06, "组数求积的最大值或最小值": 1e-06, "奇怪的142857": 1e-06, "神奇的9": 0.00010371077140071768, "用计算器计算小数乘除法": 0.0001659372342411483, "用计算器探索规律": 6.222646284043061e-05, "有趣的\"回文数\"": 1e-06, "用字母表示单价、数量、总价间的关系": 0.00014519507996100476, "用字母表示工效、工时、工总间的关系": 2.0742154280143537e-05, "用字母表示数量关系(ax±bx)": 0.00035261662276244013, "用字母表示数量关系(a±bx)或(ax±b)": 0.001078592022567464, "用字母表示数量关系ax或x÷a或a÷x": 0.0007259753998050237, "用字母表示数量关系a±x或x±a": 0.0007052332455248803, "用字母表示速度、时间、路程间的关系": 0.00024890585136172244, "用字母表示计算公式": 0.0004770695484433013, "用字母表示运算律": 8.296861712057415e-05, "用字母关系式总结数字、图形变化规律9": 0.00010371077140071768, "等量代换在算式计算和实际问题中的运用-": 4.1484308560287074e-05, "天平上的等量代换-": 6.222646284043061e-05, "含两个对象等量代换求质量": 6.222646284043061e-05, "含三个对象等量代换求质量": 0.0001659372342411483, "简单的天平代换": 8.296861712057415e-05, "图形中的等量代换": 0.00020742154280143537, "方程的意义": 0.0006844910912447367, "运用自然数间的关系列方程": 2.0742154280143537e-05, "等式的性质1": 0.0003318744684822966, "等式的性质2": 0.0001659372342411483, "解方程：x±a=b这种类型": 0.00035261662276244013, "解方程：ax=b这种类型(a≠0)": 0.00037335877704258366, "解方程：a-x=b这种类型": 0.00010371077140071768, "解方程：ax±b=c这种类型(a≠0)": 0.0005185538570035884, "用数与形探索规律-": 2.0742154280143537e-05, "用字母表示立体图形中的量-": 0.00010371077140071768, "与比有关的工程问题-": 0.00010371077140071768, "方中圆问题（一方一圆）-": 0.00010371077140071768, "圆中方问题（一圆一方）-": 2.0742154280143537e-05, "画扇形-": 2.0742154280143537e-05, "工程中的百分数问题": 2.0742154280143537e-05, "列方程解决班级人数变化问题-": 2.0742154280143537e-05, "列方程解决稍复杂实际问题-": 2.0742154280143537e-05, "与量的交换、添加、减少有关的求比问题-": 4.1484308560287074e-05, "求溶液问题中的比-": 2.0742154280143537e-05, "求利润-": 0.00012445292568086122, "求量之比（简单实际问题）-": 8.296861712057415e-05, "搭配路线": 0.000539296011283732, "数阵图（一位小数）": 4.1484308560287074e-05, "数对、位置与简单行程问题": 4.1484308560287074e-05, "知盈利亏损情况，求成本-": 2.0742154280143537e-05, "班级人数变化的百分数问题-": 2.0742154280143537e-05, "用方程法解分数乘法应用题": 6.222646284043061e-05, "求总量的实际问题（含比）-": 0.00010371077140071768, "知比原价便宜的钱数，求原价-": 2.0742154280143537e-05, "推理法求真分数-": 0.0001659372342411483, "组合体中添/去掉小正方体的摆法或位置问题（三）": 4.1484308560287074e-05, "已知部分量占总量的分率，求总量-": 4.1484308560287074e-05, "百分数有关的求总量问题-": 4.1484308560287074e-05, "方格图中的数对表示位置-": 8.296861712057415e-05, "方格图中的数对找位置-": 4.1484308560287074e-05, "估算的运用（小数乘法）-": 1e-06, "与百分数有关的补充条件/问题": 8.296861712057415e-05, "梯形面积公式正用-": 1e-06, "梯形面积公式逆用-": 4.1484308560287074e-05, "用字母表示数量关系（a±b）÷X或a÷x±b÷x": 4.1484308560287074e-05, "方程解有关的问题-": 4.1484308560287074e-05, "看图列方程-": 2.0742154280143537e-05, "用比例方程解决行程问题-": 2.0742154280143537e-05, "用比例方程解决工程问题-": 1e-06, "化简比（百分数）": 1e-06, "由实际问题，找两量的正确图线-": 6.222646284043061e-05, "由积木上数字绘制几何体的平面图形（三）": 2.0742154280143537e-05, "复式统计表的信息提取": 0.0009126547883263156, "分数连乘的实际应用-": 0.00012445292568086122, "排水法求物体体积（单个物体）-": 1e-06, "排水法求物体体积（多个物体）-": 2.0742154280143537e-05, "可能性大小的计算-": 0.00012445292568086122, "扇形统计图中获取信息计算-": 0.00018667938852129183, "等差数列计算-": 2.0742154280143537e-05, "根据可能性大小反求个数-": 6.222646284043061e-05, "用小棒摆立体图形-": 2.0742154280143537e-05, "五日游中路程时间速度问题-": 2.0742154280143537e-05, "五日游中的费用问题-": 0.00010371077140071768, "含扇形的组合图形中阴影部分周长/面积-": 2.0742154280143537e-05, "三个量的鸡兔同笼问题": 0.00018667938852129183, "根据运动情况绘制示意图-": 2.0742154280143537e-05, "画图法解鸡兔同笼": 6.222646284043061e-05, "梯形与三角形面积问题-": 2.0742154280143537e-05, "平面图形转动中的顶点轨迹问题-": 1e-06, "容器中含物的注水问题（含圆锥）-": 1e-06, "容器中含物的注水问题（含圆柱）-": 1e-06, "根据方向，角度，距离在坐标图中找位置/绘制点-": 1e-06, "图形运动中的面积问题（含圆）-": 1e-06, "推理可能性最大（小）的事件/结果/数字...": 1e-06, "由结果可能性大小反求原来组成情况-": 1e-06, "运行图中的行程问题（复式折线图）-": 1e-06, "图形/符号/物品间的运算-": 1e-06, "组合平面图形中的比-": 1e-06, "列方程解决实际问题ax=bc(a≠0)-": 1e-06, "知部分量，求总量-": 1e-06, "质数、合数有关的数字推理问题-": 1e-06, "质数有关的数字推理问题-": 1e-06, "合数有关的数字推理问题-": 1e-06, "质数有关的实际应用题-": 1e-06, "合数有关的实际应用题-": 1e-06, "晴/雨天问题": 1e-06, "2倍数的运用-": 1e-06, "5倍数的运用-": 1e-06, "2、5倍数的运用-": 1e-06, "偶数的运用-": 1e-06, "奇数的运用-": 1e-06, "3倍数的运用-": 1e-06, "质数与合数的认识-": 1e-06, "方向与路程的综合问题（四个方向）": 1e-06, "其他倍数的运用-": 1e-06, "价格中的负数": 1e-06, "求现在的量-": 1e-06, "圆柱容积的应用-": 1e-06, "几何体中削圆锥（求高/底面量）-": 1e-06, "成数先减少后增加-": 1e-06, "圆柱侧面积逆用-": 1e-06, "含圆锥的组合体的体积（挖去型）-": 1e-06, "利润和成数综合求成本-": 1e-06, "正负数的计算": 1e-06, "周期问题（两位数除以一位数）": 1e-06, "折扣与百分率-": 1e-06, "图形的份数与乘除法运算": 1e-06, "两种（及以上）物品搭配组合（除数是一位数的口算）": 1e-06, "两种（及以上）物品搭配组合（除数是一位数的笔算）": 1e-06, "成数先增加后减少-": 1e-06, "求应纳税部分-": 1e-06, "天平称重": 0.0002281636970815789, "一般油桶问题": 1e-06, "结合平面图形的特点推断图形": 1e-06, "其他推理问题": 1e-06, "由高的增/减，求侧面积-": 1e-06, "长/正方形旋转成圆柱问题（求表面积）-": 1e-06, "圆柱表面积逆用-": 1e-06, "看图列式（十几减7、6）": 1e-06, "看图列式（十几减8）": 1e-06, "十几减7、6的计算方法": 1e-06, "圆柱切拼成近似长方体的表面积": 1e-06, "数独问题": 1e-06, "求圆柱的容积-": 1e-06, "两圆柱间与体积有关的计算-": 1e-06, "由圆柱侧面展开图求体积-": 1e-06, "由高和表面积增/减变化求体积问题": 1e-06, "1000以内数的综合应用": 1e-06, "认识\"一千\"": 1e-06, "圆柱体积（容积）实际应用（容球、大分小、图象、卷纸）": 1e-06, "两容器的水中浸物问题-": 1e-06, "圆锥上的最短路线": 1e-06, "圆锥体积有关的实际问题（图象、空余）": 1e-06, "10000以内数的综合应用": 1e-06, "水中浸物问题（圆柱中放圆锥）-": 1e-06, "圆柱与圆锥关系（求体积/体积关系）-": 1e-06, "圆柱与圆锥关系（求高/高的关系）-": 1e-06, "圆柱与圆锥关系（求底面积/底面积关系）-": 1e-06, "等积变形（圆锥与圆柱间的倒水问题）-": 1e-06, "削成最大的圆锥（圆柱削圆锥）-": 1e-06, "削成最大的圆锥（正方体削圆锥）-": 1e-06, "削成最大的圆锥（长方体削圆锥）-": 1e-06, "比例的意义（写比例、写比）": 1e-06, "组成比例（线段图、知比值、补最大数）": 1e-06, "图与比例(阴影、两图间、数变化)": 1e-06, "用比例基本性质推理求值（解比例）-": 1e-06, "解比例的实际应用（补全、次数、总量）": 1e-06, "笔算加法的综合应用（两位数加两位数/整十数/一位数）": 1e-06, "笔算减法的综合运用（两位数减两位数/一位数）": 1e-06, "口算减法的综合应用（两位数减两位数/一位数/整十数）": 1e-06, "解比例在立体图形中的应用-": 1e-06, "求图上周长、面积问题-": 1e-06, "比例尺有关的综合应用题（结合工程）-": 1e-06, "拆长方体纸盒": 1e-06, "正比例的应用（弹簧、蜡烛、浓度、百米、黄金比）": 1e-06, "营养午餐": 1e-06, "正比例在行程中的应用（油表、数线图、到时时刻）": 1e-06, "反比例的应用（植树、天平、图形、水上升）": 1e-06, "反比例解决自行车运动问题（公式、齿轮转动圈数，一圈距离）": 1e-06, "反比例解决自行车运动问题（结构、全程、齿数比、圈数比）": 1e-06, "反比例解决变速自行车运动问题（速度种类、最远组合、选组合）": 1e-06, "反比例解决变速自行车运动问题（全程、蹬的圈数，不同齿数比）": 1e-06, "反比例解决自行车运动问题（半径比、比转圈、车轮转动圈数）": 1e-06, "比较液体多少": 1e-06, "行列与数对": 1e-06, "等量代换（5以内）": 1e-06, "数轴上表示数(亿以内)": 1e-06, "亿以内数的综合应用": 1e-06, "分数乘整数的实际应用": 1e-06, "一个数与分数乘法的错中求解": 1e-06, "整数乘分数的应用": 1e-06, "整数乘分数的计算": 1e-06, "运用假设法和分类讨论法解决分数乘法问题": 1e-06, "分数乘小数的应用": 1e-06, "分数乘小数的实际问题": 1e-06, "改正错误并正确读写(亿以上)": 1e-06, "2、5、3倍数的运用（二）": 1e-06, "2、5、3倍数的运用（三）": 1e-06, "质合综合运用（分类、最值）": 1e-06, "质合综合运用（猜数、错中求、程序算）": 1e-06, "分数乘分数的实际应用": 1e-06, "偶数的认识（与2倍数关系、按要求写偶数）": 1e-06, "奇数的认识（找奇数、与2倍数关系）": 1e-06, "循环小数在计算中的运用-": 1e-06, "小数除法估算的实际应用-": 1e-06, "掷骰子中的相关问题-": 1e-06, "整数乘法运算律在分数乘法中的应用": 1e-06, "整数乘法运算律在分数乘法中的实际应用": 1e-06, "含亿级数位顺序表的应用": 1e-06, "亿以上数的综合应用": 1e-06, "用数对、方向和距离确定位置": 1e-06, "应用位置和方向及行程问题的知识解决实际问题": 1e-06, "商不变的规律（分数除法）": 1e-06, "分数四则混合运算的运用": 1e-06, "一个数是另一个数的几分之几/几倍（分数除法）": 1e-06, "分数四则混合运算的实际应用": 1e-06, "小数与整数连乘的实际应用-": 1e-06, "解决含有两个单位“1”的问题（分数除法）": 1e-06, "小数与小数连乘的实际应用-": 1e-06, "看线段图列式（分数除法）": 1e-06, "找对应量和对应分率，求单位“1”的问题": 1e-06, "积不变的性质（小数乘小数）-": 1e-06, "数轴上表示数(亿以上)": 1e-06, "通过近似数反推原数(亿以内)": 1e-06, "定义新运算（ 分数除法）": 1e-06, "由所列方程写数量关系式-": 1e-06, "列方程解决实际问题ax-ab=c或a(x-b)=c": 1e-06, "应用比的意义解决问题": 1e-06, "三位数乘两位数综合计算": 1e-06, "比值的应用": 1e-06, "其他常见的数量关系的应用(如工程问题)": 1e-06, "与圆的半径、直径有关的计算": 1e-06, "梯形与长/正方形面积问题-": 1e-06, "半圆周长的应用": 1e-06, "计算不规则图形的面积-": 1e-06, "植树问题之贴瓷砖问题-": 1e-06, "认识圆的面积": 1e-06, "圆中半径、直径、周长、面积的关系": 1e-06, "圆环的认识": 0.00035261662276244013, "外方内圆和外圆内方的理解": 1e-06, "组合法计算扇形的面积": 1e-06, "利用整体法（R²－r²）求圆环的面积": 1e-06, "求组合图形的周长": 1e-06, "圆的面积的实际应用": 1e-06, "正方体的展开与折叠": 1e-06, "百分数表示任务进度/折扣等": 1e-06, "看图列式（与百分数有关）": 1e-06, "平行四边形周长相关的计算": 1e-06, "认识等腰直角梯形": 1e-06, "甲比乙多/少几分之几的含义": 1e-06, "画梯形": 1e-06, "解“ax±bx=c”型方程（？）": 1e-06, "百分数混合运算的应用": 1e-06, "由两量之间百分数的关系比较大小": 1e-06, "用方程解决天平上物重问题-": 1e-06, "扇形统计图中的开放性问题": 1e-06, "圆的周长在圆滚动中的应用": 1e-06, "商是一位数的笔算运用": 1e-06, "列方程解决简单的\"行程问题\"": 1e-06, "列方程解决复杂的\"行程问题\"": 1e-06, "商是两位数的笔算运用": 1e-06, "初步认识乘法意义的运用": 1e-06, "5的乘法口诀理解运用": 1e-06, "5的乘法口诀的计算-": 1e-06, "2-4的乘法口诀运用": 1e-06, "2-4的乘法口诀的计算-": 1e-06, "2-5乘加、乘减的理解-": 1e-06, "6的乘法口诀的运用": 1e-06, "6的乘法口诀的计算-": 1e-06, "除数是整十数的计算运用": 1e-06, "2-6的乘法口诀的理解-": 1e-06, "商的变化规律和商的不变规律综合应用": 1e-06, "分一分求份数-": 1e-06, "分一分求每份数-": 1e-06, "分一分求份数、每份数-": 1e-06, "理解平均分-": 1e-06, "分一分完成除法算式（求每份数、份数)-": 1e-06, "分一分完成除法算式（求每份数)-": 1e-06, "分一分完成除法算式（求份数)-": 1e-06, "看图列乘、除法算式-": 1e-06, "2-6乘法口诀运用（列/找/写算式）-": 1e-06, "2-6含除法的计算-": 1e-06, "2-6乘法口诀求值（求份数/每份数）-": 1e-06, "2-6乘法口诀求商的运用-": 1e-06, "除法应用题（2-6）-": 1e-06, "通过连减认识除法-": 1e-06, "除法初步认识的运用-": 1e-06, "方向与生活-": 1e-06, "认识厘米（二）": 1e-06, "曹冲称象的故事与数学思想": 1e-06, "测量身体上的长度-": 1e-06, "身体上的计量单位-": 1e-06, "上课规矩": 1e-06, "有关质量的常识": 1e-06, "看情境图比较大小（1-5）": 1e-06, "吨的应用": 1e-06, "有关0的加减法的应用": 1e-06, "比较大小的应用": 1e-06, "减法计算的应用（1-5）": 1e-06, "6~9的数比大小的应用": 1e-06, "会画缺少的部分（6~9）": 1e-06, "6、7的加法的实际应用": 1e-06, "找规律填数（10以内）": 1e-06, "8、9的加减法的实际应用": 1e-06, "认识10": 1e-06, "10的加减法的实际应用": 1e-06, "连减计算（10以内）": 1e-06, "连加的应用（10以内）": 1e-06, "比较两条线段的长短": 1e-06, "7的乘法口诀-": 1e-06, "计算（7的乘法口诀）-": 1e-06, "7的乘法应用题-": 1e-06, "8的乘法口诀-": 1e-06, "计算（8的乘法口诀）-": 1e-06, "8的乘法应用题-": 1e-06, "9的乘法口诀-": 1e-06, "计算（9的乘法口诀）-": 1e-06, "9的乘法应用-": 1e-06, "表内乘法错中求解-": 1e-06, "7-9乘法口诀求商（计算）-": 1e-06, "7-9乘法口诀求商（列式计算）-": 1e-06, "7-9乘法口诀求商（看图填空）-": 1e-06, "7-9乘法口诀求商（应用题）-": 1e-06, "7-9乘法口诀求商（运用）-": 1e-06, "归总再等分的实际问题（表内）-": 1e-06, "表内混合计算-": 1e-06, "8的乘法口诀运用-": 1e-06, "多种/步计算的应用题（表内）-": 1e-06, "十加几及其对应的减法计算": 1e-06, "十加几及其对应减法的应用": 1e-06, "十几加几的不进位加法的计算": 1e-06, "9加几的运用": 1e-06, "9加几的实际应用": 1e-06, "8、7、6加几的应用": 1e-06, "8、7、6加几的实际应用": 1e-06, "5、4、3、2加几的实际应用": 1e-06, "两种思路求总数": 1e-06, "连加/连减/混合运算的实际应用（20以内不进位不退位）": 1e-06, "5、4、3、2加几的应用": 1e-06, "加法实际应用(1-5)": 1e-06, "小数乘整数的估算-": 1e-06, "多重分类（或层层分类）-": 1e-06, "小数乘小数运用（乘数不全）-": 1e-06, "小数乘法运算律的运用-": 1e-06, "小数乘法与数轴-": 1e-06, "小数乘小数在加油行程中的应用-": 1e-06, "解决比较/够不够的实际问题（小数混合运算不含除法）-": 1e-06, "相遇问题中的应用（小数混合运算不含除法）-": 1e-06, "小数混合运算解决一半逆还原问题（不含除法）-": 1e-06, "小数乘整数中去掉小数点-": 1e-06, "理解小数乘整数-": 1e-06, "小数乘整数的运用（乘数不全）-": 1e-06, "解决折返、对折实际问题（小数乘整数）-": 1e-06, "解决弹簧挂物问题（小数混合运算不含除法）-": 1e-06, "小数点的位置（小数乘整数）-": 1e-06, "解决间隔问题（小数混合运算不含除法）-": 1e-06, "面积、周长有关的问题（小数混合运算不含除法）-": 1e-06, "积的变化规律（小数乘整数）-": 1e-06, "积不变的性质（小数乘整数）-": 1e-06, "组小数计算问题-": 1e-06, "定义新运算（分数乘法）": 1e-06, "按不同标准分类（初阶）-": 1e-06, "分类中的其它问题-": 1e-06, "看线段图列式（分数乘法）": 1e-06, "小数除法与数轴": 1e-06, "追击问题中的应用（小数混合运算不含除法）-": 1e-06, "小数乘加、乘减的实际问题-": 1e-06, "小数乘法运算律解决其它问题-": 1e-06, "小数混合运算解决其它实际问题（不含除法）-": 1e-06, "植树问题之其它问题-": 1e-06, "认识平方千米": 1e-06, "选择合适的面积单位(公顷、平方米)": 1e-06, "估算解决能否完成/参加的问题（小数乘加/乘减）-": 1e-06, "小数乘加、乘减解决其它分段计费问题-": 1e-06, "分数混合运算与几何图形的综合": 1e-06, "除数是整数的小数除法竖式谜": 1e-06, "文字叙述的小数除法（除数是小数）-": 1e-06, "除数是整数的小数除法解决行程问题": 1e-06, "除数是整数的小数除法解决周长/面积问题": 1e-06, "除数是整数的小数除法解决含倍的问题": 1e-06, "除数是小数的小数除法解决含倍的问题-": 1e-06, "除数是小数的小数除法解决行程问题-": 1e-06, "除数是小数的小数除法解决其它问题-": 1e-06, "角的分类(锐角、直角、钝角、平角和周角)": 1e-06, "除数是整数的小数除法解决其它问题": 1e-06, "小数乘除解决实际问题-": 1e-06, "混合运算解决行程问题（含小数除法）": 1e-06, "混合运算解决面积/周长问题（含小数除法）": 1e-06, "混合运算解决其它实际问题（含小数除法）": 1e-06, "用\"四舍五入\"法求平均数的近似数问题（小数除法）": 1e-06, "移多补少解决平均分问题 （小数除法）": 1e-06, "被除数的整数部分不够商1的小数除法-": 1e-06, "倍数有关小数乘法的复杂应用-": 1e-06, "小数乘整数解决比较的实际问题-": 1e-06, "速度、时间、路程的关系应用": 1e-06, "小数乘小数解决比较的实际问题": 1e-06, "含倍的小数乘法在面积/周长中的应用": 1e-06, "积的近似数的实际应用（小数乘整数）-": 1e-06, "积的近似数的实际应用（小数乘小数）-": 1e-06, "平行四边形和梯形的综合应用": 1e-06, "小数乘法分配律": 1e-06, "小数乘法结合律-": 1e-06, "认识单式条形统计图": 1e-06, "横向单式条形统计图": 1e-06, "与分数乘法有关的移多补少问题": 1e-06, "用两位数除以整十数解决简单的实际问题": 1e-06, "除数不接近整十数的笔算除法解决问题": 1e-06, "求比一个数多/少几分之几的应用": 1e-06, "小数乘法交换律-": 1e-06, "分数乘法运算与几何的综合应用（分数乘整数）": 1e-06, "分数乘法运算与几何的综合运用（分数乘分数）": 1e-06, "分数乘法运算与几何的综合运用": 1e-06, "用两步计算解决问题(表内混合运算)(含括号)": 1e-06, "表内混合运算的计算与应用": 1e-06, "求一个数的几分之几的问题（分数乘整数）": 1e-06, "运用排列组合规律解决判断可能性大小问题--": 1e-06, "运用图示法解决复杂可能性问题--": 1e-06, "多位数乘一位数的计算与应用(综合)": 1e-06, "2-4看图列乘法算式-": 1e-06, "6看图列乘法算式-": 1e-06, "解方程：a(x±b)=c这种类型(a≠0)": 0.0001659372342411483, "分数方程解决实际应用": 0.0001659372342411483, "解分数方程": 0.00205347327373421, "用方程法解分数除法应用题": 2.0742154280143537e-05, "比多比少求单位\"1\"(方程法)": 1e-06, "解百分数方程": 0.000539296011283732, "列方程解百分数应用题": 0.00020742154280143537, "列方程解决实际问题ax+ab=c或a(x＋b)=c": 0.00041484308560287074, "列方程解决实际问题ax=b(a≠0)": 0.00035261662276244013, "列方程解决实际问题ax±b=c(a≠0)": 0.0009333969426064591, "列方程解决实际问题x±a=b": 0.00018667938852129183, "列方程解决\"差倍问题\"（ax-bx=c）": 1e-06, "列方程解决\"和倍问题\"（ax+bx=c）": 1e-06, "列方程解决简单的\"年龄问题\"": 1e-06, "列方程解决简单的\"相遇问题\"": 1e-06, "列方程解决稍复杂的\"相遇问题\"": 1e-06, "列方程解决稍复杂的\"追及问题\"": 1e-06, "用工程问题的思想解行程相遇问题": 0.00020742154280143537, "列方程解决稍复杂的\"盈亏问题\"": 1e-06, "等式与方程": 4.1484308560287074e-05, "比的意义": 0.000539296011283732, "比的读写法及各部分的名称": 1e-06, "求比值": 0.0009956234054468898, "比的基本性质": 0.0006430067826844496, "运用比的基本性质解决比值变化的问题": 4.1484308560287074e-05, "运用比的基本性质解决比值不变的问题": 0.000269648005641866, "化简比(带单位)": 0.00018667938852129183, "化简比(分数)": 0.00018667938852129183, "化简比(小数)": 0.00010371077140071768, "化简比(整数)": 0.00018667938852129183, "化连比": 0.00012445292568086122, "整数小数分数化简比": 0.00010371077140071768, "比和分数、除法的关系": 0.00024890585136172244, "求比中的未知项": 4.1484308560287074e-05, "数的性质": 1e-06, "比与百分数": 1e-06, "数之间的联系": 1e-06, "按比分配": 4.1484308560287074e-05, "已知总量，分量比未知，求分量": 8.296861712057415e-05, "已知总量和分量比，求分量": 0.001078592022567464, "与图形相关的按比分配问题": 4.1484308560287074e-05, "不变量解决比的问题": 4.1484308560287074e-05, "根据两数的等量关系求比": 0.0001659372342411483, "判断可能的分量比": 0.0001659372342411483, "认识\"黄金比\"和\"黄金分割\"": 1e-06, "树叶中的比": 1e-06, "树叶中的比的综合应用": 1e-06, "与比有关的行程问题-": 0.0007259753998050237, "已知分量差和分量比，求分量或总量": 0.0003318744684822966, "已知一个分量和分量比，求其它量": 0.0001659372342411483, "与常见图形有关的比和化简比": 0.0006015224741241625, "圆与比/倍数的综合问题": 0.0004563273941631578, "圆的周长比与半径比、直径比的关系": 8.296861712057415e-05, "根据两数的分率关系求比": 4.1484308560287074e-05, "化连比的应用": 1e-06, "比例的意义（补全、判断、改写、辨析）": 1e-06, "比例各部分名称": 0.00035261662276244013, "判定能否组成比例（用比例的意义）": 0.0023023791250959325, "比与比例": 1e-06, "比例的基本性质": 0.006243388438323204, "根据等式写比例": 0.002323121279376076, "判定能否组成比例（用比例的基本性质）": 0.0004770695484433013, "比例中某项变化解决比例仍成立问题": 0.0007259753998050237, "根据反比例关系计算": 4.1484308560287074e-05, "根据正比例关系计算": 0.00024890585136172244, "解比例（概念、计算）": 0.0026964800564186595, "解比例的实际应用（溶液、铺砖、影长）": 1e-06, "正比例的判断": 0.0012445292568086122, "正比例的意义": 0.0008296861712057415, "根据正比例的图象解决问题": 0.001078592022567464, "正比例图象的特点": 0.0007259753998050237, "反比例的判断": 0.0006637489369645932, "反比例的意义": 0.0007467175540851673, "反比例图象的特点": 4.1484308560287074e-05, "正、反比例意义辨析": 0.0003318744684822966, "影子问题": 0.0005185538570035884, "正比例的应用（补全、平面图、切割）": 1e-06, "正比例与反比例": 2.0742154280143537e-05, "反比例的应用（补全题、选算式）": 1e-06, "由反比例图象解决问题": 0.00024890585136172244, "有趣的平衡-": 0.0002281636970815789, "比例尺的定义及分类": 0.0014312086453299039, "数值比例尺（可删）": 1e-06, "线段比例尺（可删）": 1e-06, "线段比例尺与数值比例尺的互化": 0.0008711704797660285, "由实际数据计算比例尺": 0.0029868702163406693, "由比例尺画平面图/找物体位置": 0.0008711704797660285, "由比例尺求实际距离": 0.0023438634336562197, "由比例尺求图上距离": 0.0015764037252909088, "求图形的放大比、缩小比-": 0.0004563273941631578, "求图形放大/缩小后的量（边长/周长/面积/体积）-": 0.0002903901599220095, "图形放大与缩小的理解": 0.0009333969426064591, "在方格纸上按一定的比将图形放大": 0.0006637489369645932, "在方格纸上按一定的比将图形缩小": 0.0006015224741241625, "三角形底、高变化引起的面积变化": 0.00024890585136172244, "求图形放大/缩小后量之比或倍数（边长/周长/面积/体积）-": 0.00018667938852129183, "比例尺有关的路线图问题-": 4.1484308560287074e-05, "认识整时": 6.222646284043061e-05, "认识半时": 1e-06, "画钟表": 2.0742154280143537e-05, "画钟表(几时几分)": 0.00010371077140071768, "认读时间(几时几分)": 0.00041484308560287074, "认识大约几时": 1e-06, "生活中的时间(几时几分)": 0.00010371077140071768, "选正确的钟表": 1e-06, "找规律(几时几分)": 1e-06, "判断时针分针": 1e-06, "认识分": 2.0742154280143537e-05, "时、分、秒时间单位换算(单名数)": 0.00037335877704258366, "时与分的换算": 0.00024890585136172244, "计量秒的关系": 2.0742154280143537e-05, "认识钟面上的指针": 0.00018667938852129183, "体验生活中的时分": 0.00012445292568086122, "比较时间的长短": 0.00020742154280143537, "按时间排序": 1e-06, "时针、分针、秒针的基本走法之间的关系": 0.00012445292568086122, "时、分、秒时间单位换算(单名数与复名数互化)": 0.00020742154280143537, "常见节日认识": 0.0004563273941631578, "年、月、日换算": 0.0008296861712057415, "认识大月与小月": 0.001472692953890191, "认识月份和季度": 0.0006844910912447367, "普通计时法与24时计时法的转化": 0.0018253095766526312, "分段计时解决问题": 8.296861712057415e-05, "计算经过的时间": 0.0004770695484433013, "计算开始或结束时刻": 0.0004770695484433013, "计算经过时间(几时几分)": 0.00010371077140071768, "时间的计算(时分秒)": 8.296861712057415e-05, "解决求等车时间的问题": 4.1484308560287074e-05, "与星期几有关的推算（没有日历）": 0.0006844910912447367, "24时计时法简单应用": 0.0006015224741241625, "24时计算经过的时间": 0.002281636970815789, "根据时间表解决简单的实际问题": 0.00010371077140071768, "认识24时计时法、12时计时法": 0.0004563273941631578, "时间推理(几时几分)": 0.00037335877704258366, "时间排序、推理及比较": 1e-06, "根据已知时间推算过半时后的时间": 1e-06, "根据已知时间推算过几时后的时间": 1e-06, "共同的休息日": 1e-06, "日历中的规律": 0.0004355852398830143, "推理几天前或几天后是星期几": 0.00018667938852129183, "推理昨天或明天是星期几": 1e-06, "日历中的周期问题": 0.00041484308560287074, "认识平年和闰年": 0.0018253095766526312, "选择合适的时间单位(时、分、秒)": 0.00024890585136172244, "人民币单位间的换算": 0.00035261662276244013, "人民币的单位换算(小面额)（删）": 1e-06, "人民币的兑换": 0.0004978117027234449, "人民币的兑换(大面额)（删）": 1e-06, "人民币的兑换(小面额)（删）": 1e-06, "人民币兑换(求积、商的近似数)": 1e-06, "认识1元及1元以下的人民币": 1e-06, "认识小面额人民币": 0.000269648005641866, "加减法应用(人民币)": 0.0006637489369645932, "简单的加、减运算(人民币)": 0.00010371077140071768, "人民币的计算与比较": 0.00018667938852129183, "认识5元及5元以上的人民币": 1e-06, "认识大面额人民币": 8.296861712057415e-05, "认识克": 0.0001659372342411483, "认识千克": 4.1484308560287074e-05, "有关千克的简单应用": 1e-06, "认识质量单位\"吨\"": 1e-06, "千克和克之间的进率及换算": 0.000580780319844019, "质量单位比较大小(吨、千克、克)": 1e-06, "吨、千克、克的计算": 1e-06, "等量秤物品": 1e-06, "解决吨相关的实际问题": 0.0002903901599220095, "吨、千克和克之间的进率及换算": 1e-06, "填质量单位(克和千克)": 0.0006015224741241625, "填合适的质量单位(吨、千克、克)": 1e-06, "千克与克的相关应用题": 0.0007259753998050237, "千克与克的应用": 1e-06, "认识交通标志": 8.296861712057415e-05, "自选长度单位的测量": 2.0742154280143537e-05, "量与计量": 1e-06, "比大小(米和厘米)": 6.222646284043061e-05, "多个量比大小(人民币)": 1e-06, "分米、厘米和毫米之间的比较大小": 8.296861712057415e-05, "面积单位比较大小": 1e-06, "千米和米的计算": 0.00018667938852129183, "人民币的大小比较": 1e-06, "根据条件提出问题并解答": 0.0003318744684822966, "提出问题并列算式(20以内进位)": 2.0742154280143537e-05, "先补充条件，再解答": 0.00010371077140071768, "计算": 0.008110182323536122, "看图列减法算式(6-10)": 1e-06, "看图列减法算式(整体与部分)": 4.1484308560287074e-05, "看图列式": 0.001783825268092344, "看图列式(20以内进位加)": 1e-06, "求一共（20以内）": 0.0001659372342411483, "求原来": 0.0007052332455248803, "求原来(20以内)": 2.0742154280143537e-05, "图文算式(1-5)": 1e-06, "图文算式(20以内)": 2.0742154280143537e-05, "图文算式(表内)": 2.0742154280143537e-05, "加法计算(6~9)": 1e-06, "减法计算(6~9)": 1e-06, "图文算式(9以内)": 1e-06, "归一问题（100以内）": 1e-06, "减法解决归一问题": 1e-06, "运用数形结合法解决简单的归一问题": 6.222646284043061e-05, "加法解决归总问题": 1e-06, "分数除法之和倍、差倍问题": 0.00014519507996100476, "解决多个数是同一个数的倍数的问题": 0.00018667938852129183, "半价票问题": 1e-06, "促销方案选择问题（多个方案）": 0.0012445292568086122, "单价、数量、总价的关系应用": 1e-06, "单价、数量、总价的数量关系": 0.00014519507996100476, "根据公式求单价": 4.1484308560287074e-05, "根据公式求数量": 1e-06, "根据公式求总价": 0.00024890585136172244, "购买两物问题": 1e-06, "购物中的买几送几问题": 0.0001659372342411483, "购物中的满减问题": 4.1484308560287074e-05, "经济问题(表内除法)": 0.0011408184854078945, "优惠方案问题(表内除法)": 0.0005600381655638754, "折扣中的盈利亏损问题": 0.0001659372342411483, "绿色出行": 2.0742154280143537e-05, "根据公式求路程": 0.0004978117027234449, "根据公式求时间": 0.0001659372342411483, "根据公式求速度": 0.00018667938852129183, "解决千米相关的实际问题": 0.00024890585136172244, "速度、时间、路程之间的关系": 0.00035261662276244013, "列方程解决简单的\"追及问题\"": 1e-06, "两人合作工程问题": 0.0005600381655638754, "用工程问题的思想解进出水管问题": 4.1484308560287074e-05, "其它编码": 0.00010371077140071768, "邮政编码": 1e-06, "设计编码": 2.0742154280143537e-05, "身份证编码": 2.0742154280143537e-05, "排列中的组数问题（卡片）": 0.00010371077140071768, "排列中的组数问题": 0.002509800667897368, "排列思想解决其他类型问题": 0.0026549957478583727, "组数与排列(2-3个对象)": 0.00041484308560287074, "方案问题(枚举全部方案)": 0.00020742154280143537, "列表法解决付钱问题": 1e-06, "列表法解决问题": 0.00018667938852129183, "与时间相关的计数": 1e-06, "与算式相关的计数": 1e-06, "运用列表法解决购物问题": 1e-06, "运用列表法解决实际问题": 1e-06, "运用列表法解决行程问题": 1e-06, "搭配(2种物品)": 0.0001659372342411483, "搭配综合问题": 4.1484308560287074e-05, "两数求和(100以內)": 6.222646284043061e-05, "取币值": 0.00010371077140071768, "握手问题": 0.00014519507996100476, "实际情境中的搭配": 0.0022401526622555018, "组合思想解决问题": 0.003132065296301674, "比赛问题": 0.00018667938852129183, "体育比赛单循环赛的场数问题": 6.222646284043061e-05, "用体育比赛策略解决其他问题": 1e-06, "比较型推理问题(三量)": 0.00014519507996100476, "比较型推理问题(四量)": 1e-06, "比较型推理问题(有数量)": 4.1484308560287074e-05, "九宫数独宫内排除法": 1e-06, "九宫数独唯一数法": 1e-06, "九宫数独行列排除法": 1e-06, "列表法解决推理问题": 0.00014519507996100476, "排除法解决推理问题": 0.0003941009313227272, "四宫数独宫内排除法": 1e-06, "四宫数独唯一数法": 1e-06, "四宫数独行列排除法": 1e-06, "集合概念": 6.222646284043061e-05, "体育比赛中的集合问题": 1e-06, "用韦恩图表示集合": 0.0001659372342411483, "运用集合的知识解决较复杂问题（有圈外部分）": 0.00014519507996100476, "运用集合的知识解决简单问题（无圈外部分）": 0.0005600381655638754, "沏茶问题": 0.0010578498682873203, "合理安排时间": 0.00014519507996100476, "烙饼问题": 0.001161560639688038, "田忌赛马": 0.0003318744684822966, "电话联络问题": 1e-06, "复杂打电话问题": 0.0007674597083653108, "基础打电话问题": 0.0011408184854078945, "方案问题(最优解)": 0.00012445292568086122, "租船/车问题": 0.001659372342411483, "租船问题与省钱方案": 0.0001659372342411483, "买票问题": 0.0010993341768476073, "优惠方案问题(买门票、购物等)": 0.0002281636970815789, "列表法解决鸡兔同笼": 8.296861712057415e-05, "假设法解决鸡兔同笼": 0.0004563273941631578, "\"百僧分馍\"问题": 1e-06, "倒扣型鸡兔同笼": 0.0009333969426064591, "列方程解决\"鸡兔同笼问题\"": 1e-06, "用假设的策略解决倍比关系的实际问题": 1e-06, "用假设的策略解决相差关系的实际问题": 1e-06, "列方程解决简单的\"盈亏问题\"": 1e-06, "植树问题(两端种)": 0.00035261662276244013, "植树问题(两端都不种)": 6.222646284043061e-05, "植树问题(一端种，一端不种)": 0.0001659372342411483, "封闭路线植树问题": 0.0003318744684822966, "用小数乘加、乘减解决分段计费问题": 0.0009956234054468898, "小数运算解决分段计费问题（小数除法）": 0.000269648005641866, "不知次品轻重找次品": 0.00024890585136172244, "已知次品轻/重找次品（1个次品）": 0.004231399473149281, "找规律填数(组数)(20以内)": 1e-06, "数形结合找规律": 8.296861712057415e-05, "完全平方和": 6.222646284043061e-05, "用小棒摆其他平面图形": 0.00024890585136172244, "用小棒摆三角形": 0.00010371077140071768, "杨辉三角": 8.296861712057415e-05, "运用数与形总结规律": 0.0011823027939681814, "求一个笼里的鸽子数-": 0.003982493621787559, "初步认识\"鸽巢原理\"": 1e-06, "较复杂\"鸽巢原理\"（与数字性质/运算有关）": 1e-06, "利用\"鸽巢原理\"解决实际问题-": 1e-06, "最不利": 0.002281636970815789, "球的反弹高度": 1e-06, "平面图形的认识": 4.1484308560287074e-05, "分类图形计数": 2.0742154280143537e-05, "认识各种平面图形": 0.0003941009313227272, "多边形与平行四边形": 1e-06, "认识四边形、五边形和六边形": 1e-06, "四边形的特点": 0.0001659372342411483, "多边形的切分": 2.0742154280143537e-05, "折、剪、拼多边形": 1e-06, "四边形之间的关系": 0.00014519507996100476, "认识线段": 0.00020742154280143537, "画线段": 0.0001659372342411483, "画线段、直线和射线": 0.00010371077140071768, "过点画直线的规律": 1e-06, "认识射线": 0.0003318744684822966, "认识直线": 6.222646284043061e-05, "线段的再认识": 8.296861712057415e-05, "直线、射线、线段之间的关系": 0.00010371077140071768, "两点间的距离(三角形)": 0.0005600381655638754, "两点之间线段最短": 1e-06, "根据垂线的知识发现平行线的关系": 1e-06, "关于垂直的相关判断": 0.00014519507996100476, "认识垂直": 0.00024890585136172244, "相交": 4.1484308560287074e-05, "平行与垂直的综合应用": 2.0742154280143537e-05, "关于平行的相关判断": 0.0001659372342411483, "平行线间的距离处处相等": 1e-06, "认识平行": 4.1484308560287074e-05, "点到直线的距离": 0.00014519507996100476, "点到直线的距离应用": 0.000269648005641866, "画垂线": 0.0002281636970815789, "画已知直线的平行线": 1e-06, "运用点到直线的距离的知识解决画已知直线的平行线的问题": 1e-06, "认识角": 0.00037335877704258366, "画线添角": 0.00012445292568086122, "画直角": 0.00012445292568086122, "认识锐角、钝角": 0.000580780319844019, "认识直角": 0.00018667938852129183, "认识直角、锐角、钝角": 0.00010371077140071768, "角的定义": 4.1484308560287074e-05, "看错内外圈刻度问题": 8.296861712057415e-05, "量指定角的度数(一边未与0刻度线重合)": 0.00012445292568086122, "量指定角的度数(一边与0刻度线重合)": 6.222646284043061e-05, "认识角的度量单位": 1e-06, "认识量角器": 4.1484308560287074e-05, "角的大小比较(涉及角的度数)": 0.00010371077140071768, "初步认识平角和周角": 1e-06, "认识平角": 4.1484308560287074e-05, "认识周角": 1e-06, "角的分类(锐角、直角、钝角)": 1e-06, "锐角、直角、钝角、平角和周角之间的大小关系": 0.00024890585136172244, "画角": 0.00012445292568086122, "用量角器画角": 0.0002903901599220095, "根据三角尺计算角度": 4.1484308560287074e-05, "三角尺拼角": 0.00024890585136172244, "三角板拼角的计算": 0.00010371077140071768, "用三角板画角": 2.0742154280143537e-05, "角度直接运算": 0.0007467175540851673, "认识长方形": 0.00020742154280143537, "认识正方形": 2.0742154280143537e-05, "画长方形": 0.00014519507996100476, "画正方形": 6.222646284043061e-05, "运用画垂线和平行线的方法画长方形": 4.1484308560287074e-05, "运用平行线与正方形的特点解决画最大正方形的问题": 1e-06, "比较图形的周长": 0.0004978117027234449, "多边形周长的简单计算": 6.222646284043061e-05, "拼一拼，比一比": 4.1484308560287074e-05, "认识周长": 0.0003318744684822966, "根据长方形的周长公式求长方形的长和宽": 0.00012445292568086122, "长方形周长的运用": 0.000580780319844019, "篱笆围墙(长方形)": 0.0002281636970815789, "探究长方形的周长计算公式": 1e-06, "铁丝围长方形": 8.296861712057415e-05, "计算长方形的周长": 0.0002281636970815789, "长或宽改变求周长": 8.296861712057415e-05, "正方形周长的应用": 0.00012445292568086122, "根据正方形的周长公式求正方形的边长": 0.00014519507996100476, "计算正方形的周长": 0.0002281636970815789, "长、正方形周长互化": 0.00012445292568086122, "在方格纸中画长、正方形": 0.0003318744684822966, "面积单位比较大小(公顷、平方米和平方千米)": 1e-06, "面积单位比较大小(公顷、平方米)": 1e-06, "面积单位间的大小比较": 0.0007882018626454544, "面积的大小比较": 0.0006222646284043061, "面积的意义": 0.0003941009313227272, "边长增加问题": 0.00018667938852129183, "改变长方形的长或宽求面积变化量": 0.0009956234054468898, "解决有关面积的问题(小数点移动规律)": 0.0006637489369645932, "洒水车、收割机问题": 0.0016178880338511958, "运用长方形面积公式解决实际问题": 0.0015556615710107652, "长方形面积反求": 0.0004770695484433013, "长方形面积与周长的综合应用": 0.0009126547883263156, "长方形面积正求": 0.002281636970815789, "改变正方形的边长求面积变化量": 0.0004770695484433013, "运用正方形面积公式解决实际问题": 0.00037335877704258366, "正方形面积反求": 0.00018667938852129183, "正方形面积与周长的综合应用": 0.0009748812511667462, "正方形面积正求": 0.0005185538570035884, "长、正方形面积综合应用": 0.0013689821824894733, "平行四边形的认识": 0.00014519507996100476, "平行四边形的特征": 0.00012445292568086122, "生活中的平行四边形": 4.1484308560287074e-05, "平行四边形的底和高": 0.00020742154280143537, "平行四边形具有不稳定性": 4.1484308560287074e-05, "画平行四边形": 0.0001659372342411483, "等底等高的平行四边形的面积": 6.222646284043061e-05, "运用多种方法求平行四边形的面积": 4.1484308560287074e-05, "平行四边形高与底变化引起面积的变化": 0.00012445292568086122, "平行四边形面积公式逆用": 0.00031113231420215305, "平行四边形面积公式正用": 0.0005185538570035884, "平行四边形、长方形的面积关系（长拉平/平拉长）": 0.00024890585136172244, "与平行四边形的面积和周长公式有关的问题（无法删）": 1e-06, "平行四边形面积有关的实际问题（二）": 1e-06, "平行四边形面积和周长的运用（求底/高/边长/周长/面积）": 8.296861712057415e-05, "运用平行四边形面积公式解决实际问题（一）": 6.222646284043061e-05, "平行四边形面积计算公式的推导": 6.222646284043061e-05, "梯形的概念": 0.0001659372342411483, "梯形的上底、下底和高": 2.0742154280143537e-05, "梯形的特征": 1e-06, "认识等腰梯形": 4.1484308560287074e-05, "认识直角梯形": 2.0742154280143537e-05, "画梯形的高": 8.296861712057415e-05, "梯形底和高": 1e-06, "等腰梯形周长相关的计算": 0.0001659372342411483, "梯形面积计算公式的推导": 4.1484308560287074e-05, "推导梯形的面积计算公式": 1e-06, "堆放材料的数量": 0.00010371077140071768, "平行线间图形面积问题（含梯、三、平行四边形）": 0.000539296011283732, "梯形的底变化或高变化": 0.0002903901599220095, "梯形面积公式的多种应用": 1e-06, "梯形面积公式应用(逆用)": 0.00041484308560287074, "梯形面积公式应用(正用)": 0.0004978117027234449, "一面靠墙的梯形的面积问题": 0.00020742154280143537, "运用梯形面积公式解决实际问题": 0.00012445292568086122, "梯形、三角形、平行四边形面积综合": 1e-06, "认识三角形": 0.0018253095766526312, "三角形高的画法": 0.0009956234054468898, "三角形的底和高": 0.0012860135653688992, "三角形的稳定性": 0.0016386301881313394, "等腰三角形判断求周长": 0.0013689821824894733, "三角形的三边关系": 0.005413702267117463, "按边分类的应用": 0.00035261662276244013, "按边分类三角形": 0.00018667938852129183, "按角分类的应用": 0.0015764037252909088, "按角分类三角形": 0.0014312086453299039, "等边三角形": 0.000580780319844019, "等腰三角形": 0.0006844910912447367, "三角形的內角和的应用": 0.0023023791250959325, "三角形内角和的认识": 0.0019290203480533488, "多边形内角和的认识": 0.0008296861712057415, "多边形内角和的应用": 0.00024890585136172244, "四边形内角和的认识": 0.000580780319844019, "四边形内角和的应用": 0.0012860135653688992, "等底等高的三角形面积": 0.00024890585136172244, "三角形面积计算公式的推导": 4.1484308560287074e-05, "三角形面积公式的多种应用": 4.1484308560287074e-05, "三角形面积公式逆用": 0.00037335877704258366, "三角形面积公式正用": 0.0003318744684822966, "三角形面积的实际运用": 0.00018667938852129183, "三角形与平行四边形的面积": 0.0007674597083653108, "三角形中的等积变形": 2.0742154280143537e-05, "同圆(或等圆)中直径和半径的关系": 0.0001659372342411483, "圆的认识": 0.00024890585136172244, "圆在生活中的应用": 2.0742154280143537e-05, "长(正)方形中的圆": 0.00020742154280143537, "圆的组合图形的对称轴": 0.0001659372342411483, "圆是轴对称图形": 0.0001659372342411483, "圆周长的意义和测量方式": 4.1484308560287074e-05, "圆的周长公式": 0.0005185538570035884, "圆周率的意义": 0.00010371077140071768, "圆周率的意义(不带比)": 1e-06, "求半圆的周长": 0.000580780319844019, "等周长的圆和其他图形的面积关系": 0.00014519507996100476, "起跑线问题": 2.0742154280143537e-05, "确定起跑线": 1e-06, "圆的周长公式逆向应用": 0.00018667938852129183, "圆的周长公式正向应用": 0.0001659372342411483, "圆周长在生活中的实际应用": 6.222646284043061e-05, "圆的周长与面积的综合运用": 0.0001659372342411483, "圆的面积公式推导": 0.0003318744684822966, "圆的面积计算": 2.0742154280143537e-05, "圆的面积公式逆用": 2.0742154280143537e-05, "圆的面积公式正用": 0.0004978117027234449, "圆的周长、面积与半径的倍数关系(不带比)": 8.296861712057415e-05, "圆环面积的实际应用（考法需和圆环面积计算重整）": 6.222646284043061e-05, "圆环面积的计算": 0.00035261662276244013, "变形的方中圆（考法需重整）": 0.0004978117027234449, "变形的圆中方": 0.0001659372342411483, "多个圆柱缠绕1周求绳长": 1e-06, "求与圆有关的不规则图形的周长": 0.00020742154280143537, "求与圆有关的不规则图形的面积": 0.000269648005641866, "最大活动范围计算": 6.222646284043061e-05, "扇形的认识": 0.00010371077140071768, "时钟问题中弧长的计算": 2.0742154280143537e-05, "扇环的面积": 2.0742154280143537e-05, "扇形的面积": 0.00010371077140071768, "时钟问题中面积的计算": 2.0742154280143537e-05, "圆与扇形中的容斥原理求面积": 1e-06, "用圆规画指定大小的圆（可删）": 1e-06, "方格中的圆": 1e-06, "在方格纸上比较图形的面积": 1e-06, "多边形周长的计算": 2.0742154280143537e-05, "平面图形的周长": 1e-06, "组合图形中的面积问题综合": 0.0001659372342411483, "利用差不变求组合图形的面积": 2.0742154280143537e-05, "运用分割法求组合图形的面积": 2.0742154280143537e-05, "分割法求不规则图形面积": 0.0006222646284043061, "添补法求不规则图形的面积": 6.222646284043061e-05, "拼接后求周长": 0.0006637489369645932, "巧求面积（割补法、平移法、整体减空白法）": 0.0008296861712057415, "巧求长、正方形修路问题": 0.0005185538570035884, "用平移解决面积问题": 0.0013482400282093297, "用平移解决周长问题": 0.0003318744684822966, "运用差不变求组合图形的面积": 0.00018667938852129183, "估算不规则图形的面积": 0.0001659372342411483, "钉子板上的多边形": 1e-06, "平面图形的面积": 2.0742154280143537e-05, "运用整体减空白法求面积": 0.00018667938852129183, "从不同方向观察同一几何体(正、长方体)": 0.0001659372342411483, "从不同方向观察同一物体": 2.0742154280143537e-05, "从不同位置看几何体": 4.1484308560287074e-05, "从不同方向观察简单物体": 8.296861712057415e-05, "从相对位置观察同一物体": 1e-06, "从不同方向观察两个物体的组合体": 1e-06, "从不同位置观察同一几何组合体": 0.0024268320507767937, "从多个方向看到的形状相同的几何组合体": 0.0004770695484433013, "从同一位置观察多个不同几何组合体": 0.0023023791250959325, "与添加/去掉一个小正方体后相关的几何组合体问题": 0.0007259753998050237, "根据立体图形画出平面图形(观察物体二)": 0.0007467175540851673, "根据立体图形画出平面图形(观察物体三)": 0.0005185538570035884, "从两个方向看用小正方体摆出相应组合体": 0.0006222646284043061, "从三个方向看用小正方体摆出相应组合体": 0.000850428325485885, "从一个方向看用小正方体摆出相应组合体-": 0.0015556615710107652, "确定立体图形的摆法-两个方向": 0.00024890585136172244, "确定立体图形的摆法-一个方向": 0.0008711704797660285, "从两个方向看组合体确定小正方体数量": 0.000580780319844019, "从三个方向看组合体确定小正方体数量": 0.0008711704797660285, "根据从两个方向看到的图形推测几何组合体": 2.0742154280143537e-05, "根据从某个方向看到的图形推测几何组合体": 1e-06, "根据从三个方向看到的图形推测几何组合体": 4.1484308560287074e-05, "根据平面图形确定立体图形": 0.0006637489369645932, "根据看到的立体图形的一个面推测立体图形的形状": 8.296861712057415e-05, "根据积木上的数字还原几何体": 0.0007259753998050237, "由观察到的图形确定立体图形需要的小正方体数量": 0.0004978117027234449, "找出物品对应的形状": 2.0742154280143537e-05, "立体图形的比较": 1e-06, "立体图形的认识": 1e-06, "长方体的认识": 0.0017630831138122005, "长方体的特征": 1e-06, "长方体棱长和": 0.00170085665097177, "长方体棱长和的实际问题": 0.0007467175540851673, "正方体的认识": 0.001659372342411483, "正方体和长方体的综合运用": 0.0004978117027234449, "正方体棱长和": 0.0008089440169255979, "正方体棱长和的实际问题": 0.00018667938852129183, "正(长)方体的展开图中相对面的判定": 0.000269648005641866, "正方体的展开图": 0.0021779261994150714, "长方体的展开图": 0.0018667938852129182, "长方体表面积（考法需整合）": 0.002509800667897368, "生活中的长方体面积问题": 0.0031942917591421044, "正方体表面积": 0.0010993341768476073, "生活中的正方体面积问题": 0.0002281636970815789, "长方体棱长变化的运用-": 0.0007052332455248803, "长方体体积的计算": 0.004231399473149281, "长方体体积公式实际应用": 0.0016178880338511958, "正方体体积的计算": 0.0008711704797660285, "正方体体积公式实际应用": 6.222646284043061e-05, "容积有关的复杂实际应用": 0.0010993341768476073, "两面涂色": 8.296861712057415e-05, "没有涂色": 2.0742154280143537e-05, "三面涂色": 2.0742154280143537e-05, "一面涂色": 2.0742154280143537e-05, "卷一卷、折一折": 1e-06, "圆柱的底面、侧面、高、截面": 0.0007052332455248803, "圆柱的形成-": 0.0001659372342411483, "单个圆柱捆扎问题": 0.00024890585136172244, "圆柱的展开图认识": 0.0009333969426064591, "由圆柱表面展开图求体积": 0.00012445292568086122, "侧面积的实际应用": 0.0006015224741241625, "求圆柱侧面积": 0.0006844910912447367, "圆柱底面积的运用-": 1e-06, "圆柱侧面积的变化规律": 0.0001659372342411483, "公式法求圆柱表面积": 0.0012237871025284686, "挖圆柱的表面积问题-": 6.222646284043061e-05, "圆柱高变化与表面积问题-": 0.00037335877704258366, "圆柱的表面积的较复杂应用": 0.0004355852398830143, "圆柱的表面积的应用": 0.00041484308560287074, "圆柱的占地面积": 0.00010371077140071768, "圆柱的展开图求表面积": 0.00024890585136172244, "圆柱体积的变化": 0.00020742154280143537, "圆柱体积（容积）实际应用（装水、流速、短板、包装盒）": 1e-06, "圆柱体积公式的逆用": 0.00020742154280143537, "圆柱体积公式的正用": 0.001659372342411483, "圆柱体积公式推导": 0.000539296011283732, "长方形旋转形成的圆柱体积计算": 0.00031113231420215305, "平面图形的旋转（圆柱、圆锥、组合体...）": 4.1484308560287074e-05, "圆锥的认识（底面、侧面、高）": 0.0007882018626454544, "圆锥的形成": 0.00024890585136172244, "圆锥的展开图（含底面量计算）-": 0.0003941009313227272, "圆柱与圆锥的关系（公式/辨析/找图）-": 0.000539296011283732, "由体积和/差，求等底等高圆柱、圆锥体积": 0.0009956234054468898, "圆锥体积有关的实际问题（纸盒、倒水）": 1e-06, "圆锥的截面": 0.0001659372342411483, "圆锥体积公式的正用": 0.0007467175540851673, "圆锥体积公式的逆用": 0.000580780319844019, "小正方体堆叠求面积": 0.0014104664910497605, "立体图形的表面积": 1e-06, "含圆柱的立体组合图形的表面积": 0.0002903901599220095, "小正方体堆叠求体积": 0.001472692953890191, "立体图形的体积": 2.0742154280143537e-05, "含圆柱的立体组合图形的体积(叠放型)": 0.0001659372342411483, "含圆柱的立体组合图形的体积(挖去型)": 0.00014519507996100476, "含圆锥的组合体的体积（叠放型）": 0.0002281636970815789, "复杂水中浸物（求溢出水的体积）": 0.0010371077140071767, "排水法求物体体积（方法/步骤）（考法需整合）": 0.0022608948165356454, "稍复杂水中浸物": 0.0004978117027234449, "圆柱中的排水法（单个物体）-": 0.001078592022567464, "利用\"等积变形\"的数学思想解决问题": 1e-06, "瓶中水（含圆柱的正倒放）": 0.0008711704797660285, "不规则图形折、剪、拼": 1e-06, "规则图形折、剪、拼": 0.0003941009313227272, "长方形的剪拼": 4.1484308560287074e-05, "正方形的剪拼": 4.1484308560287074e-05, "给定条件分割四边形的问题": 0.00010371077140071768, "角度的简单计算--对折": 6.222646284043061e-05, "角度的简单计算--折叠": 0.0001659372342411483, "角度的简单计算--重叠": 0.00010371077140071768, "绳长对折问题": 0.00014519507996100476, "轴对称中对折问题": 0.0003318744684822966, "需要添加几个小正方体": 0.0001659372342411483, "长正方体切割的表面积问题（考法需整合）": 0.0013067557196490428, "平行底面切/拼圆柱的表面积": 0.0003318744684822966, "平行底面切/拼圆柱的体积计算": 0.0004355852398830143, "竖切圆柱体的表面积": 0.00041484308560287074, "竖切圆柱体积的计算": 0.00024890585136172244, "斜切圆柱的体积": 0.00020742154280143537, "削成最大的圆柱": 0.00031113231420215305, "圆柱体切拼成近似长方体的体积": 0.0003318744684822966, "竖切圆锥表面积的变化-": 0.0001659372342411483, "初步认识轴对称图形": 0.000580780319844019, "认识轴对称图形和对称轴(平面图形)": 0.0007467175540851673, "生活中的对称现象": 0.0008089440169255979, "轴对称图形的性质及应用": 0.0003941009313227272, "镜子问题": 0.0001659372342411483, "钟表中的对称问题": 8.296861712057415e-05, "剪纸的分辨": 0.000269648005641866, "如何得到剪纸图案": 0.0009748812511667462, "华容道": 1e-06, "确定平移的方向和距离": 0.0012237871025284686, "认识平移": 0.0005600381655638754, "生活中的平移": 0.00020742154280143537, "图形的平移": 0.0009541390968866026, "生活中的旋转": 0.0001659372342411483, "通过旋转解决实际问题": 0.002717222210698803, "图形的旋转": 0.0007467175540851673, "旋转的含义": 0.00041484308560287074, "基础图形的运动-": 0.0004563273941631578, "旋转与重合": 0.0008089440169255979, "旋转中转盘问题": 0.00014519507996100476, "对折法数画对称轴": 0.00037335877704258366, "画出轴对称图形的对称轴": 1e-06, "根据对称轴补全轴对称图形": 0.0009748812511667462, "在方格纸上画平移图形": 0.0008711704797660285, "图形旋转的方向和角度": 0.0021364418908547842, "判断/绘制图形旋转90°的图形": 0.0021986683536952146, "利用平移和旋转探究图形变化的方法": 0.002157184045134928, "在方格纸上画出对称，旋转、平移后的图形（考法需整合）": 0.0012652714110887556, "图形的运动（轴对称、平移、旋转）": 0.0002281636970815789, "画出与圆有关的图案": 4.1484308560287074e-05, "不同位置观察物体的范围": 1e-06, "物体影长的变化规律及画法": 1e-06, "判断连续拍摄的一组照片拍摄的先后顺序": 1e-06, "拍摄点与照片的位置关系": 1e-06, "判断物体所在的位置(前后)": 6.222646284043061e-05, "物体之间的相对位置(前后)": 1e-06, "序数描述位置关系(前后)": 1e-06, "判断物体所在的位置(上下)": 1e-06, "物体之间的相对位置(上下)": 1e-06, "序数描述位置关系(上下)": 1e-06, "判断物体所在的位置(左右)": 0.00010371077140071768, "生活中的左右": 2.0742154280143537e-05, "物体之间的相对位置(左右)": 1e-06, "序数描述位置关系(左右)": 1e-06, "前后、左右、上下的综合问题": 1e-06, "人或物的位置综合": 1e-06, "空间位置关系": 1e-06, "位置推理": 1e-06, "位置应用": 1e-06, "行进路线描述": 1e-06, "辨认东、西、南、北四个方向": 0.0016386301881313394, "根据描述确定位置（八个方向）": 0.0006844910912447367, "相对方向(四个方向)": 0.001514177262450478, "运用推理法解决方格图中的方位问题（四个方向）": 4.1484308560287074e-05, "在平面图上辨认东、南、西、北": 0.0015971458795710522, "认识东北、东南、西北、西南四个方向": 0.0007467175540851673, "相对方向(八个方向)": 0.002157184045134928, "运用推理法解决物体移动的位置与方向问题": 0.00018667938852129183, "在地图或平面示意图上辨认方向": 0.0016178880338511958, "路线图中的数对问题": 0.00014519507996100476, "认识数对": 0.0004563273941631578, "示意图中物体的位置": 8.296861712057415e-05, "数对与棋盘中的位置": 8.296861712057415e-05, "数对与轴对称": 1e-06, "根据数对确定位置": 0.000269648005641866, "具体情境中物体的位置": 0.00018667938852129183, "数对与平移": 0.00020742154280143537, "数对与图形的顶点": 0.0002903901599220095, "数对与位置变化的规律": 0.00012445292568086122, "数对与行列": 0.00024890585136172244, "八个方向描述简单的行走路线": 0.002468316359337081, "描述路线图": 0.0001659372342411483, "绘制路线图": 0.00014519507996100476, "用方向和距离描述某个点的位置": 0.0007467175540851673, "用方向和距离确定某个点的位置": 0.00035261662276244013, "图形与位置": 8.296861712057415e-05, "残余刻度量长度（厘米）": 6.222646284043061e-05, "测量线段（厘米）": 2.0742154280143537e-05, "测量折线长度": 1e-06, "描述其他物品的长度和高度": 6.222646284043061e-05, "测量方式": 0.00012445292568086122, "直尺测量（厘米）": 0.0001659372342411483, "认识厘米": 0.0002903901599220095, "认识米": 2.0742154280143537e-05, "认识分米": 0.0001659372342411483, "认识毫米": 0.00024890585136172244, "千米的认识": 0.0002903901599220095, "单位换算(米和厘米)": 0.0001659372342411483, "米、厘米的应用": 0.00014519507996100476, "长度的简单计算问题(米、厘米)-": 2.0742154280143537e-05, "长度与比较(米和厘米)": 4.1484308560287074e-05, "分米、厘米和毫米之间的单位换算": 0.00020742154280143537, "分米、厘米和毫米之间的单位计算": 4.1484308560287074e-05, "解决有关分米、厘米、毫米的问题": 0.0001659372342411483, "千米和米的大小比较": 6.222646284043061e-05, "千米与米的换算": 6.222646284043061e-05, "选择合适的长度单位(米和厘米)": 0.0004978117027234449, "选择合适的长度单位(分米、毫米)": 0.0003941009313227272, "选择合适的长度单位(千米、米、厘米、毫米)": 0.00014519507996100476, "估一估，量一量(米)": 1e-06, "量一量，比一比": 6.222646284043061e-05, "估计距离/路程": 1e-06, "认识公顷": 1e-06, "认识公顷和平方千米(含小数)": 1e-06, "公顷、平方米和平方千米的单位换算与计算": 1e-06, "公顷与平方米的换算与计算": 1e-06, "图形的面积计算并换算(公顷、平方米和平方千米)": 1e-06, "与公顷、平方千米有关的实际问题": 1e-06, "公顷、平方米和平方千米的单位换算(含小数)": 1e-06, "面积单位换算应用": 0.0011823027939681814, "面积单位间的换算": 0.002219410507975358, "与公顷有关的实际问题": 1e-06, "选择合适的面积单位(公顷、平方千米)": 0.0002903901599220095, "认识面积单位": 0.002945385907780382, "含容积单位的比大小": 1e-06, "含体积单位的比大小": 0.000269648005641866, "容量的大小比较": 1e-06, "升与毫升的大小比较": 1e-06, "解决升与毫升的实际问题": 1e-06, "认识毫升": 1e-06, "认识容积单位": 0.0013482400282093297, "认识容量": 2.0742154280143537e-05, "认识升": 1e-06, "容积的运用": 0.000269648005641866, "容量与分数": 1e-06, "升与毫升在生活中的应用": 1e-06, "体积单位的认识": 0.0016386301881313394, "体积定义": 0.0006222646284043061, "含体积单位的计算": 4.1484308560287074e-05, "体积单位间的进率和换算": 0.0011408184854078945, "含容积单位的计算": 0.0001659372342411483, "容积单位的换算": 0.001389724336769617, "升与毫升的换算": 1e-06, "升与立方分米的关系": 1e-06, "填合适的单位(容积）": 0.0008296861712057415, "按不同标准分类、计数(初阶)": 1e-06, "按给定标准分类(初阶)": 1e-06, "按给定标准分类计数（初阶）": 1e-06, "按要求分类(100以内数的比较)": 1e-06, "按指定标准分类(初阶)": 1e-06, "根据分类结果判断分类依据": 1e-06, "自选标准分类计数": 1e-06, "旅游": 1e-06, "认识象形统计图": 1e-06, "象形统计图和统计表": 2.0742154280143537e-05, "用\"正\"字记录数据": 1e-06, "用调查法收集数据及认识简单的统计表": 0.0006430067826844496, "制作复式统计表": 0.0009541390968866026, "认识复式统计表": 0.00010371077140071768, "以一当一的条形统计图": 0.000580780319844019, "以一当多的条形统计图": 0.0004563273941631578, "以一当二的条形统计图": 0.00010371077140071768, "以一当五的条形统计图": 0.00035261662276244013, "读取复式条形统计图并回答问题": 0.0003318744684822966, "复式条形统计图综合应用": 0.002945385907780382, "绘制复式条形统计图": 1e-06, "认识复式条形统计图": 2.0742154280143537e-05, "根据单式折线统计图分析、预测": 0.0022401526622555018, "绘制单式折线统计图": 0.00024890585136172244, "认识折线统计图及其特点": 0.0012860135653688992, "根据复式折线统计图分析、预测": 0.002903901599220095, "绘制复式折线统计图": 0.00041484308560287074, "认识复式折线统计图及其特点": 0.0005600381655638754, "根据数据绘制扇形统计图": 0.0002281636970815789, "解决扇形统计图圆心角的度数问题": 4.1484308560287074e-05, "认识扇形统计图": 0.0001659372342411483, "选择合适的统计图": 0.0007467175540851673, "公式法求平均数": 0.00170085665097177, "认识平均数": 0.00020742154280143537, "小数运算解决平均数问题": 2.0742154280143537e-05, "移多补少求平均数": 0.00010371077140071768, "在统计表中计算平均数": 0.000850428325485885, "根据统计表解决问题": 0.0018875360394930618, "统计表综合问题": 6.222646284043061e-05, "根据表中数据解决问题": 0.0018045674223724877, "根据数据完成统计表和统计图": 4.1484308560287074e-05, "节约用水": 0.00010371077140071768, "扇形统计图和条形统计图的比较": 0.00041484308560287074, "扇形统计图中获取信息填空-（考法需整合）": 0.0006430067826844496, "分段整理数据": 4.1484308560287074e-05, "分段整理数据，绘制单式统计图(表)": 1e-06, "运用分段整理数据解决实际问题": 4.1484308560287074e-05, "分组整理数据，绘制复式统计图(表)": 1e-06, "平面图上某点运动结合s-t图形求面积-": 2.0742154280143537e-05, "行程折线图": 0.00018667938852129183, "运行图中的行程问题（单式折线图）": 0.0012652714110887556, "不确定现象和确定现象": 2.0742154280143537e-05, "摸球问题": 0.00035261662276244013, "抛硬币问题": 0.0001659372342411483, "三种事件的认识": 1e-06, "事件发生的不确定性和确定性": 0.0002903901599220095, "根据可能性的大小设计方案": 0.00012445292568086122, "判断事件发生的可能性的大小": 0.0013067557196490428, "可能性判断游戏规则的公平": 0.0002281636970815789, "根据可能性大小进行推测": 6.222646284043061e-05, "卡片求和问题": 4.1484308560287074e-05, "运用列表法解决可能性问题": 8.296861712057415e-05, "探索两位数乘11的计算规律": 0.00018667938852129183, "算式找规律问题": 1e-06, "找规律(算式)": 1e-06, "找规律(100以内)": 6.222646284043061e-05, "找规律填数(两位数加一位数、整十数)": 1e-06, "找规律填数(两位数减一位数、整十数)": 1e-06, "递减规律": 1e-06, "递增规律": 1e-06, "找规律填数（20以内）": 4.1484308560287074e-05, "找规律填数(10000以内)": 0.00018667938852129183, "找规律填数(1000以内)": 0.00035261662276244013, "找规律(2-6的乘法口诀)": 1e-06, "根据规律写小数": 0.00024890585136172244, "根据规律补全图形或数": 2.0742154280143537e-05, "根据规律判断第几个图形是什么": 4.1484308560287074e-05, "图形找规律(形状、颜色)": 8.296861712057415e-05, "找规律(图形)": 8.296861712057415e-05, "找规律画图": 4.1484308560287074e-05, "位置变化规律": 1e-06, "框数游戏": 1e-06, "数表中规律": 0.00014519507996100476, "间隔问题(20以内)": 0.00010371077140071768, "三位数加三位数的巧算": 4.1484308560287074e-05, "三位数减三位数的巧算": 8.296861712057415e-05, "巧求\"头同尾合十\"的两位数乘两位数": 1e-06, "巧求\"尾同头合十\"的两位数乘两位数": 1e-06, "有趣的乘法计算": 0.00018667938852129183, "小数乘除简算": 4.1484308560287074e-05, "分数裂差": 1e-06, "表内除法错中求解": 0.0002903901599220095, "除数是两位数除法的错中求解问题": 8.296861712057415e-05, "错中求解(乘除法各部分间的关系)": 0.00035261662276244013, "错中求解(加减法各部分间的关系)（万以内）": 0.00018667938852129183, "错中求解(表内混合运算)": 0.0003318744684822966, "简单小数的加法和减法错中求解": 0.0007467175540851673, "错中求解(两位数、几百几十数)": 0.00014519507996100476, "三位数乘两位数的错中求解问题": 1e-06, "万以内的加法和减法错中求解": 2.0742154280143537e-05, "数形结合正方形数": 0.0003318744684822966, "连续自然数求和的巧算": 1e-06, "极限思想": 6.222646284043061e-05, "数的大小比较": 1e-06, "变化后变相同(100以内)": 1e-06, "不等化等(1-5)": 8.296861712057415e-05, "移多补少(结合倍)": 6.222646284043061e-05, "移多补少(10以内)": 4.1484308560287074e-05, "移多补少(求多多少)(100以内)": 6.222646284043061e-05, "移多补少(求给多少)(100以内)": 8.296861712057415e-05, "移多补少(求原来)(100以内)": 0.0001659372342411483, "移多补少（20以内）": 6.222646284043061e-05, "用不同的估算策略解决实际问题": 8.296861712057415e-05, "除号后添/去括号": 2.0742154280143537e-05, "带括号的四则混合运算中的趣味题目": 0.00018667938852129183, "减号后添/去括号": 0.00020742154280143537, "巧填算符(不含中括号)": 0.00014519507996100476, "解决添运算符号的问题": 4.1484308560287074e-05, "巧填算符(表内混合运算)": 8.296861712057415e-05, "巧填算符(100以内)": 2.0742154280143537e-05, "填算符": 4.1484308560287074e-05, "填算符(加减乘2-6)": 2.0742154280143537e-05, "填算符(加减乘除1-9)": 0.00012445292568086122, "填算符(加减乘除2-6)（移题后删）": 0.00012445292568086122, "不等式方框里最大能填几": 1e-06, "不等式填空(100以内)": 1e-06, "括号里最大能填几": 1e-06, "不等式方框里能填几": 1e-06, "三位数乘两位数积的大小比较": 2.0742154280143537e-05, "算式比大小(10以内)": 4.1484308560287074e-05, "算式比大小(20以内不进退位)": 1e-06, "算式比大小(20以内退位减)": 1e-06, "算式比大小(5和2)": 1e-06, "算式比大小(两位数加一位数、整十数)": 1e-06, "算式比大小(两位数减一位数、整十数)": 1e-06, "算式比大小(20以内进位加)": 8.296861712057415e-05, "除法竖式谜": 0.0003941009313227272, "几百几十数加、减几百几十数竖式谜": 6.222646284043061e-05, "两、三位数乘一位数的竖式谜": 1e-06, "两位数乘两位数的数字谜": 0.00037335877704258366, "万以内加法竖式谜": 6.222646284043061e-05, "万以内减法竖式谜": 1e-06, "小数乘法竖式谜": 2.0742154280143537e-05, "除数是小数的小数除法竖式谜": 1e-06, "小数加法竖式谜": 0.0002903901599220095, "小数减法竖式谜": 2.0742154280143537e-05, "有关0的除法竖式谜": 1e-06, "两、三位数乘一位数的填数问题": 1e-06, "运用推理法解决除法竖式谜问题": 1e-06, "不等式横式谜(乘法口诀)": 0.00014519507996100476, "乘除法横式谜(1-9)": 0.0006637489369645932, "乘法横式谜(表内2-6）": 1e-06, "乘法横式谜(表内)": 1e-06, "除法横式谜(表内2-6)": 0.000580780319844019, "除数是一位数的填数问题": 0.001161560639688038, "横式谜(1-10)": 1e-06, "横式谜(11-20)": 2.0742154280143537e-05, "横式谜(20以内)": 0.00010371077140071768, "加减法横式谜(100以内)": 0.0001659372342411483, "加减法一元推算(20以内)": 2.0742154280143537e-05, "数图找规律(加减乘除1-9)": 1e-06, "小数加法数字谜": 0.0008711704797660285, "小数减法数字谜": 0.00035261662276244013, "横式谜(20以内进位加)": 4.1484308560287074e-05, "三阶幻方和数阵图(100以内)": 1e-06, "封闭型数阵图(100以内)": 4.1484308560287074e-05, "封闭型数阵图(1-10)": 8.296861712057415e-05, "封闭型数阵图(20以内)": 8.296861712057415e-05, "封闭型数阵图(整百数)": 0.0001659372342411483, "辐射型数阵图(100以内)": 1e-06, "辐射型数阵图(1-10)": 1e-06, "辐射型数阵图(20以内)": 1e-06, "数阵图(100以上)": 1e-06, "填数游戏": 1e-06, "整数": 1e-06, "奇偶性在实际问题中的应用": 0.0007259753998050237, "数角(角的初步认识)": 0.00012445292568086122, "分类枚举法数三角形": 4.1484308560287074e-05, "分类枚举数平行四边形": 1e-06, "简单平面图形计数": 0.0004770695484433013, "平行四边形计数": 6.222646284043061e-05, "数平行线和垂线": 0.00018667938852129183, "数三角形": 0.0004978117027234449, "数四边形": 2.0742154280143537e-05, "数线段、直线和射线": 0.0003318744684822966, "数线段-解决实际问题": 2.0742154280143537e-05, "数线段与连线计数": 1e-06, "四边形计数(有序思考)": 1e-06, "梯形计数": 6.222646284043061e-05, "与图形相关的计数": 1e-06, "长方形计数": 4.1484308560287074e-05, "正方形计数": 1e-06, "正方形数": 6.222646284043061e-05, "数射线": 1e-06, "枚举法数长方形": 1e-06, "枚举法数正方形": 4.1484308560287074e-05, "从一个方向看组合体确定小正方体数量": 0.00012445292568086122, "数组合图形小正方体的数量": 0.00041484308560287074, "有隐藏的立方体": 1e-06, "组合图形数小正方体数量的规律": 0.00014519507996100476, "用规定数字组数填数": 2.0742154280143537e-05, "可能性": 0.0021156997365746407, "平均数与数据分析": 2.0742154280143537e-05, "统计图表的绘制与计算": 1e-06, "统计图表的认识与选择": 1e-06, "玩中认图形": 1e-06, "不规则图形求周长": 0.000269648005641866, "改变边长求周长": 1e-06, "剪后求周长": 0.0002281636970815789, "方格图中的面积": 0.0007674597083653108, "平行四边形中的等积变形": 1e-06, "运用平移、\"等积变形\"解决复杂的面积问题": 1e-06, "长方形和平行四边形的一半": 1e-06, "七巧板拼图问题": 0.00041484308560287074, "认识七巧板": 0.0002903901599220095, "七巧板中的应用": 1e-06, "用几块七巧板摆拼平面图形": 1e-06, "相遇的行程问题": 8.296861712057415e-05, "往返行程问题": 0.00018667938852129183, "解决发车问题": 0.000269648005641866, "解决复杂应用题": 2.0742154280143537e-05, "解决简单应用题": 1e-06, "从条件出发解决实际问题": 1e-06, "从条件出发解决有关四则混合运算的实际应用题": 1e-06, "从条件出发解决综合实际问题": 1e-06, "解决分段计费问题": 8.296861712057415e-05, "从条件出发解决归一、归总问题": 1e-06, "分数乘除解归一归总问题": 0.00014519507996100476, "解决归一归总问题": 0.00018667938852129183, "图示法解决问题": 2.0742154280143537e-05, "小数运算解决归一归总问题": 0.00010371077140071768, "和倍问题": 0.0010163655597270331, "差倍问题": 0.0004770695484433013, "改动替换一个数后的平均数": 4.1484308560287074e-05, "平均数的实际问题": 0.0005185538570035884, "平均数的应用": 6.222646284043061e-05, "用平均数比较数据的总体情况": 0.0001659372342411483, "用平均数倒推": 0.0006430067826844496, "增加去掉一个数后的平均数": 0.000269648005641866, "单人工程问题": 6.222646284043061e-05, "解决工程问题": 1e-06, "三人合作工程问题": 8.296861712057415e-05, "单双数(20以内)": 1e-06, "简单页码问题": 6.222646284043061e-05, "求页数（20以内）": 0.0001659372342411483, "根据时间规律解决问题": 2.0742154280143537e-05, "钟面上角的计算问题": 0.00031113231420215305, "间隔问题的相关应用": 0.00041484308560287074, "两种物体间隔排列的规律": 1e-06, "测量中的间隔问题": 2.0742154280143537e-05, "锯木头问题": 0.0001659372342411483, "敲钟问题": 8.296861712057415e-05, "有关时间推算的锯木头问题": 4.1484308560287074e-05, "有关时间推算的爬楼梯问题": 2.0742154280143537e-05, "排队问题单主角\"第\"\"第\"问题(20以内)": 1e-06, "排队问题单主角\"有\"\"第\"问题(20以内)": 1e-06, "排队问题单主角\"有\"\"有\"问题（20以内）": 1e-06, "排队问题基础": 1e-06, "双主角排队问题": 0.00010371077140071768, "方阵问题": 0.0006637489369645932, "植树问题之爬楼梯问题": 6.222646284043061e-05, "串珠子问题": 1e-06, "基本周期问题": 0.0014519507996100475, "分组法解决鸡兔同笼": 4.1484308560287074e-05, "年龄中的倍数问题": 0.00018667938852129183, "利润和折扣综合求成本": 0.0002903901599220095, "利润和折扣综合求定价": 0.0002281636970815789, "根据浓度和溶液求溶质": 0.00012445292568086122, "根据溶质和浓度求溶液": 2.0742154280143537e-05, "两种不同浓度溶液混合问题": 1e-06, "浓度的认识": 2.0742154280143537e-05, "浓度问题中的增加溶质或溶剂问题": 8.296861712057415e-05, "浓度问题中的蒸发问题": 2.0742154280143537e-05, "报数问题的必胜策略": 6.222646284043061e-05, "过河问题": 0.0001659372342411483, "排队问题--求最短时间": 0.0001659372342411483, "找规律综合": 4.1484308560287074e-05, "组数求积的最大值或最小值(不含0)(三位数乘两位数)": 1e-06, "组数求积的最大值或最小值(含0)(三位数乘两位数)": 2.0742154280143537e-05, "巧用\"差\"解决问题": 1e-06, "抓住不变量解决连瓶重问题": 1e-06, "运用多种策略解决问题": 1e-06, "测量中的重叠问题": 0.00012445292568086122, "容斥法求重叠图形的面积": 1e-06, "读取秤上数值": 0.0002281636970815789, "分析法": 1e-06, "列表法": 1e-06, "数形结合法": 1e-06, "体验统一长度单位": 1e-06, "初步认识立体图形": 0.00041484308560287074, "立体图形的稳定性（单个图形）": 4.1484308560287074e-05, "多种立体图形的拼搭": 0.0001659372342411483, "相同正方体拼搭": 0.00041484308560287074, "2-6的乘加、乘减计算": 1e-06, "2-6的乘加乘减应用题": 2.0742154280143537e-05, "7的乘加、乘减计算": 0.00012445292568086122, "7的乘加乘减应用题": 0.00037335877704258366, "8的乘加、乘减计算": 2.0742154280143537e-05, "8的乘加乘减应用题": 0.0004770695484433013, "巧用小数乘法运算律进行简算": 0.0004978117027234449, "小数四则混合运算（乘法运算律）": 0.0006637489369645932, "9的乘法应用题（旧版）": 1e-06, "9的乘加、乘减计算": 0.00012445292568086122, "分类枚举法数角(角的认识)": 0.00010371077140071768, "有余数的小数除法（除数是整数）": 1e-06, "除数是整数的基本算理及算法9": 4.1484308560287074e-05, "除数是整数的小数除法计算9": 0.00018667938852129183, "判断商与1的大小关系（除数是整数的小数除法）9": 2.0742154280143537e-05, "除数是整数的小数运算比大小9": 2.0742154280143537e-05, "除数是整数的小数除法运用9": 8.296861712057415e-05, "商不变的性质（小数除法）-": 0.0003318744684822966, "除数是小数的除法计算9": 0.0003318744684822966, "除数是小数的除法运用9": 0.00010371077140071768, "除数是小数的算式比大小9": 0.0001659372342411483, "小数运算解决错中求解问题（除数是整数）": 4.1484308560287074e-05, "小数运算解决错中求解问题（除数是小数）": 6.222646284043061e-05, "解含有两个未知数的方程": 0.0002903901599220095, "化简含字母的式子": 0.0007052332455248803, "线段图与方程": 0.0001659372342411483, "小数乘小数的运用-": 0.00014519507996100476, "数的组成(亿以上)": 4.1484308560287074e-05, "计算工具的认识": 8.296861712057415e-05, "小数乘整数的算理、算法-": 1e-06, "小数乘整数的运用-": 1e-06, "错中求解（小数乘整数）9": 6.222646284043061e-05, "错中求解（含字母/未知数）": 8.296861712057415e-05, "用字母表示自然数9": 0.0004978117027234449, "数线图与字母9": 2.0742154280143537e-05, "油桶问题(两、三位数的加减法)": 6.222646284043061e-05, "利润与折扣的综合问题": 0.00010371077140071768, "接送问题": 1e-06, "环形路线": 1e-06, "行程中的变速及平均速度问题": 1e-06, "火车过桥问题": 2.0742154280143537e-05, "流水行船问题": 2.0742154280143537e-05, "错车问题": 1e-06, "扶梯问题": 1e-06, "买票问题(多位数乘一位数)": 0.0001659372342411483, "三角形与长/正方形的面积": 0.00010371077140071768, "图形中的百分数问题": 1e-06, "行程中的百分数问题": 0.00014519507996100476, "表与时间": 2.0742154280143537e-05, "商是循环小数的算式的规律": 1e-06, "一亿有多大": 0.00037335877704258366, "错中求解(100以内)": 0.0005185538570035884, "尺规作图": 4.1484308560287074e-05, "10以内加减法中的运算规律": 1e-06, "分数乘整数的运用": 4.1484308560287074e-05, "解小数方程": 8.296861712057415e-05, "含圆的组合图形的面积": 0.0007674597083653108, "铁丝围正方形": 6.222646284043061e-05, "画三角形": 0.0003318744684822966, "时间比较(几时几分)": 8.296861712057415e-05, "加法应用题(不进位加)(100以内)": 8.296861712057415e-05, "笔算两位数加一位数(不进位)": 0.00014519507996100476, "不进位/进位加法的计算运用": 1e-06, "减法应用题(不退位)(100以内)": 4.1484308560287074e-05, "两位数加减法的计算运用(100以内)": 1e-06, "连加、连减、加减混合的应用(100以内)": 1e-06, "整数乘加、乘减运算": 1e-06, "百分数、分数、小数、比之间的互化": 0.00014519507996100476, "表内乘法口诀（1-9）-": 1e-06, "购物问题(100以内)": 8.296861712057415e-05, "不退位/退位减法的计算运用": 4.1484308560287074e-05, "比多少的计算运用(100以内)": 2.0742154280143537e-05, "\"提问题\"、\"填条件\"问题(100以内)": 1e-06, "认识含万级的数位顺序表": 0.0019082781937732052, "两数比大小(1000以内)": 4.1484308560287074e-05, "算盘表示数(10000以内)": 2.0742154280143537e-05, "算盘上拨数(10000以内)": 6.222646284043061e-05, "包含分求份数（列除法算式）": 0.0006637489369645932, "平均分求每份数（列除法算式）": 0.0009541390968866026, "角的大小比较(初步认识角)": 8.296861712057415e-05, "与直角有关的应用": 6.222646284043061e-05, "角的初步认识的应用(直角、锐角、钝角)": 8.296861712057415e-05, "数阵图(几百几十数)": 1e-06, "角的认识的应用(直角、锐角、钝角、平角和周角)": 4.1484308560287074e-05, "量角应用": 1e-06, "相同长方体拼搭": 4.1484308560287074e-05, "两位数与两位数的不进位/进位加的口算运用": 2.0742154280143537e-05, "两位数与两位数不退位/退位减的口算运用": 2.0742154280143537e-05, "两位数加减的口算运用": 4.1484308560287074e-05, "两位数加减的口算应用题": 4.1484308560287074e-05, "乘法意义的应用题": 4.1484308560287074e-05, "由增减成数，求量间百分率关系-": 0.0001659372342411483, "含字母式子的计算9": 0.0003318744684822966, "2-5的乘法口诀": 0.00024890585136172244, "用字母表示含倍数关系的量9": 0.0001659372342411483, "6的乘法口诀应用题": 0.0003941009313227272, "2-6的乘法口诀的理解（旧）": 1e-06, "等量关系与方程9": 2.0742154280143537e-05, "加法、乘法求总数问题的对比-": 0.00024890585136172244, "梯形与平行四边形的面积问题": 0.00014519507996100476, "画梯形（知面积）-": 1e-06, "6的乘加、乘减计算": 4.1484308560287074e-05, "6的乘加乘减应用题": 0.00014519507996100476, "从不同方向观察简单立体图形": 0.0001659372342411483, "分数列项计算": 0.00010371077140071768, "解决总价问题(表内乘法)": 0.00018667938852129183, "扇形圆心角": 2.0742154280143537e-05, "表内乘加、乘减计算": 2.0742154280143537e-05, "表内乘法应用题（1-9）-": 8.296861712057415e-05, "表内乘法计算（1-9)-": 4.1484308560287074e-05, "与平均数有关的实际问题（正负数）": 1e-06, "体育运动中的实际问题（正负数）": 0.00018667938852129183, "选择合适的时间单位(时、分)": 4.1484308560287074e-05, "根据描述确定位置（四个方向）": 0.0003941009313227272, "方向与路程的综合问题（八个方向）": 0.0006844910912447367, "由原价、现价、售价关系求折扣": 0.0005185538570035884, "用不同的方法记录数据": 0.0010993341768476073, "两位数除以一位数(被除数首位能被整除)解决实际问题": 0.0004978117027234449, "除数是一位数的估算的应用": 0.0014519507996100475, "几个小部分拼接成大长方体": 2.0742154280143537e-05, "除数是一位数除法的错中求解问题": 0.000539296011283732, "利用被减数、减数、差的关系解决问题": 2.0742154280143537e-05, "由一道乘法算式计算两道除法算式(2-6)": 0.0005185538570035884, "购物问题(2-6)": 0.00014519507996100476, "用除法解决问题": 0.0007467175540851673, "乘、除法各部分间的关系的计算与应用": 0.0011823027939681814, "含有小括号的四则混合运算的运算顺序": 0.0006637489369645932, "购物问题": 0.0002281636970815789, "算式比大小(整数的四则混合运算)": 0.00014519507996100476, "整数的四则混合运算": 0.0018045674223724877, "圆柱展开图的运用（涉及圆柱底面信息、高）": 0.0006222646284043061, "质因数的含义": 1e-06, "质数、合数、因数、倍数的运用-": 0.0005185538570035884, "加法运算定律和减法运算性质的综合运用": 0.0004978117027234449, "同级运算的计算与应用（乘除混合、连乘、连除）": 0.0006222646284043061, "运用乘法分配律简算": 0.003484681919064114, "两位数乘两位数的错中求解问题": 0.0004978117027234449, "买票问题（两位数乘两位数）": 0.00035261662276244013, "图文算式（两位数乘两位数）": 0.00010371077140071768, "估一估（有关面积和长度）": 0.00010371077140071768, "倒数的比大小9": 0.0005185538570035884, "平移和旋转的综合": 0.00545518657567775, "倒数的运用9": 0.0007259753998050237, "乘除法的计算(表内)": 0.0007259753998050237, "计算经过的天数": 0.001742340959532057, "一天时间的理解": 0.00037335877704258366, "含有小括号的表内混合运算": 0.0007467175540851673, "小数的含义": 0.002592769285017942, "小数的写法（一位小数）": 0.00012445292568086122, "小数加减法的错中求解（一位小数）": 0.000539296011283732, "求两数公因数、最大公因数的特殊情况": 0.0030490966791810997, "计数器表示数(1000以内)": 0.000269648005641866, "简单分数方程（分数加减）-": 0.000850428325485885, "计数器表示数(10000以内)": 0.0001659372342411483, "横式谜、算式谜(整百、整千数加减法)": 0.0001659372342411483, "用几百几十、整十、整百和整千数的估算解决问题": 0.00041484308560287074, "找规律填数（复杂）-": 0.00010371077140071768, "根据已给数找出规律继续填数9": 1e-06, "鸡兔同笼变型题": 0.0023438634336562197, "确定平移后的图形": 0.0001659372342411483, "三角形的分类综合应用": 0.0013689821824894733, "在点子图上画三角形": 0.0003318744684822966, "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)": 0.0038165563875464104, "特殊四边形内角和的应用(长方形/正方形)": 0.0006844910912447367, "小数加减法": 0.005766318889879903, "小数加减法应用题(涉及小数简算)": 0.002468316359337081, "立体图形中的平面图形": 0.0009956234054468898, "算式比大小（十几减9）": 4.1484308560287074e-05, "十几减9的计算": 0.00012445292568086122, "十几减9的应用": 0.00012445292568086122, "平十法（十几减8）": 2.0742154280143537e-05, "破十法（十几减8）": 6.222646284043061e-05, "十几减8的应用": 0.00014519507996100476, "十几减8的计算": 0.00012445292568086122, "十几减7、6的计算": 8.296861712057415e-05, "算式比大小（十几减7、6）": 0.0001659372342411483, "十几减7、6的实际问题": 0.0004563273941631578, "算式比大小（十几减5、4、）": 0.00010371077140071768, "十几减5、4、3、2的应用": 0.00020742154280143537, "认识计数单位": 0.000269648005641866, "数的顺序（100以内）": 0.0007052332455248803, "用珠子摆数": 0.0006430067826844496, "两位数减一位数（退位）解决实际问题": 0.0005185538570035884, "数量关系的比较": 0.0003941009313227272, "分步解决问题的策略": 0.0008296861712057415, "直条统计图": 0.0008711704797660285, "运用乘、除法的意义和各部分之间的关系解决实际问题": 0.0007467175540851673, "运用加、减法各部分间的关系巧解算式": 0.0005600381655638754, "运用加、减法的意义和各部分之间的关系解决实际问题": 0.0003941009313227272}