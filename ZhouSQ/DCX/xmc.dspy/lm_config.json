{"llama-2-13b-chat": {"url": "http://future-hgx-1", "model": "meta-llama/Llama-2-13b-chat-hf", "temperature": 0.01, "max_tokens": 150, "top_p": 0.97, "n": 1, "stop": ["\n\n"]}, "gpt-4-1106-preview": {"model": "gpt-4-1106-preview", "temperature": 0.0, "max_tokens": 150, "top_p": 1, "frequency_penalty": 0, "presence_penalty": 0, "n": 1, "stop": ["\n\n"]}, "gpt-3.5-turbo-instruct": {"model": "gpt-3.5-turbo-instruct", "temperature": 0.0, "max_tokens": 150, "top_p": 1, "frequency_penalty": 0, "presence_penalty": 0, "n": 1, "stop": ["\n\n"]}, "gpt-3.5-turbo-instruct-penalty": {"model": "gpt-3.5-turbo-instruct", "temperature": 0.0, "max_tokens": 150, "top_p": 1, "frequency_penalty": 0.1, "presence_penalty": 0, "n": 1, "stop": ["\n\n"]}, "qwen3-0.6b": {"url": "http://localhost:8082", "model": "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-0.6B", "hf_device_map": {"": "cuda:0"}, "temperature": 0.0, "max_tokens": 150, "top_p": 1, "frequency_penalty": 0, "presence_penalty": 0, "n": 1, "stop": ["\n\n"]}}