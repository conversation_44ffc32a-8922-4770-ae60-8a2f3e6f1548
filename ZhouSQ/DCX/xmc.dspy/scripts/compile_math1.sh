#!/bin/bash

# 使用Qwen模型训练math1数据集
python compile_irera.py \
    --dataset_name math1 \
    --ontology_name math1_data \
    --prior_path /home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data/math1_points_priors.json \
    --ontology_path /home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data/math1_points.txt \
    --infer_signature_name infer_math1 \
    --rank_signature_name rank_math1 \
    --retriever_model_name /home/<USER>/ZhouSQ/DCX/xmc.dspy/all-mpnet-base-v2 \
    --infer_student_model_name qwen3-0.6b \
    --infer_teacher_model_name qwen3-0.6b \
    --rank_student_model_name qwen3-0.6b \
    --rank_teacher_model_name qwen3-0.6b \
    --infer_compile_metric_name rp10 \
    --rank_compile_metric_name rp10 \
    --prior_A 0 \
    --rank_topk 50 \
    --do_validation \
    --do_test \
    --optimizer_name left-to-right \
    --lm_config_path ./lm_config.json 