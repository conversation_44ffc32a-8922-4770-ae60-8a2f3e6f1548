#!/usr/bin/env python3
"""
为math1数据集准备先验概率文件和标签文件
"""

import json
import os
import math
from collections import Counter, defaultdict

def prepare_math1_files():
    """准备math1数据集所需的额外文件"""
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"

    # 读取训练数据
    with open(os.path.join(data_dir, "train.json"), 'r') as f:
        train_data = json.load(f)

    # 收集所有标签并计算频率
    all_labels = []
    for item in train_data:
        all_labels.extend(item["doc_label"])

    total_samples = len(train_data)
    label_counter = Counter(all_labels)

    # 计算先验概率
    priors = defaultdict(
        lambda: 0.0,
        {k: v / total_samples for k, v in label_counter.items()}
    )

    # 保存先验概率到文件
    with open(os.path.join(data_dir, "math1_priors.json"), 'w') as f:
        json.dump(dict(priors), f, ensure_ascii=False, indent=2)

    # 保存所有唯一标签到文件
    unique_labels = list(set(all_labels))
    with open(os.path.join(data_dir, "math1_labels.txt"), 'w') as f:
        for label in unique_labels:
            f.write(f"{label}\n")

    print(f"处理完成:")
    print(f"- 训练样本数: {total_samples}")
    print(f"- 唯一标签数: {len(unique_labels)}")
    print(f"- 总标签实例数: {len(all_labels)}")
    print(f"- 先验概率文件已保存到: {os.path.join(data_dir, 'math1_priors.json')}")
    print(f"- 标签文件已保存到: {os.path.join(data_dir, 'math1_labels.txt')}")

def generate_prior_probabilities_for_points():
    """为math1_points.txt中的每个标签生成对应的先验概率"""
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"

    # 读取math1_points.txt中的标签
    points_file = os.path.join(data_dir, "math1_points.txt")
    with open(points_file, 'r', encoding='utf-8') as f:
        point_labels = [line.strip() for line in f if line.strip()]

    # 读取现有的先验概率
    priors_file = os.path.join(data_dir, "math1_priors.json")
    with open(priors_file, 'r', encoding='utf-8') as f:
        all_priors = json.load(f)

    # 为每个标签生成先验概率
    point_priors = {}
    missing_labels = []

    for label in point_labels:
        if label in all_priors:
            point_priors[label] = all_priors[label]
        else:
            # 如果标签不在训练数据中，设置一个很小的默认先验概率
            point_priors[label] = 1e-6
            missing_labels.append(label)

    # 保存math1_points的先验概率
    points_priors_file = os.path.join(data_dir, "math1_points_priors.json")
    with open(points_priors_file, 'w', encoding='utf-8') as f:
        json.dump(point_priors, f, ensure_ascii=False, indent=2)

    print(f"\n为math1_points.txt生成先验概率:")
    print(f"- 处理标签数: {len(point_labels)}")
    print(f"- 找到先验概率的标签数: {len(point_labels) - len(missing_labels)}")
    print(f"- 缺失先验概率的标签数: {len(missing_labels)}")
    print(f"- 先验概率文件已保存到: {points_priors_file}")

    if missing_labels:
        print(f"- 缺失的标签（前10个）: {missing_labels[:10]}")
        if len(missing_labels) > 10:
            print(f"  ... 还有 {len(missing_labels) - 10} 个标签")

def apply_prior_adjustment(original_score, prior_prob, A=1.0):
    """
    应用先验概率调整公式: ŝᵢ = sᵢ · log₁₀(A · pᵢ + 10)

    Args:
        original_score: 原始分数 sᵢ
        prior_prob: 先验概率 pᵢ
        A: 调整参数，默认为1.0

    Returns:
        调整后的分数 ŝᵢ
    """
    if prior_prob <= 0:
        prior_prob = 1e-6  # 避免log(0)

    adjustment_factor = math.log10(A * prior_prob + 10)
    adjusted_score = original_score * adjustment_factor

    return adjusted_score

def demonstrate_prior_adjustment():
    """演示先验概率调整的效果"""
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"

    # 读取math1_points的先验概率
    points_priors_file = os.path.join(data_dir, "math1_points_priors.json")
    if not os.path.exists(points_priors_file):
        print("请先运行 generate_prior_probabilities_for_points() 生成先验概率文件")
        return

    with open(points_priors_file, 'r', encoding='utf-8') as f:
        point_priors = json.load(f)

    print(f"\n先验概率调整演示:")
    print(f"公式: ŝᵢ = sᵢ · log₁₀(A · pᵢ + 10)")
    print(f"其中 A=1.0 (调整参数)")
    print("-" * 80)

    # 选择几个不同先验概率的标签进行演示
    sample_labels = list(point_priors.keys())[:10]
    original_score = 0.5  # 假设原始分数为0.5

    print(f"{'标签':<30} {'先验概率':<12} {'调整因子':<12} {'原始分数':<10} {'调整后分数':<12}")
    print("-" * 80)

    for label in sample_labels:
        prior_prob = point_priors[label]
        adjusted_score = apply_prior_adjustment(original_score, prior_prob)
        adjustment_factor = math.log10(1.0 * prior_prob + 10)

        print(f"{label[:28]:<30} {prior_prob:<12.6f} {adjustment_factor:<12.6f} {original_score:<10.3f} {adjusted_score:<12.6f}")

def create_score_adjustment_example():
    """创建一个完整的分数调整示例，展示不同先验概率对分数的影响"""
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"

    # 读取math1_points的先验概率
    points_priors_file = os.path.join(data_dir, "math1_points_priors.json")
    if not os.path.exists(points_priors_file):
        print("请先运行 generate_prior_probabilities_for_points() 生成先验概率文件")
        return

    with open(points_priors_file, 'r', encoding='utf-8') as f:
        point_priors = json.load(f)

    # 按先验概率排序，选择高、中、低概率的标签
    sorted_labels = sorted(point_priors.items(), key=lambda x: x[1], reverse=True)

    high_prob_labels = sorted_labels[:5]  # 前5个高概率标签
    mid_prob_labels = sorted_labels[len(sorted_labels)//2:len(sorted_labels)//2+5]  # 中等概率标签
    low_prob_labels = sorted_labels[-5:]  # 后5个低概率标签

    print(f"\n分数调整效果对比:")
    print(f"公式: ŝᵢ = sᵢ · log₁₀(A · pᵢ + 10), A=1.0")
    print("=" * 100)

    # 测试不同的原始分数
    test_scores = [0.1, 0.3, 0.5, 0.7, 0.9]

    for category, labels in [("高概率标签", high_prob_labels),
                           ("中等概率标签", mid_prob_labels),
                           ("低概率标签", low_prob_labels)]:
        print(f"\n{category}:")
        print("-" * 100)
        print(f"{'标签':<25} {'先验概率':<12} {'原始分数→调整后分数':<50}")
        print("-" * 100)

        for label, prior_prob in labels:
            score_adjustments = []
            for score in test_scores:
                adjusted = apply_prior_adjustment(score, prior_prob)
                score_adjustments.append(f"{score:.1f}→{adjusted:.3f}")

            adjustments_str = " | ".join(score_adjustments)
            print(f"{label[:23]:<25} {prior_prob:<12.6f} {adjustments_str}")

def save_adjustment_factors():
    """保存所有标签的调整因子到文件"""
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"

    # 读取math1_points的先验概率
    points_priors_file = os.path.join(data_dir, "math1_points_priors.json")
    if not os.path.exists(points_priors_file):
        print("请先运行 generate_prior_probabilities_for_points() 生成先验概率文件")
        return

    with open(points_priors_file, 'r', encoding='utf-8') as f:
        point_priors = json.load(f)

    # 计算每个标签的调整因子
    adjustment_factors = {}
    A = 1.0  # 调整参数

    for label, prior_prob in point_priors.items():
        if prior_prob <= 0:
            prior_prob = 1e-6
        adjustment_factor = math.log10(A * prior_prob + 10)
        adjustment_factors[label] = adjustment_factor

    # 保存调整因子
    factors_file = os.path.join(data_dir, "math1_points_adjustment_factors.json")
    with open(factors_file, 'w', encoding='utf-8') as f:
        json.dump(adjustment_factors, f, ensure_ascii=False, indent=2)

    print(f"\n调整因子已保存:")
    print(f"- 文件路径: {factors_file}")
    print(f"- 标签数量: {len(adjustment_factors)}")

    # 统计调整因子的分布
    factors_values = list(adjustment_factors.values())
    print(f"- 调整因子范围: {min(factors_values):.6f} ~ {max(factors_values):.6f}")
    print(f"- 平均调整因子: {sum(factors_values)/len(factors_values):.6f}")

    return adjustment_factors

def apply_prior_adjustment_to_scores(scores_dict, A=1.0):
    """
    对一组分数应用先验概率调整

    Args:
        scores_dict: 字典，键为标签名，值为原始分数
        A: 调整参数，默认为1.0

    Returns:
        调整后的分数字典
    """
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"

    # 读取先验概率
    points_priors_file = os.path.join(data_dir, "math1_points_priors.json")
    if not os.path.exists(points_priors_file):
        print("错误: 先验概率文件不存在，请先运行 generate_prior_probabilities_for_points()")
        return scores_dict

    with open(points_priors_file, 'r', encoding='utf-8') as f:
        point_priors = json.load(f)

    adjusted_scores = {}

    for label, original_score in scores_dict.items():
        if label in point_priors:
            prior_prob = point_priors[label]
            adjusted_score = apply_prior_adjustment(original_score, prior_prob, A)
            adjusted_scores[label] = adjusted_score
        else:
            # 如果标签不在先验概率中，使用原始分数
            adjusted_scores[label] = original_score
            print(f"警告: 标签 '{label}' 不在先验概率文件中，使用原始分数")

    return adjusted_scores

def create_usage_example():
    """创建使用示例"""
    print("\n" + "="*80)
    print("使用示例:")
    print("="*80)

    # 示例分数
    example_scores = {
        "小数的认识": 0.8,
        "分数的意义": 0.7,
        "初识1-10": 0.6,
        "画图游戏": 0.5,
        "感知数与量": 0.4
    }

    print("原始分数:")
    for label, score in example_scores.items():
        print(f"  {label}: {score}")

    # 应用先验概率调整
    adjusted_scores = apply_prior_adjustment_to_scores(example_scores)

    print("\n调整后分数:")
    for label in example_scores.keys():
        original = example_scores[label]
        adjusted = adjusted_scores[label]
        improvement = ((adjusted - original) / original) * 100
        print(f"  {label}: {original} → {adjusted:.6f} (变化: {improvement:+.4f}%)")

def print_usage_instructions():
    """打印使用说明"""
    print("\n" + "="*80)
    print("使用说明:")
    print("="*80)
    print("""
1. 生成的文件:
   - math1_points_priors.json: 每个标签的先验概率
   - math1_points_adjustment_factors.json: 每个标签的调整因子

2. 先验概率调整公式:
   ŝᵢ = sᵢ · log₁₀(A · pᵢ + 10)

   其中:
   - sᵢ: 原始分数
   - pᵢ: 先验概率
   - A: 调整参数 (默认1.0)
   - ŝᵢ: 调整后分数

3. 在代码中使用:
   ```python
   from scripts.prepare_math1_data import apply_prior_adjustment_to_scores

   # 原始分数
   scores = {"标签1": 0.8, "标签2": 0.6}

   # 应用先验概率调整
   adjusted_scores = apply_prior_adjustment_to_scores(scores, A=1.0)
   ```

4. 调整效果:
   - 高频标签 (高先验概率): 分数会略微增加
   - 低频标签 (低先验概率): 分数基本不变
   - 调整幅度通常很小，但能够反映标签的重要性
""")

if __name__ == "__main__":
    prepare_math1_files()
    generate_prior_probabilities_for_points()
    demonstrate_prior_adjustment()
    create_score_adjustment_example()
    save_adjustment_factors()
    create_usage_example()
    print_usage_instructions()