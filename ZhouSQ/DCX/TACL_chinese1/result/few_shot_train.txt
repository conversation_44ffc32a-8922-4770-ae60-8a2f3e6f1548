====================
start_time 2025-08-06 17:31:37.470780
end_time 2025-08-07 08:52:58.187382	model bert	model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext	result_file /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/few_shot_train.txt	multi_label 0	multi_verb 1	constraint_loss 0	constraint_alpha 0.9	lm_training 1	lm_alpha 0.7	lr 3e-05	lr2 0.0001	contrastive_loss 0	contrastive_alpha 0.9	contrastive_level 1	batch_size 64	depth 7	multi_mask 1	dropout 0.1	shuffle False	contrastive_logits 0	cs_mode 0	dataset wos	eval_mode 0	use_hier_mean 1	freeze_plm 0	use_scheduler1 1	use_scheduler2 1	imbalanced_weight True	imbalanced_weight_reverse True	device 1	use_multi_gpu True	use_fp16 False	max_grad_norm 1.0	max_seq_lens 512	use_new_ct 1	use_dropout_sim 0	use_withoutWrappedLM False	mean_verbalizer True	shot 30	label_description 0	seed 171	plm_eval_mode False	verbalizer soft	template_id 0	not_manual False	gradient_accumulation_steps 1	max_epochs 30	early_stop 5	eval_full 0	
best_macro macro_f1: 0.06916056116573367	micro_f1: 0.5813027552157987	acc: 0.5813027552157987	
best_micro macro_f1: 0.06916056116573367	micro_f1: 0.5813027552157987	acc: 0.5813027552157987	

====================
start_time 2025-09-23 15:10:34.697564
end_time 2025-09-23 15:16:54.683665	model bert	model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext	result_file /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/few_shot_train.txt	multi_label 0	multi_verb 1	constraint_loss 0	constraint_alpha 0.9	lm_training 1	lm_alpha 0.7	lr 3e-05	lr2 0.0001	contrastive_loss 0	contrastive_alpha 0.9	contrastive_level 1	batch_size 32	depth 7	multi_mask 1	dropout 0.1	shuffle False	contrastive_logits 0	cs_mode 0	dataset wos	eval_mode 0	use_hier_mean 1	freeze_plm 0	use_scheduler1 1	use_scheduler2 1	imbalanced_weight True	imbalanced_weight_reverse True	device 1	max_grad_norm 1.0	max_seq_lens 512	use_new_ct 1	use_dropout_sim 0	use_withoutWrappedLM False	mean_verbalizer True	shot 30	label_description 0	seed 171	plm_eval_mode False	verbalizer soft	template_id 0	not_manual False	gradient_accumulation_steps 1	max_epochs 50	early_stop 10	eval_full 0	
best_macro macro_f1: 0.23082763312096002	micro_f1: 0.769123179318993	acc: 0.769123179318993	


====================
start_time 2025-09-23 15:18:21.753685
end_time 2025-09-23 15:18:55.608565	model bert	model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext	result_file /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/few_shot_train.txt	multi_label 0	multi_verb 1	constraint_loss 0	constraint_alpha 0.9	lm_training 1	lm_alpha 0.7	lr 3e-05	lr2 0.0001	contrastive_loss 0	contrastive_alpha 0.9	contrastive_level 1	batch_size 8	depth 7	multi_mask 1	dropout 0.1	shuffle False	contrastive_logits 0	cs_mode 0	dataset wos	eval_mode 0	use_hier_mean 1	freeze_plm 0	use_scheduler1 1	use_scheduler2 1	imbalanced_weight True	imbalanced_weight_reverse True	device 1	max_grad_norm 1.0	max_seq_lens 512	use_new_ct 1	use_dropout_sim 0	use_withoutWrappedLM False	mean_verbalizer True	shot 30	label_description 0	seed 171	plm_eval_mode False	verbalizer soft	template_id 0	not_manual False	gradient_accumulation_steps 1	max_epochs 50	early_stop 10	eval_full 0	topk 3	
best_macro macro_f1: 0.14000274240970612	micro_f1: 0.6647555343207517	

