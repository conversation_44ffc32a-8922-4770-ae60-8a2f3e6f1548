[{"confidence_threshold": 0.0, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.01, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.02, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.03, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.04, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.05, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.06, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.07, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.08, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.09, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.1, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.11, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.12, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.13, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.14, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.15, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.16, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.17, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.18, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.19, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.2, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.21, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.22, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.23, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.24, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.25, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.26, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.27, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.28, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.29, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.3, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.31, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.32, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.33, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.34, "precision": 0.1486997635933806, "recall": 0.7012263099219621, "f1": 0.2453676614004291, "true_positives": 629, "false_positives": 3601, "false_negatives": 268}, {"confidence_threshold": 0.35, "precision": 0.14853358561967833, "recall": 0.7001114827201784, "f1": 0.2450731707317073, "true_positives": 628, "false_positives": 3600, "false_negatives": 269}, {"confidence_threshold": 0.36, "precision": 0.14885043849253377, "recall": 0.7001114827201784, "f1": 0.24550430023455821, "true_positives": 628, "false_positives": 3591, "false_negatives": 269}, {"confidence_threshold": 0.37, "precision": 0.14939242315939957, "recall": 0.6989966555183946, "f1": 0.24617196702002356, "true_positives": 627, "false_positives": 3570, "false_negatives": 270}, {"confidence_threshold": 0.38, "precision": 0.15055315055315055, "recall": 0.6978818283166109, "f1": 0.247675568743818, "true_positives": 626, "false_positives": 3532, "false_negatives": 271}, {"confidence_threshold": 0.39, "precision": 0.1524390243902439, "recall": 0.6967670011148273, "f1": 0.25015009005403244, "true_positives": 625, "false_positives": 3475, "false_negatives": 272}, {"confidence_threshold": 0.4, "precision": 0.15430414289258249, "recall": 0.693422519509476, "f1": 0.2524350649350649, "true_positives": 622, "false_positives": 3409, "false_negatives": 275}, {"confidence_threshold": 0.41, "precision": 0.15716096324461343, "recall": 0.6911928651059086, "f1": 0.2560925237505163, "true_positives": 620, "false_positives": 3325, "false_negatives": 277}, {"confidence_threshold": 0.42, "precision": 0.16151022548505506, "recall": 0.6867335562987736, "f1": 0.26151560178306094, "true_positives": 616, "false_positives": 3198, "false_negatives": 281}, {"confidence_threshold": 0.43, "precision": 0.1648052301825116, "recall": 0.6744704570791528, "f1": 0.26488616462346765, "true_positives": 605, "false_positives": 3066, "false_negatives": 292}, {"confidence_threshold": 0.44, "precision": 0.1717579250720461, "recall": 0.6644370122630993, "f1": 0.2729562628806961, "true_positives": 596, "false_positives": 2874, "false_negatives": 301}, {"confidence_threshold": 0.45, "precision": 0.1786043652013526, "recall": 0.6477146042363434, "f1": 0.27999999999999997, "true_positives": 581, "false_positives": 2672, "false_negatives": 316}, {"confidence_threshold": 0.46, "precision": 0.18546042003231017, "recall": 0.6399108138238573, "f1": 0.2875751503006012, "true_positives": 574, "false_positives": 2521, "false_negatives": 323}, {"confidence_threshold": 0.47, "precision": 0.1887246476452389, "recall": 0.6120401337792643, "f1": 0.2884918549658434, "true_positives": 549, "false_positives": 2360, "false_negatives": 348}, {"confidence_threshold": 0.48, "precision": 0.19619326500732065, "recall": 0.5975473801560758, "f1": 0.2953981813171673, "true_positives": 536, "false_positives": 2196, "false_negatives": 361}, {"confidence_threshold": 0.49, "precision": 0.2066536203522505, "recall": 0.5886287625418061, "f1": 0.305909617612978, "true_positives": 528, "false_positives": 2027, "false_negatives": 369}, {"confidence_threshold": 0.5, "precision": 0.2139282735613011, "recall": 0.5719063545150501, "f1": 0.3113808801213961, "true_positives": 513, "false_positives": 1885, "false_negatives": 384}, {"confidence_threshold": 0.51, "precision": 0.22476446837146702, "recall": 0.5585284280936454, "f1": 0.3205374280230326, "true_positives": 501, "false_positives": 1728, "false_negatives": 396}, {"confidence_threshold": 0.52, "precision": 0.23203087313072843, "recall": 0.5362318840579711, "f1": 0.3239057239057239, "true_positives": 481, "false_positives": 1592, "false_negatives": 416}, {"confidence_threshold": 0.53, "precision": 0.24438642297650132, "recall": 0.5217391304347826, "f1": 0.3328591749644381, "true_positives": 468, "false_positives": 1447, "false_negatives": 429}, {"confidence_threshold": 0.54, "precision": 0.257256687535572, "recall": 0.5039018952062431, "f1": 0.3406179351921628, "true_positives": 452, "false_positives": 1305, "false_negatives": 445}, {"confidence_threshold": 0.55, "precision": 0.268443893366398, "recall": 0.48272017837235226, "f1": 0.3450199203187251, "true_positives": 433, "false_positives": 1180, "false_negatives": 464}, {"confidence_threshold": 0.56, "precision": 0.2872777017783858, "recall": 0.4682274247491639, "f1": 0.3560830860534125, "true_positives": 420, "false_positives": 1042, "false_negatives": 477}, {"confidence_threshold": 0.57, "precision": 0.3097412480974125, "recall": 0.4537346711259755, "f1": 0.36815920398009955, "true_positives": 407, "false_positives": 907, "false_negatives": 490}, {"confidence_threshold": 0.58, "precision": 0.3333333333333333, "recall": 0.4269788182831661, "f1": 0.3743890518084066, "true_positives": 383, "false_positives": 766, "false_negatives": 514}, {"confidence_threshold": 0.59, "precision": 0.3607532210109019, "recall": 0.4057971014492754, "f1": 0.38195173137460653, "true_positives": 364, "false_positives": 645, "false_negatives": 533}, {"confidence_threshold": 0.6, "precision": 0.38530066815144765, "recall": 0.3857302118171683, "f1": 0.38551532033426184, "true_positives": 346, "false_positives": 552, "false_negatives": 551}, {"confidence_threshold": 0.61, "precision": 0.4005037783375315, "recall": 0.35451505016722407, "f1": 0.37610881135422825, "true_positives": 318, "false_positives": 476, "false_negatives": 579}, {"confidence_threshold": 0.62, "precision": 0.42814814814814817, "recall": 0.3221850613154961, "f1": 0.3676844783715013, "true_positives": 289, "false_positives": 386, "false_negatives": 608}, {"confidence_threshold": 0.63, "precision": 0.45518453427065025, "recall": 0.2887402452619844, "f1": 0.35334242837653473, "true_positives": 259, "false_positives": 310, "false_negatives": 638}, {"confidence_threshold": 0.64, "precision": 0.501039501039501, "recall": 0.2686733556298774, "f1": 0.3497822931785196, "true_positives": 241, "false_positives": 240, "false_negatives": 656}, {"confidence_threshold": 0.65, "precision": 0.51338199513382, "recall": 0.23522853957636566, "f1": 0.3226299694189602, "true_positives": 211, "false_positives": 200, "false_negatives": 686}, {"confidence_threshold": 0.66, "precision": 0.5289017341040463, "recall": 0.2040133779264214, "f1": 0.2944489139179405, "true_positives": 183, "false_positives": 163, "false_negatives": 714}, {"confidence_threshold": 0.67, "precision": 0.5294117647058824, "recall": 0.1705685618729097, "f1": 0.2580101180438448, "true_positives": 153, "false_positives": 136, "false_negatives": 744}, {"confidence_threshold": 0.68, "precision": 0.5550660792951542, "recall": 0.14046822742474915, "f1": 0.22419928825622773, "true_positives": 126, "false_positives": 101, "false_negatives": 771}, {"confidence_threshold": 0.69, "precision": 0.5789473684210527, "recall": 0.12263099219620958, "f1": 0.20239190432382706, "true_positives": 110, "false_positives": 80, "false_negatives": 787}, {"confidence_threshold": 0.7, "precision": 0.5526315789473685, "recall": 0.09364548494983277, "f1": 0.16015252621544326, "true_positives": 84, "false_positives": 68, "false_negatives": 813}, {"confidence_threshold": 0.71, "precision": 0.5241935483870968, "recall": 0.07246376811594203, "f1": 0.12732615083251714, "true_positives": 65, "false_positives": 59, "false_negatives": 832}, {"confidence_threshold": 0.72, "precision": 0.4945054945054945, "recall": 0.05016722408026756, "f1": 0.09109311740890688, "true_positives": 45, "false_positives": 46, "false_negatives": 852}, {"confidence_threshold": 0.73, "precision": 0.5352112676056338, "recall": 0.042363433667781496, "f1": 0.07851239669421488, "true_positives": 38, "false_positives": 33, "false_negatives": 859}, {"confidence_threshold": 0.74, "precision": 0.5666666666666667, "recall": 0.0379041248606466, "f1": 0.07105538140020898, "true_positives": 34, "false_positives": 26, "false_negatives": 863}, {"confidence_threshold": 0.75, "precision": 0.5116279069767442, "recall": 0.024526198439241916, "f1": 0.04680851063829788, "true_positives": 22, "false_positives": 21, "false_negatives": 875}, {"confidence_threshold": 0.76, "precision": 0.48717948717948717, "recall": 0.021181716833890748, "f1": 0.0405982905982906, "true_positives": 19, "false_positives": 20, "false_negatives": 878}, {"confidence_threshold": 0.77, "precision": 0.2916666666666667, "recall": 0.007803790412486065, "f1": 0.01520086862106406, "true_positives": 7, "false_positives": 17, "false_negatives": 890}, {"confidence_threshold": 0.78, "precision": 0.2916666666666667, "recall": 0.007803790412486065, "f1": 0.01520086862106406, "true_positives": 7, "false_positives": 17, "false_negatives": 890}, {"confidence_threshold": 0.79, "precision": 0.4, "recall": 0.004459308807134894, "f1": 0.008820286659316428, "true_positives": 4, "false_positives": 6, "false_negatives": 893}, {"confidence_threshold": 0.8, "precision": 1.0, "recall": 0.0011148272017837235, "f1": 0.0022271714922048997, "true_positives": 1, "false_positives": 0, "false_negatives": 896}, {"confidence_threshold": 0.81, "precision": 1.0, "recall": 0.0011148272017837235, "f1": 0.0022271714922048997, "true_positives": 1, "false_positives": 0, "false_negatives": 896}, {"confidence_threshold": 0.82, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.83, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.84, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.85, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.86, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.87, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.88, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.89, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.9, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.91, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.92, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.93, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.94, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.95, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.96, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.97, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.98, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.99, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.0, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.01, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.02, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.03, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.04, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}]