import torch
import torch.nn.functional as F
import random

flag_imbalanced_contrastive_loss = False
flag_imbalanced_weight_reverse = False
flag_print_loss_weight = False


def sim(x, y):
    norm_x = F.normalize(x, dim=-1)
    norm_y = F.normalize(y, dim=-1)
    return torch.matmul(norm_x, norm_y.transpose(1, 0))

# flat contrastive_loss 优化版
def flat_contrastive_loss_func(label_sim, hier_labels, processor, output_at_mask, 
                               imbalanced_weight=False, depth=2, contrastive_level=1,
                               imbalanced_weight_reverse=True, use_cuda=True, text_contents=None):
    global flag_imbalanced_contrastive_loss, flag_imbalanced_weight_reverse, flag_print_loss_weight

    if use_cuda:
        output_at_mask = output_at_mask.cuda()

    cur_batch_size = output_at_mask.size(0)
    assert cur_batch_size == len(hier_labels[0])

    loss_ins = 0

    # 层级损失权重
    if not imbalanced_weight:
        loss_weight = [1.0 for _ in range(depth)]
    else:
        if not flag_imbalanced_contrastive_loss:
            print(f"[Info] Using imbalanced contrastive loss with contrastive_level: {contrastive_level}")
            flag_imbalanced_contrastive_loss = True
        loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth)]

    if imbalanced_weight_reverse:
        if not flag_imbalanced_weight_reverse:
            print("[Info] Imbalanced weight reversed")
            flag_imbalanced_weight_reverse = True
        loss_weight.reverse()

    if not flag_print_loss_weight:
        print(f"[Info] Loss weights: {loss_weight}")
        flag_print_loss_weight = True

    device = output_at_mask.device

    for mask_idx in range(depth):
        cur_output = output_at_mask[:, mask_idx, :]  # [B, 768]
        sim_score = torch.exp(torch.matmul(cur_output, cur_output.T))  # [B, B]
        sim_score = sim(cur_output, cur_output)
        sim_score = torch.exp(sim_score)
        cur_loss_weight = loss_weight[depth - 1 - mask_idx:]

        for cur_depth in range(mask_idx):
            # [B] labels at current level
            cur_hier_labels = torch.tensor(hier_labels[cur_depth], device=device)

            # 构造相等矩阵 (positive: 1, negative: -1)
            label_eq = cur_hier_labels.unsqueeze(0) == cur_hier_labels.unsqueeze(1)
            cur_hier_matrix = torch.where(label_eq, torch.ones_like(label_eq, dtype=torch.float), -1 * torch.ones_like(label_eq, dtype=torch.float))

            # 忽略相同文本内容
            if text_contents is not None:
                # 生成 [B, B] 矩阵，若相同文本内容则设为 0
                text_tensor = torch.tensor([hash(x) for x in text_contents], device=device)
                same_text = text_tensor.unsqueeze(0) == text_tensor.unsqueeze(1)
                ignore_mask = same_text & (~torch.eye(cur_batch_size, dtype=torch.bool, device=device))
                cur_hier_matrix[ignore_mask] = 0

            # 倒排表提前构造
            if cur_depth > 2:
                offset = sum(len(l) for l in processor.label_list[:cur_depth])
                label_vals = (cur_hier_labels + offset).tolist()
                value_to_key_map = {v: k for k, v in processor.level_sim_matrix_list[cur_depth].items()}

                for i, label0 in enumerate(label_vals):
                    k = value_to_key_map.get(label0, None)
                    if k is None:
                        continue
                    _, top_indices = torch.topk(label_sim[cur_depth][k], (cur_depth - 2) * 30, largest=True)

                    mapped_labels = []
                    for idx in top_indices.tolist():
                        mapped = processor.level_sim_matrix_list[cur_depth].get(idx, None)
                        if mapped is not None:
                            mapped_labels.append(mapped - offset)

                    if mapped_labels:
                        mapped_labels_tensor = torch.tensor(mapped_labels, device=device)
                        mask = (cur_hier_labels.unsqueeze(1) == mapped_labels_tensor).any(dim=1)
                        cur_hier_matrix[i][mask] = 0

            # 随机过滤负样本（将 -1 的两项随机置为 0）
            neg_mask = cur_hier_matrix == -1
            for i in range(cur_batch_size):
                neg_indices = neg_mask[i].nonzero(as_tuple=True)[0]
                if neg_indices.size(0) > 2:
                    perm = torch.randperm(neg_indices.size(0), device=device)[:2]
                    to_ignore = neg_indices[perm]
                    cur_hier_matrix[i, to_ignore] = 0

            # Loss 计算
            pos_mask = (cur_hier_matrix == 1)
            neg_mask = (cur_hier_matrix == 0)

            pos_sim = (sim_score * pos_mask).sum(dim=1)
            neg_sim = (sim_score * neg_mask).sum(dim=1)

            valid = pos_sim > 0  # 避免 log(0)
            eps = 1e-12
            loss_vec = torch.zeros_like(pos_sim)
            loss_vec[valid] = -torch.log(pos_sim[valid] / (pos_sim[valid] + neg_sim[valid] + eps))

            cur_loss_ins = loss_vec.sum()
            loss_ins += cur_loss_ins * cur_loss_weight[cur_depth]

    loss_ins = loss_ins / (cur_batch_size ** 2)
    return loss_ins


# flat contrastive_loss
# def flat_contrastive_loss_func(label_sim, hier_labels, processor, output_at_mask, imbalanced_weight=False, depth=2,
#                                              contrastive_level=1,
#                                              imbalanced_weight_reverse=True, use_cuda=True, text_contents=None):
#     global flag_imbalanced_contrastive_loss, flag_imbalanced_weight_reverse, flag_print_loss_weight
#     ## output_at_mask = [batch_size, multi_mask, 768]
#     if use_cuda:
#         output_at_mask = output_at_mask.cuda()
#     cur_batch_size = output_at_mask.shape[0]
#     assert cur_batch_size == len(hier_labels[0])

#     loss_ins = 0
#     # 不同层的权重，默认同等地位
#     if not imbalanced_weight:
#         loss_weight = [1 for i in range(depth)]
#     else:
#         if not flag_imbalanced_contrastive_loss:
#             print(f"using imbalanced contrastive loss with contrastive_level:{contrastive_level}")
#             flag_imbalanced_contrastive_loss = True
#         loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth)]

#     if imbalanced_weight_reverse:
#         if not flag_imbalanced_weight_reverse:
#             print("imbalanced weight reversed ")
#             flag_imbalanced_weight_reverse = True

#         loss_weight.reverse()
#     if not flag_print_loss_weight:
#         print("loss_weight:", loss_weight)
#         flag_print_loss_weight = True

#     for mask_idx in range(depth):
#         # shape: [batch_size, 768]
#         cur_output_at_mask = output_at_mask[:, mask_idx, :]
#         sim_score = sim(cur_output_at_mask, cur_output_at_mask)
#         sim_score = torch.exp(sim_score)
#         cur_loss_weight = loss_weight[depth - 1 - mask_idx:]

#         for cur_depth in range(mask_idx):

#             cur_loss_ins = 0

#             cur_hier_matrix = torch.zeros(cur_batch_size, cur_batch_size)

#             cur_hier_labels = hier_labels[cur_depth]

#             for i in range(len(cur_hier_labels)):
#                 tmp = cur_hier_labels[i]
#                 for j in range(len(cur_hier_labels)):
#                     # 首先检查是否为相同文本内容，如果是则跳过（既不作为正样本也不作为负样本）
#                     if text_contents is not None and text_contents[i] == text_contents[j] and i != j:
#                         cur_hier_matrix[i][j] = 0  # 相同文本内容的样本设为0（忽略）
#                         continue

#                     if isinstance(tmp, list):
#                         flag = False
#                         if len(set(tmp).intersection(set(cur_hier_labels[j]))) != 0:
#                             flag = True
#                         # positive samples with the same label/description
#                         if flag:
#                             cur_hier_matrix[i][j] = 1
#                         else:
#                             cur_hier_matrix[i][j] = -1
#                     else:
#                         # positive samples with the same label/description
#                         if cur_hier_labels[j] == tmp:
#                             cur_hier_matrix[i][j] = 1
#                         else:
#                             cur_hier_matrix[i][j] = -1
                    
#                     # if cur_depth > 2:
#                     #     # nagetives part1: 基于标签相似度的负样本采样

#                     #     value_to_key_map = {}
#                     #     for k, v in processor.level_sim_matrix_list[cur_depth].items():
#                     #         value_to_key_map[v] = k

#                     #     offset = sum(len(l) for l in processor.label_list[:cur_depth])
#                     #     label0 = cur_hier_labels[i].item() + offset

#                     #     k = value_to_key_map.get(label0)
#                     #     if k is None:
#                     #         continue

#                     #     _, indices1 = torch.sort(label_sim[cur_depth][k], descending=True)

#                     #     indices1 = indices1[:(cur_depth-2)*30]
#                     #     for l in indices1:
#                     #         label = processor.level_sim_matrix_list[cur_depth].get(l.item())
#                     #         label -= offset

#                     #         for batch_idx in range(cur_batch_size):
#                     #             if batch_idx < len(cur_hier_labels) and cur_hier_labels[batch_idx] == label:
#                     #                 cur_hier_matrix[i][batch_idx] = 0



#                     if cur_depth > 2:
#                         value_to_key_map = {}
#                         for k, v in processor.level_sim_matrix_list[cur_depth].items():
#                             value_to_key_map[v] = k
#                         # 预处理
#                         offset = sum(len(l) for l in processor.label_list[:cur_depth])
#                         label0 = cur_hier_labels[i].item() + offset

#                         # 使用倒排索引快速查找
#                         k = value_to_key_map.get(label0)
#                         if k is None:
#                             continue

#                         _, indices1 = torch.sort(label_sim[cur_depth][k], descending=True)
#                         indices1 = indices1[:(cur_depth - 2) * 30]

#                         for l in indices1:
#                             mapped_label = processor.level_sim_matrix_list[cur_depth].get(l.item())
#                             if mapped_label is None:
#                                 continue

#                             mapped_label -= offset

#                             batch_idx_tensor = (cur_hier_labels == mapped_label).nonzero(as_tuple=True)[0]
#                             cur_hier_matrix[i, batch_idx_tensor] = 0

#                     # nagetives part1: 基于标签相似度的负样本采样
#                     # _, indices1 = torch.sort(label_sim[i], descending=True)
#                     # indices1 = indices1[:30]
#                     # for l in indices1:
#                     #     # 确保不将相同文本内容的样本作为负样本
#                     #     if text_contents is None or text_contents[i] != text_contents[l]:
#                     #         cur_hier_matrix[i][l] = 0

#                     # nagetives part2: 随机负样本采样

#             for i in range(len(cur_hier_matrix)):
#                 row = cur_hier_matrix[i]

#                 # 找出所有值为 -1 的列索引（即负样本的位置）
#                 neg_indices = (row == -1).nonzero(as_tuple=True)[0].tolist()

#                 # 如果负样本数 >= 2，随机选择 2 个；否则就选现有的所有
#                 num_to_ignore = min(2, len(neg_indices))
#                 if num_to_ignore > 0:
#                     indices2 = random.sample(neg_indices, num_to_ignore)

#                     # 把这些位置从 -1 改成 0（忽略）
#                     for l in indices2:
#                         cur_hier_matrix[i][l] = 0
#             # print(cur_hier_matrix)
#             for i in range(len(cur_hier_matrix)):
#                 y_true = cur_hier_matrix[i]
#                 # 如果当前instance A 在当前层级与其他所有instance的label_matrix矩阵值全为0，
#                 # 那么认为此instance A的gold label路径没有延伸至当前depth，故不在当前depth计算对比学习损失
#                 if len(y_true[y_true == 1]) == 0:
#                     continue
#                 # sim.shape = [batch_size, batch_size]
#                 cur_sim_score = sim_score[i]
#                 # sim_score = sim_score - torch.eye(cur_batch_size).cuda() * 1e12
#                 pos_sim = cur_sim_score[y_true == 1].sum()
#                 neg_sim = cur_sim_score[y_true == 0].sum()
#                 cur_loss_ins += - torch.log(pos_sim / (pos_sim + neg_sim))

#             loss_ins += cur_loss_ins * cur_loss_weight[cur_depth]

#     loss_ins = loss_ins / (cur_batch_size ** 2)

#     return loss_ins


def constraint_multi_depth_loss_func(logits, loss_func, hier_labels, processor, args, use_cuda=True, mode=0):
    if isinstance(logits, list):
        leaf_logits = logits[-1]
    elif isinstance(logits, torch.Tensor):
        leaf_logits = logits[:, -1, :]
    contrastive_level = 0
    hier_mapping = processor.hier_mapping
    flat_slot2value = processor.flat_slot2value
    # batch_size * label_size(134)
    depth = len(hier_mapping) + 1

    loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth - 1)]

    leaf_logits = torch.softmax(leaf_logits, dim=-1)
    hier_logits = []
    hier_logits.insert(0, leaf_logits)

    batch_s = leaf_logits.shape[0]
    constraint_loss = 0

    all_length = len(processor.all_labels)
    for depth_idx in range(depth - 2, -1, -1):
        if isinstance(logits, list):
            ori_logits = logits[depth_idx]
        elif isinstance(logits, torch.Tensor):
            ori_logits = logits[:, depth_idx, :]
        ## True
        if args.multi_verb:
            cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))

            for i in range(cur_logits.shape[-1]):
                # sum
                cur_logits[:, i] = torch.sum(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
                # mean
                # cur_logits[:, i] = torch.mean(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
        else:
            cur_logits = torch.zeros(batch_s, all_length)
            cd_labels = processor.depth2label[depth_idx]
            for i in range(all_length):
                if i in cd_labels:
                    cur_logits[:, i] = torch.sum(hier_logits[0][:, list(flat_slot2value[i])], dim=-1)
            # ver.weight.shape  [7+ 64 + 140, 768]
        cur_labels = hier_labels[depth_idx]

        if use_cuda:
            cur_logits = cur_logits.cuda()
            cur_labels = cur_labels.cuda()
        # default mode = 0
        if mode:
            cur_logits = cur_logits + ori_logits

        if args.multi_label:
            cur_multi_label = torch.zeros_like(cur_logits)
            for i in range(cur_multi_label.shape[0]):
                cur_multi_label[i][cur_labels[i]] = 1
            cur_labels = cur_multi_label

            # cur_logits = torch.softmax(cur_logits, dim=-1)
        hier_logits.insert(0, cur_logits)
        cur_constraint_loss = loss_func(cur_logits, cur_labels)
        constraint_loss += cur_constraint_loss * loss_weight[depth_idx]
    return constraint_loss


def multi_path_constraint_multi_depth_loss_func(logits, loss_func, hier_labels, processor, args, use_cuda=True, mode=0):
    contrastive_level = 0
    hier_mapping = processor.hier_mapping

    depth = len(hier_mapping) + 1

    loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth - 1)]

    batch_s = logits[0].shape[0]
    constraint_loss = 0

    for depth_idx in range(depth - 2, -1, -1):
        if isinstance(logits, list):
            pre_logits = logits[depth_idx+1]
            ori_logits = logits[depth_idx]
        elif isinstance(logits, torch.Tensor):
            pre_logits = logits[:, depth_idx+1, :]
            ori_logits = logits[:, depth_idx, :]
        else:
            print(type(logits))
            raise TypeError
        cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))

        for i in range(cur_logits.shape[-1]):
            ## sum
            # cur_logits[:, i] = torch.sum(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
            ## mean
            if len(hier_mapping[depth_idx][0][i]) != 0:
                # ori_logits[:, i] = torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1)
                # ori_logits[:, i] = torch.mean(ori_logits[:, i] + torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1), dim=-1)
                ori_logits[:, i] = ori_logits[:, i] * 0.99 + torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1) * 0.01

        cur_labels = hier_labels[depth_idx]

        if use_cuda:
            ori_logits = ori_logits.cuda()

        cur_multi_label = torch.zeros_like(cur_logits).to("cuda:0")
        for i in range(cur_multi_label.shape[0]):
            for j in cur_labels[i]:
                cur_multi_label[i][j] = 1
        cur_labels = cur_multi_label

        cur_constraint_loss = loss_func(ori_logits, cur_labels)
        constraint_loss += cur_constraint_loss * loss_weight[depth_idx]
    return constraint_loss
