--- Log Start: 2025-09-24 09:56:34 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 6047 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 28257 ------------
length dataset['train']: 28257
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([30, 768])
depth 3: torch.Size([219, 768])
depth 4: torch.Size([898, 768])
depth 5: torch.Size([2157, 768])
depth 6: torch.Size([3306, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
❌ 初始化模型组件失败: 'embedding'
Traceback (most recent call last):
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/predict.py", line 193, in _initialize_model
    'embedding': torch.stack([torch.Tensor(item) for item in embedding_list['embedding']]).to(self.device),
                                                             ~~~~~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'embedding'
❌ 分类器初始化失败: 'embedding'
