#!/bin/bash

# ==============================================================================
# 自动按顺序执行多个任务的脚本
#
# 使用方法:
# 1. 将此代码保存为文件，例如 run_all_tasks.sh
# 2. 赋予执行权限: chmod +x run_all_tasks.sh
# 3. 创建一个 screen 会话并在其中运行脚本，以保证离线后仍可继续运行:
#
#    screen -S math    # 创建并进入一个名为 math 的新会话
#    ./run_all_tasks.sh  # 在会话中启动脚本
#
# 启动后，您可以随时按 Ctrl+A 然后按 D 键来“分离”会话（脚本会在后台继续跑）。
# 当您想再次查看运行时，使用 screen -r math 重新连接即可。
# ==============================================================================

echo "脚本启动于: $(date)"
echo "========================================="
echo ""

# --- 步骤 1: 激活 Conda 环境 ---
echo "[INFO] 正在激活 Conda 环境 'htc'..."
# 确保 conda 命令在脚本中可用
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate htc
echo "[SUCCESS] Conda 环境 'htc' 已激活。"
echo ""


# --- 步骤 2: 运行第一个 Python 任务 ---
echo "[INFO] 任务 1/3: 开始执行 DCL 训练..."
cd /home/<USER>/ZhouSQ/DCX/TACL_math1 || { echo "[ERROR] 无法进入目录 /home/<USER>/ZhouSQ/DCX/TACL_math1"; exit 1; }
python /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/train_tb.py
echo "[SUCCESS] 任务 1/3: DCL 训练完成。"
echo ""


# --- 步骤 3: 运行 LLaMA Factory 训练任务 ---
echo "[INFO] 任务 2/3: 开始执行 LLaMA Factory SFT 训练..."
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory || { echo "[ERROR] 无法进入目录 /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory"; exit 1; }

NCCL_SOCKET_IFNAME="eno1" NCCL_IB_DISABLE="1" NCCL_P2P_DISABLE="1" NCCL_DEBUG="WARN" PYTORCH_CUDA_ALLOC_CONF="expandable_segments:True" llamafactory-cli train \
    --stage sft \
    --do_train True \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B \
    --preprocessing_num_workers 16 \
    --finetuning_type lora \
    --template qwen3 \
    --flash_attn auto \
    --dataset_dir data \
    --dataset math1 \
    --cutoff_len 2048 \
    --learning_rate 5e-05 \
    --num_train_epochs 3.0 \
    --max_samples 100000 \
    --per_device_train_batch_size 4 \
    --gradient_accumulation_steps 8 \
    --lr_scheduler_type cosine \
    --max_grad_norm 1.0 \
    --logging_steps 5 \
    --save_steps 500 \
    --warmup_steps 0 \
    --packing False \
    --enable_thinking True \
    --report_to none \
    --output_dir saves/Qwen3-8B-Thinking/lora/train_2025-09-15-16-54-11 \
    --bf16 True \
    --plot_loss True \
    --trust_remote_code True \
    --ddp_timeout 180000000 \
    --include_num_input_tokens_seen True \
    --optim adamw_torch \
    --lora_rank 16 \
    --lora_alpha 32 \
    --lora_dropout 0 \
    --lora_target all \
    --deepspeed cache/ds_z3_config.json

echo "[SUCCESS] 任务 2/3: LLaMA Factory SFT 训练完成。"
echo ""


# --- 步骤 4: 运行第三个 Python 任务 ---
echo "[INFO] 任务 3/3: 开始运行数学相似度系统..."
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math_similarity_system || { echo "[ERROR] 无法进入目录 /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math_similarity_system"; exit 1; }
python main.py --step all
echo "[SUCCESS] 任务 3/3: 数学相似度系统运行完成。"
echo ""


# --- 脚本结束 ---
echo "========================================="
echo "所有任务已全部执行完毕于: $(date)"