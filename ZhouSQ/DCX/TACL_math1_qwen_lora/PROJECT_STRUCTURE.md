# 项目结构说明

## 📁 整理后的项目结构

```
TACL_math1_qwen_lora/
├── analysis_tools/                    # 分析工具脚本
│   ├── badcase_analysis/              # Badcase分析工具
│   │   └── badcase_analysis.py        # 三模型badcase分析脚本
│   ├── model_ensemble/                # 模型集成分析工具
│   │   ├── analyze_model_ensemble.py  # 双模型集成分析
│   │   └── analyze_three_model_ensemble.py # 三模型集成分析
│   ├── dataset_analysis/              # 数据集分析工具
│   │   └── analyze_dataset_comparison.py # 数据集对比分析
│   ├── evaluation_metrics/            # 评估指标工具
│   │   ├── improved_evaluation_metrics.py # 改进的评估指标
│   │   ├── generate_knowledge_point_metrics.py # 知识点指标生成
│   │   └── generate_embedding_metrics.py # 嵌入指标生成
│   └── unified_predictions/           # 统一预测工具
│       └── unify_model_predictions.py # 统一预测结果脚本
├── results/                           # 分析结果文件
│   ├── badcase_analysis/              # Badcase分析结果
│   │   └── badcase_analysis_detailed.xlsx # 详细badcase分析Excel
│   ├── model_ensemble/                # 模型集成分析结果
│   │   ├── model_ensemble_analysis-num5.xlsx # 5样本集成分析
│   │   ├── model_ensemble_analysis.xlsx # 完整集成分析
│   │   └── three_model_ensemble_analysis.xlsx # 三模型集成分析
│   ├── dataset_analysis/              # 数据集分析结果
│   │   └── dataset_comparison_analysis_20250916_165308.xlsx
│   ├── evaluation_metrics/            # 评估指标结果
│   │   ├── knowledge_point_metrics.xlsx # 知识点指标
│   │   ├── embedding_knowledge_point_metrics.xlsx # 嵌入知识点指标
│   │   ├── improved_evaluation_results_20250916_172304.xlsx
│   │   ├── improved_model_ensemble_analysis.xlsx
│   │   └── incorrect_predictions-*.json # 错误预测记录
│   └── unified_predictions/           # 统一预测结果
│       ├── unified_model_predictions.json # 主要统一预测结果
│       ├── unified_model_predictions_6047.json # 6047样本结果
│       ├── unified_model_predictions_6047_top10.json # 6047样本Top-10结果
│       ├── unified_model_predictions_num10.json # 10样本结果
│       └── unified_model_predictions_num10_top10.json # 10样本Top-10结果
├── scripts/                           # 辅助脚本
│   ├── data_processing/               # 数据处理脚本
│   │   └── convert_datasets.py        # 数据集转换脚本
│   ├── model_training/                # 模型训练脚本 (空)
│   └── evaluation/                    # 评估脚本
│       ├── calculate_accuracy.py      # 准确率计算
│       ├── check_predictions.py       # 预测检查
│       ├── case_study_analysis.py     # 案例研究分析
│       ├── debug_num10_analysis.py    # 调试分析
│       └── matching_demo.py           # 匹配演示
├── docs/                              # 文档
│   ├── reports/                       # 分析报告
│   │   ├── badcase_analysis_report.md # Badcase分析报告
│   │   ├── complete_ensemble_analysis_report.md # 完整集成分析报告
│   │   ├── dataset_analysis_report.md # 数据集分析报告
│   │   ├── final_topk_analysis_report.md # 最终Top-k分析报告
│   │   ├── model_ensemble_report.md   # 模型集成报告
│   │   ├── three_model_ensemble_report.md # 三模型集成报告
│   │   ├── three_model_topk_analysis_report.md # 三模型Top-k分析报告
│   │   ├── top10_analysis_report.md   # Top-10分析报告
│   │   ├── topk_analysis_report.md    # Top-k分析报告
│   │   ├── unified_predictions_report.md # 统一预测报告
│   │   └── unified_predictions_topk_report.md # 统一预测Top-k报告
│   └── guides/                        # 使用指南
│       ├── 运行指令.md                 # 运行指令说明
│       └── matching_strategy_analysis.md # 匹配策略分析
├── math_data/                         # 数学数据集
├── math_similarity_system/            # 数学相似度系统
├── LLaMA-Factory/                     # LLaMA-Factory框架
├── TACL-result/                       # TACL项目结果
├── Qwen3-*/                          # Qwen3模型文件
└── PROJECT_STRUCTURE.md              # 本文件
```

## 🎯 功能模块说明

### 1. **analysis_tools/** - 分析工具
包含所有的分析脚本，按功能分类：

#### **badcase_analysis/**
- `badcase_analysis.py`: 三模型badcase深度分析脚本
  - 分析LLaMA、Embedding、TACL三个模型的失败案例
  - 生成详细的Excel分析报告
  - 包含数据集标签频次统计

#### **model_ensemble/**
- `analyze_model_ensemble.py`: 双模型(LLaMA+Embedding)集成分析
- `analyze_three_model_ensemble.py`: 三模型集成分析
  - 支持任意Top-k结果分析
  - 多种集成策略评估
  - 详细的Excel输出

#### **dataset_analysis/**
- `analyze_dataset_comparison.py`: 数据集对比分析工具

#### **evaluation_metrics/**
- `improved_evaluation_metrics.py`: 改进的评估指标计算
- `generate_knowledge_point_metrics.py`: 知识点级别指标生成
- `generate_embedding_metrics.py`: 嵌入模型指标生成

#### **unified_predictions/**
- `unify_model_predictions.py`: 统一预测结果格式
  - 支持任意Top-k结果
  - 生成简洁的JSON格式

### 2. **results/** - 分析结果
所有分析工具生成的结果文件，按功能分类存储：

#### **badcase_analysis/**
- 包含详细的badcase分析Excel文件

#### **model_ensemble/**
- 包含各种模型集成分析的Excel结果

#### **unified_predictions/**
- 包含不同配置的统一预测JSON文件

### 3. **scripts/** - 辅助脚本
按用途分类的辅助脚本：

#### **evaluation/**
- 各种评估和检查脚本

#### **data_processing/**
- 数据处理和转换脚本

### 4. **docs/** - 文档
项目文档，分为报告和指南：

#### **reports/**
- 所有分析报告的Markdown文件

#### **guides/**
- 使用指南和说明文档

## 🚀 使用方法

### 方法一：使用便捷运行脚本（推荐）
```bash
# 查看所有可用工具
python run_analysis.py --list

# 运行指定工具
python run_analysis.py badcase         # Badcase分析
python run_analysis.py ensemble        # 双模型集成分析
python run_analysis.py three_ensemble  # 三模型集成分析
python run_analysis.py unified         # 统一预测格式
python run_analysis.py all             # 运行所有工具
```

### 方法二：直接运行脚本
```bash
# 进入对应目录运行（推荐，路径已优化）
cd analysis_tools/badcase_analysis && python badcase_analysis.py
cd analysis_tools/model_ensemble && python analyze_model_ensemble.py
cd analysis_tools/unified_predictions && python unify_model_predictions.py

# 或从项目根目录运行（需要手动处理路径）
python analysis_tools/badcase_analysis/badcase_analysis.py
```

### 查看结果
- Excel结果文件在 `results/` 对应子目录中
- 分析报告在 `docs/reports/` 中
- 使用指南在 `docs/guides/` 中

## 📊 文件配套关系

### Badcase分析套件
- **脚本**: `analysis_tools/badcase_analysis/badcase_analysis.py`
- **结果**: `results/badcase_analysis/badcase_analysis_detailed.xlsx`
- **报告**: `docs/reports/badcase_analysis_report.md`

### 模型集成分析套件
- **脚本**: `analysis_tools/model_ensemble/analyze_*_ensemble.py`
- **结果**: `results/model_ensemble/*.xlsx`
- **报告**: `docs/reports/*ensemble*report.md`

### 统一预测套件
- **脚本**: `analysis_tools/unified_predictions/unify_model_predictions.py`
- **结果**: `results/unified_predictions/unified_model_predictions*.json`
- **报告**: `docs/reports/unified_predictions*report.md`

## ✅ 整理优势

1. **功能清晰**: 按功能模块组织，易于查找
2. **配套完整**: 脚本、结果、报告三位一体
3. **结构规范**: 统一的目录命名和组织方式
4. **易于维护**: 相关文件集中管理
5. **扩展友好**: 新功能可以按模块添加
6. **路径优化**: 所有脚本自动输出到正确的results文件夹
7. **便捷运行**: 提供统一的运行脚本，支持从项目根目录运行

## 🔧 路径配置说明

### 自动路径处理
所有分析脚本已经过路径优化，确保：
- ✅ **自动创建目录**: 脚本运行时自动创建对应的results子目录
- ✅ **相对路径计算**: 使用相对路径`../../results/模块名`确保输出正确
- ✅ **跨平台兼容**: 使用`os.path.join()`确保路径在不同系统上正确

### 验证工具
```bash
# 验证项目结构和路径配置
python verify_paths.py
```

**项目现在结构清晰，路径配置完善，便于管理和使用！** 🎉
