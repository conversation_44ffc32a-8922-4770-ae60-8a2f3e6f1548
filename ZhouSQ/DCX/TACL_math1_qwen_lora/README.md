# 数学知识点分类项目

## 🎯 项目概述

本项目专注于数学知识点的自动分类，使用多个模型（LLaMA、Embedding、TACL）进行预测和集成分析。

## 📁 快速导航

### 🔧 分析工具 (`analysis_tools/`)
| 工具 | 功能 | 位置 |
|------|------|------|
| **Badcase分析** | 三模型失败案例深度分析 | `badcase_analysis/badcase_analysis.py` |
| **模型集成分析** | 双模型/三模型集成效果评估 | `model_ensemble/analyze_*_ensemble.py` |
| **数据集分析** | 数据集对比和统计分析 | `dataset_analysis/analyze_dataset_comparison.py` |
| **评估指标** | 各种评估指标计算 | `evaluation_metrics/` |
| **统一预测** | 预测结果格式统一 | `unified_predictions/unify_model_predictions.py` |

### 📊 分析结果 (`results/`)
| 类型 | 内容 | 位置 |
|------|------|------|
| **Badcase分析** | 详细失败案例Excel | `badcase_analysis/badcase_analysis_detailed.xlsx` |
| **集成分析** | 模型集成效果Excel | `model_ensemble/*.xlsx` |
| **统一预测** | JSON格式预测结果 | `unified_predictions/*.json` |
| **评估指标** | 各种指标Excel | `evaluation_metrics/*.xlsx` |

### 📖 文档报告 (`docs/`)
| 类型 | 内容 | 位置 |
|------|------|------|
| **分析报告** | 详细分析结果报告 | `reports/*.md` |
| **使用指南** | 操作说明和指南 | `guides/*.md` |

## 🚀 快速开始

### 1. Badcase分析
```bash
# 运行三模型badcase分析
python analysis_tools/badcase_analysis/badcase_analysis.py

# 查看结果
# Excel: results/badcase_analysis/badcase_analysis_detailed.xlsx
# 报告: docs/reports/badcase_analysis_report.md
```

### 2. 模型集成分析
```bash
# 双模型集成分析
python analysis_tools/model_ensemble/analyze_model_ensemble.py

# 三模型集成分析
python analysis_tools/model_ensemble/analyze_three_model_ensemble.py

# 查看结果
# Excel: results/model_ensemble/*.xlsx
# 报告: docs/reports/*ensemble*report.md
```

### 3. 统一预测格式
```bash
# 生成统一预测JSON
python analysis_tools/unified_predictions/unify_model_predictions.py

# 查看结果
# JSON: results/unified_predictions/unified_model_predictions*.json
# 报告: docs/reports/unified_predictions*report.md
```

## 📈 核心功能

### 🔍 Badcase分析
- **三模型对比**: LLaMA vs Embedding vs TACL
- **失败案例识别**: 找出所有模型都失败的困难样本
- **标签频次分析**: 数据集中各标签的分布统计
- **模型特异性**: 识别每个模型的独有优势

### 🤝 模型集成
- **多种策略**: 多数投票、优先级、置信度加权
- **Top-k支持**: 自动处理任意Top-k预测结果
- **性能提升**: 量化集成带来的准确率提升
- **详细统计**: 包含题目数量的完整统计

### 📊 评估指标
- **准确率计算**: 各种粒度的准确率统计
- **知识点分析**: 按知识点类别的性能分析
- **Top-k分析**: 累积准确率和单独准确率

## 🎯 主要发现

### Badcase分析结果
- **37.45%的样本三个模型都无法正确预测** (91/243)
- **TACL模型表现最好**: 独有正确预测32个样本
- **几何空间类题目**: 所有模型的共同弱点
- **低频标签**: 预测准确率普遍较低

### 模型集成效果
- **LLaMA + Embedding Top-10**: 87.18%准确率
- **三模型集成最佳策略**: 51.44%准确率
- **显著提升**: 相比单模型提升12-18个百分点

## 📁 数据文件

### 输入数据
- `math_data/`: 数学题目数据集
- `LLaMA-Factory/saves/`: LLaMA模型预测结果
- `math_similarity_system/results/`: Embedding模型结果
- `TACL-result/`: TACL项目预测结果

### 输出结果
- `results/`: 所有分析结果Excel和JSON文件
- `docs/reports/`: 详细的分析报告

## 🛠️ 技术特点

- **自动Top-k检测**: 支持任意Top-k值，无需手动配置
- **多格式兼容**: 处理不同模型的输出格式
- **完整匹配策略**: 位置索引→精确匹配→模糊匹配→强制匹配
- **详细统计**: 所有百分比都包含具体题目数量
- **可视化友好**: Excel格式便于进一步分析

## 📞 使用说明

1. **查看项目结构**: 阅读 `PROJECT_STRUCTURE.md`
2. **运行指令**: 参考 `docs/guides/运行指令.md`
3. **匹配策略**: 了解 `docs/guides/matching_strategy_analysis.md`
4. **分析报告**: 查看 `docs/reports/` 中的详细报告

## ✅ 项目状态

- ✅ **Badcase分析系统**: 完全就绪
- ✅ **模型集成分析**: 支持任意Top-k
- ✅ **统一预测格式**: 简洁高效
- ✅ **评估指标体系**: 全面完整
- ✅ **文档体系**: 详细规范

**项目已完全整理，结构清晰，功能完备！** 🎉
