{"overall_metrics": {"micro_precision": 0.3970276008492569, "micro_recall": 0.3970276008492569, "micro_f1": 0.3970276008492569, "macro_precision": 0.2569064505234718, "macro_recall": 0.20760876239599643, "macro_f1": 0.21037604719925013, "weighted_precision": 0.3970276008492569, "weighted_recall": 0.3970276008492569, "weighted_f1": 0.3970276008492569, "exact_match_accuracy": 0.39490445859872614, "hamming_loss": 0.6029723991507431, "num_samples": 471, "num_classes": 282}, "prediction_source_metrics": {"fine_tuned_model": {"count": 471, "percentage": 100.0, "accuracy": 0.39490445859872614}}, "similarity_threshold_analysis": {"above_threshold_count": 0, "below_threshold_count": 471, "threshold_value": 1e-05, "below_threshold_avg_similarity": 1.0, "below_threshold_min_similarity": 1.0, "below_threshold_max_similarity": 1.0}, "prediction_accuracy": {"overall_accuracy_topk": 0.39490445859872614, "overall_accuracy_top1": 0.39490445859872614, "total_correct_topk": 186, "total_correct_top1": 186, "total_samples": 471, "vector_db_accuracy_topk": 0.0, "vector_db_correct": 0, "vector_db_total": 0, "model_accuracy": 0.39490445859872614, "model_correct": 186, "model_total": 471, "improvement_topk_vs_top1": 0.0}, "leaf_node_metrics": {"leaf_node_accuracy": 0.3970276008492569, "leaf_node_precision": 0.3970276008492569, "leaf_node_recall": 0.3970276008492569, "leaf_node_f1": 0.3970276008492569, "leaf_node_macro_precision": 0.2569064505234718, "leaf_node_macro_recall": 0.20760876239599643, "leaf_node_macro_f1": 0.21037604719925013, "leaf_node_micro_precision": 0.3970276008492569, "leaf_node_micro_recall": 0.3970276008492569, "leaf_node_micro_f1": 0.3970276008492569, "num_leaf_classes": 282, "leaf_class_distribution": {"100以内数的组成": {"true_count": 0, "pred_count": 1, "support": 0}, "10的分与合": {"true_count": 1, "pred_count": 3, "support": 1}, "10的加减法的应用": {"true_count": 3, "pred_count": 2, "support": 3}, "10的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "11-20各数的组成": {"true_count": 0, "pred_count": 1, "support": 0}, "11-20各数的读写": {"true_count": 1, "pred_count": 1, "support": 1}, "11-20各数的顺序": {"true_count": 4, "pred_count": 3, "support": 4}, "2-4乘法口诀的应用题-": {"true_count": 1, "pred_count": 0, "support": 1}, "2-5的乘加乘减应用题": {"true_count": 3, "pred_count": 2, "support": 3}, "2-5的乘法应用题": {"true_count": 0, "pred_count": 1, "support": 0}, "24点游戏": {"true_count": 0, "pred_count": 1, "support": 0}, "5、4、3、2加几": {"true_count": 1, "pred_count": 0, "support": 1}, "5的乘法口诀应用题": {"true_count": 5, "pred_count": 2, "support": 5}, "5的乘法口诀理解": {"true_count": 0, "pred_count": 1, "support": 0}, "6 、7的加减法的应用": {"true_count": 3, "pred_count": 3, "support": 3}, "6-9的认识": {"true_count": 1, "pred_count": 0, "support": 1}, "6、7的减法的实际应用": {"true_count": 4, "pred_count": 0, "support": 4}, "6的乘法口诀应用题": {"true_count": 3, "pred_count": 0, "support": 3}, "7-9的认识与读写": {"true_count": 0, "pred_count": 1, "support": 0}, "7-9连减与除法": {"true_count": 1, "pred_count": 0, "support": 1}, "8、7、6加几": {"true_count": 1, "pred_count": 1, "support": 1}, "8、9的分合的应用": {"true_count": 2, "pred_count": 4, "support": 2}, "8、9的加减法的应用": {"true_count": 4, "pred_count": 4, "support": 4}, "8、9的加减法的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "8的乘法应用题（旧版）": {"true_count": 0, "pred_count": 2, "support": 0}, "9的乘法口诀（旧版）": {"true_count": 0, "pred_count": 1, "support": 0}, "一个因数变化的规律": {"true_count": 3, "pred_count": 0, "support": 3}, "三位数乘一位数(乘数中间有0)的笔算": {"true_count": 8, "pred_count": 6, "support": 8}, "三位数乘两位数的估算": {"true_count": 3, "pred_count": 0, "support": 3}, "三位数乘两位数笔算": {"true_count": 5, "pred_count": 4, "support": 5}, "三位数加两、三位数(连续进位)的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "三位数除以两位数(商是两位数)的笔算除法": {"true_count": 3, "pred_count": 5, "support": 3}, "三位数除以两位数的笔算(有余数)": {"true_count": 0, "pred_count": 1, "support": 0}, "三位数除以整十数商是一位数的笔算除法": {"true_count": 0, "pred_count": 3, "support": 0}, "三位数除以整十数的笔算除法": {"true_count": 0, "pred_count": 1, "support": 0}, "三角尺拼角": {"true_count": 3, "pred_count": 2, "support": 3}, "不含括号的四则混合运算": {"true_count": 1, "pred_count": 0, "support": 1}, "不含括号的四则混合运算的运算顺序": {"true_count": 0, "pred_count": 1, "support": 0}, "不含括号的表内混合运算": {"true_count": 3, "pred_count": 2, "support": 3}, "与公顷、平方千米有关的实际问题": {"true_count": 1, "pred_count": 0, "support": 1}, "两、三位数乘一位数(不连续进位)的笔算": {"true_count": 1, "pred_count": 0, "support": 1}, "两、三位数乘一位数(连续进位)的笔算": {"true_count": 7, "pred_count": 10, "support": 7}, "两、三位数乘一位数的估算": {"true_count": 4, "pred_count": 2, "support": 4}, "两、三位数乘一位数的估算应用": {"true_count": 4, "pred_count": 1, "support": 4}, "两位数乘一位数的口算(不进位)": {"true_count": 3, "pred_count": 0, "support": 3}, "两位数乘一位数的口算(有进位)": {"true_count": 1, "pred_count": 0, "support": 1}, "两位数乘一位数的口算(有进位)的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数乘一位数的口算的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数乘两位数的估算": {"true_count": 0, "pred_count": 3, "support": 0}, "两位数乘两位数的笔算(不进位)的实际问题": {"true_count": 0, "pred_count": 2, "support": 0}, "两位数减一位数（退位）解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数加两位数的口算的应用题": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数除三位数的估算方法": {"true_count": 2, "pred_count": 4, "support": 2}, "两位数除以一位数的口算除法的实际问题": {"true_count": 0, "pred_count": 2, "support": 0}, "两点间的距离(三角形)": {"true_count": 0, "pred_count": 1, "support": 0}, "乘、除法各部分间的关系的计算与应用": {"true_count": 0, "pred_count": 1, "support": 0}, "乘加乘减应用题(表内混合运算)": {"true_count": 3, "pred_count": 6, "support": 3}, "乘法交换律和结合律的综合运用": {"true_count": 0, "pred_count": 1, "support": 0}, "乘法的意义": {"true_count": 0, "pred_count": 1, "support": 0}, "乘除法的应用题(表内)": {"true_count": 1, "pred_count": 1, "support": 1}, "乘除法的计算与应用(2-6)": {"true_count": 0, "pred_count": 1, "support": 0}, "亿以内数的读法": {"true_count": 1, "pred_count": 1, "support": 1}, "从不同位置观察同一几何组合体": {"true_count": 0, "pred_count": 1, "support": 0}, "以一当一的条形统计图": {"true_count": 3, "pred_count": 1, "support": 3}, "以一当二的条形统计图": {"true_count": 2, "pred_count": 2, "support": 2}, "以一当多的条形统计图": {"true_count": 5, "pred_count": 2, "support": 5}, "优惠方案问题(表内除法)": {"true_count": 0, "pred_count": 1, "support": 0}, "优惠策略": {"true_count": 7, "pred_count": 4, "support": 7}, "体积单位的认识": {"true_count": 0, "pred_count": 1, "support": 0}, "关于平行的相关判断": {"true_count": 2, "pred_count": 1, "support": 2}, "减号后添/去括号": {"true_count": 2, "pred_count": 0, "support": 2}, "减法的运算性质": {"true_count": 0, "pred_count": 2, "support": 0}, "几分之一的大小比较": {"true_count": 0, "pred_count": 1, "support": 0}, "几百几十数乘一位数的口算(有进位)": {"true_count": 0, "pred_count": 7, "support": 0}, "分数与除法关系的应用-": {"true_count": 0, "pred_count": 4, "support": 0}, "分数乘分数的实际应用": {"true_count": 3, "pred_count": 2, "support": 3}, "分数乘小数的计算": {"true_count": 1, "pred_count": 0, "support": 1}, "分数乘整数": {"true_count": 14, "pred_count": 7, "support": 14}, "分数乘整数的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "分数乘法中因数与积的大小关系": {"true_count": 0, "pred_count": 1, "support": 0}, "分数加减乘混合运算的应用题": {"true_count": 0, "pred_count": 2, "support": 0}, "分数方程解决实际应用": {"true_count": 1, "pred_count": 0, "support": 1}, "分数比大小综合(同分母/同分子)": {"true_count": 7, "pred_count": 1, "support": 7}, "分数的大小比较": {"true_count": 0, "pred_count": 2, "support": 0}, "分数的意义": {"true_count": 0, "pred_count": 4, "support": 0}, "分数连乘的实际应用-": {"true_count": 0, "pred_count": 1, "support": 0}, "分数量的理解（如3/4千克）-": {"true_count": 0, "pred_count": 1, "support": 0}, "分数除法之和倍、差倍问题": {"true_count": 1, "pred_count": 0, "support": 1}, "分步解决问题的策略": {"true_count": 0, "pred_count": 2, "support": 0}, "分米、厘米和毫米之间的单位换算": {"true_count": 2, "pred_count": 6, "support": 2}, "分米、厘米和毫米之间的单位计算": {"true_count": 2, "pred_count": 3, "support": 2}, "列多个算式的应用题（2-6含除法）-": {"true_count": 3, "pred_count": 1, "support": 3}, "列方程解决实际问题ax=b(a≠0)": {"true_count": 0, "pred_count": 2, "support": 0}, "初步认识立体图形": {"true_count": 1, "pred_count": 2, "support": 1}, "初步认识除法": {"true_count": 2, "pred_count": 0, "support": 2}, "判断商是几位数(除数是两位数)": {"true_count": 15, "pred_count": 8, "support": 15}, "利用小数点移动引起小数大小的变化解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "利用整体的几分之几解决问题": {"true_count": 11, "pred_count": 2, "support": 11}, "加、减、乘、除各部分间的关系的计算与应用": {"true_count": 0, "pred_count": 1, "support": 0}, "加减混合应用题": {"true_count": 0, "pred_count": 1, "support": 0}, "加减混合运用(10以内加减混合)": {"true_count": 6, "pred_count": 1, "support": 6}, "加减混合运算(10以内)": {"true_count": 0, "pred_count": 1, "support": 0}, "加小括号或省略括号问题": {"true_count": 0, "pred_count": 1, "support": 0}, "加小括号的综合问题(表内混合运算)": {"true_count": 1, "pred_count": 2, "support": 1}, "加法应用题(100以内)": {"true_count": 0, "pred_count": 1, "support": 0}, "包含分求份数（列除法算式）": {"true_count": 1, "pred_count": 0, "support": 1}, "十几减5、4、3、2的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "十几减5、4、3、2的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "十几减7、6的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "十几减几的不退位减法": {"true_count": 1, "pred_count": 1, "support": 1}, "千克与克的相关应用题": {"true_count": 4, "pred_count": 1, "support": 4}, "千克和克之间的进率及换算": {"true_count": 2, "pred_count": 1, "support": 2}, "千米与米的换算": {"true_count": 3, "pred_count": 2, "support": 3}, "千米的认识": {"true_count": 0, "pred_count": 2, "support": 0}, "反比例的应用（补全题、选算式）": {"true_count": 0, "pred_count": 1, "support": 0}, "口算两位数加一位数(进位)": {"true_count": 0, "pred_count": 1, "support": 0}, "合理安排时间": {"true_count": 0, "pred_count": 1, "support": 0}, "同分母分数减法在实际生活的应用-": {"true_count": 0, "pred_count": 1, "support": 0}, "同分母分数减法的计算、运用-": {"true_count": 0, "pred_count": 2, "support": 0}, "同分母分数加、减法": {"true_count": 2, "pred_count": 0, "support": 2}, "同分母分数加、减法的应用": {"true_count": 2, "pred_count": 0, "support": 2}, "同分母分数加法的含义及计算方法": {"true_count": 0, "pred_count": 1, "support": 0}, "同分母分数加法的计算、运用-": {"true_count": 0, "pred_count": 1, "support": 0}, "同级运算的运算顺序": {"true_count": 2, "pred_count": 2, "support": 2}, "吨、千克和克之间的进率及换算": {"true_count": 3, "pred_count": 2, "support": 3}, "含有两级运算的运算顺序": {"true_count": 2, "pred_count": 1, "support": 2}, "含有小括号的表内混合运算": {"true_count": 6, "pred_count": 5, "support": 6}, "商不变的性质（除数是一位数）": {"true_count": 0, "pred_count": 1, "support": 0}, "商不变的规律": {"true_count": 14, "pred_count": 14, "support": 14}, "商不变规律中余数的变化": {"true_count": 5, "pred_count": 0, "support": 5}, "商的变化规律": {"true_count": 4, "pred_count": 5, "support": 4}, "四个方向描述简单的行走路线": {"true_count": 1, "pred_count": 2, "support": 1}, "图形的变化规律": {"true_count": 0, "pred_count": 1, "support": 0}, "在平面图上辨认东、南、西、北": {"true_count": 2, "pred_count": 2, "support": 2}, "在统计表中计算平均数": {"true_count": 0, "pred_count": 1, "support": 0}, "填质量单位(克和千克)": {"true_count": 1, "pred_count": 2, "support": 1}, "多个小数的大小比较": {"true_count": 0, "pred_count": 1, "support": 0}, "多种立体图形的拼搭": {"true_count": 2, "pred_count": 0, "support": 2}, "够不够问题": {"true_count": 2, "pred_count": 0, "support": 2}, "封闭路线植树问题": {"true_count": 0, "pred_count": 1, "support": 0}, "将分步算式改写成带小括号或中括号的综合算式": {"true_count": 0, "pred_count": 1, "support": 0}, "小数乘分数的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "已知一个数的几分之几是多少，求这个数（考法需整合）": {"true_count": 0, "pred_count": 1, "support": 0}, "已知总量和分量比，求分量": {"true_count": 1, "pred_count": 0, "support": 1}, "已知经过时间，求开始（结束）时刻": {"true_count": 0, "pred_count": 1, "support": 0}, "平均分与分数-": {"true_count": 0, "pred_count": 2, "support": 0}, "平均分求每份数（列除法算式）": {"true_count": 0, "pred_count": 1, "support": 0}, "平均分的综合理解与应用": {"true_count": 1, "pred_count": 0, "support": 1}, "应用几、第几及左右/前后关系解决问题": {"true_count": 4, "pred_count": 3, "support": 4}, "异分母分数加法在实际生活的应用-": {"true_count": 0, "pred_count": 1, "support": 0}, "按角分类的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "数的组成（20以内）": {"true_count": 2, "pred_count": 0, "support": 2}, "数线段、直线和射线": {"true_count": 3, "pred_count": 2, "support": 3}, "数角(角的初步认识)": {"true_count": 6, "pred_count": 3, "support": 6}, "整十、整百、整千数乘一位数的口算": {"true_count": 10, "pred_count": 4, "support": 10}, "整十、整百、整千数除以一位数的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "整数乘分数": {"true_count": 2, "pred_count": 2, "support": 2}, "整百、整千数、几百几十数的加减法": {"true_count": 1, "pred_count": 0, "support": 1}, "方向变化推理题（四个方向）": {"true_count": 2, "pred_count": 3, "support": 2}, "有隐藏条件的除法应用题(1-9)": {"true_count": 0, "pred_count": 1, "support": 0}, "有隐藏条件的除法应用题(2-6)": {"true_count": 0, "pred_count": 3, "support": 0}, "根据公式求时间": {"true_count": 1, "pred_count": 1, "support": 1}, "根据公式求路程": {"true_count": 1, "pred_count": 0, "support": 1}, "根据分步运算列综合算式(含括号)(表内混合运算)": {"true_count": 5, "pred_count": 2, "support": 5}, "根据单式折线统计图分析、预测": {"true_count": 0, "pred_count": 1, "support": 0}, "根据描述确定位置（八个方向）": {"true_count": 1, "pred_count": 1, "support": 1}, "根据描述确定位置（四个方向）": {"true_count": 1, "pred_count": 0, "support": 1}, "梯形周长的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "梯形的概念": {"true_count": 2, "pred_count": 1, "support": 2}, "求一个数是另一个数的几分之几": {"true_count": 0, "pred_count": 2, "support": 0}, "求一个数的几倍是多少": {"true_count": 0, "pred_count": 2, "support": 0}, "求两数公因数、最大公因数的特殊情况": {"true_count": 0, "pred_count": 1, "support": 0}, "沏茶问题": {"true_count": 4, "pred_count": 3, "support": 4}, "洒水车、收割机问题": {"true_count": 0, "pred_count": 1, "support": 0}, "测量方式": {"true_count": 2, "pred_count": 0, "support": 2}, "添加分数单位后，变成某数": {"true_count": 0, "pred_count": 1, "support": 0}, "灵活选择估算策略解决问题": {"true_count": 0, "pred_count": 2, "support": 0}, "点到直线的距离应用": {"true_count": 3, "pred_count": 3, "support": 3}, "烙饼问题": {"true_count": 5, "pred_count": 5, "support": 5}, "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)": {"true_count": 0, "pred_count": 3, "support": 0}, "理解一个数的几分之几是多少(分数乘法)": {"true_count": 0, "pred_count": 5, "support": 0}, "理解加法与减法之间的互逆关系（十几减5、4、3、2）": {"true_count": 0, "pred_count": 1, "support": 0}, "理解平均分": {"true_count": 2, "pred_count": 1, "support": 2}, "用三位数除以两位数(商是两位数)解决简单的实际问题": {"true_count": 8, "pred_count": 1, "support": 8}, "用三位数除以整十数解决简单的实际问题": {"true_count": 9, "pred_count": 3, "support": 9}, "用两步计算解决问题(表内混合运算)(不含括号)": {"true_count": 10, "pred_count": 1, "support": 10}, "用乘法口诀求商(2-6)": {"true_count": 0, "pred_count": 1, "support": 0}, "用乘法和除法两步计算解决问题": {"true_count": 4, "pred_count": 2, "support": 4}, "用乘除混合解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "用公倍数、最小公倍数解决简单的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "用含有括号的四则混合运算解决实际问题": {"true_count": 0, "pred_count": 4, "support": 0}, "用平移解决面积问题": {"true_count": 0, "pred_count": 1, "support": 0}, "用连除解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "田忌赛马": {"true_count": 1, "pred_count": 1, "support": 1}, "画线段、直线和射线": {"true_count": 3, "pred_count": 4, "support": 3}, "画线添角": {"true_count": 1, "pred_count": 1, "support": 1}, "画角": {"true_count": 0, "pred_count": 1, "support": 0}, "直尺测量（厘米）": {"true_count": 3, "pred_count": 1, "support": 3}, "相同正方体拼搭": {"true_count": 1, "pred_count": 0, "support": 1}, "相对方向(四个方向)": {"true_count": 3, "pred_count": 3, "support": 3}, "看图列综合算式并计算": {"true_count": 3, "pred_count": 1, "support": 3}, "租船/车问题": {"true_count": 0, "pred_count": 1, "support": 0}, "积不变的规律的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "积末尾或中间0的个数的问题": {"true_count": 0, "pred_count": 1, "support": 0}, "稍复杂的求一个数的几分之几是多少": {"true_count": 4, "pred_count": 1, "support": 4}, "立体图形中的平面图形": {"true_count": 0, "pred_count": 1, "support": 0}, "笔算两位数加两位数(进位加)解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "等分(按指定的份数平均分)": {"true_count": 0, "pred_count": 1, "support": 0}, "等腰梯形周长相关的计算": {"true_count": 1, "pred_count": 0, "support": 1}, "简单求一个数的几分之几是多少": {"true_count": 7, "pred_count": 4, "support": 7}, "算式中有0的加减法": {"true_count": 1, "pred_count": 1, "support": 1}, "线段的再认识": {"true_count": 0, "pred_count": 1, "support": 0}, "组合图形数小正方体数量的规律": {"true_count": 1, "pred_count": 0, "support": 1}, "组合思想解决问题": {"true_count": 2, "pred_count": 3, "support": 2}, "绳长对折问题": {"true_count": 1, "pred_count": 0, "support": 1}, "角度的简单计算--折叠": {"true_count": 1, "pred_count": 2, "support": 1}, "角度直接运算": {"true_count": 3, "pred_count": 0, "support": 3}, "角的初步认识的应用(直角、锐角、钝角)": {"true_count": 5, "pred_count": 1, "support": 5}, "角的大小比较(初步认识角)": {"true_count": 2, "pred_count": 2, "support": 2}, "解决两、三位数乘一位数(不进位)的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "解决两、三位数乘一位数(连续进位)的实际问题": {"true_count": 1, "pred_count": 0, "support": 1}, "解决千米相关的实际问题": {"true_count": 7, "pred_count": 1, "support": 7}, "解决吨相关的实际问题": {"true_count": 3, "pred_count": 3, "support": 3}, "解决小数与单位换算的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "解决有关分米、厘米、毫米的问题": {"true_count": 5, "pred_count": 3, "support": 5}, "认识三角尺": {"true_count": 0, "pred_count": 1, "support": 0}, "认识东北、东南、西北、西南四个方向": {"true_count": 0, "pred_count": 1, "support": 0}, "认识克": {"true_count": 1, "pred_count": 1, "support": 1}, "认识几分之几": {"true_count": 6, "pred_count": 1, "support": 6}, "认识厘米": {"true_count": 0, "pred_count": 2, "support": 0}, "认识平行": {"true_count": 0, "pred_count": 2, "support": 0}, "认识折线统计图及其特点": {"true_count": 0, "pred_count": 1, "support": 0}, "认识整体的几分之几(整体是多个物体)": {"true_count": 3, "pred_count": 2, "support": 3}, "认识毫米": {"true_count": 2, "pred_count": 2, "support": 2}, "认识直角": {"true_count": 1, "pred_count": 1, "support": 1}, "认识直角、锐角、钝角": {"true_count": 0, "pred_count": 1, "support": 0}, "认识米": {"true_count": 0, "pred_count": 1, "support": 0}, "认识线段": {"true_count": 3, "pred_count": 2, "support": 3}, "认识角": {"true_count": 0, "pred_count": 1, "support": 0}, "认识锐角、钝角": {"true_count": 3, "pred_count": 4, "support": 3}, "读取复式条形统计图并回答问题": {"true_count": 0, "pred_count": 2, "support": 0}, "质量单位比较大小(吨、千克、克)": {"true_count": 1, "pred_count": 2, "support": 1}, "购物问题(2-6)": {"true_count": 0, "pred_count": 1, "support": 0}, "辨析情景中分数大小（单位1不同）-": {"true_count": 0, "pred_count": 1, "support": 0}, "辨认东、西、南、北四个方向": {"true_count": 5, "pred_count": 3, "support": 5}, "运用三位数除以一位数的笔算解决实际问题(商是两位数)": {"true_count": 0, "pred_count": 1, "support": 0}, "运用乘、除法的意义和各部分之间的关系解决实际问题": {"true_count": 0, "pred_count": 3, "support": 0}, "运用乘法交换律和结合律解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "运用乘法分配律解决实际问题": {"true_count": 0, "pred_count": 2, "support": 0}, "运用估算判断积的可能性": {"true_count": 1, "pred_count": 0, "support": 1}, "运用减法的运算性质解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "运用分数通分比较大小来解决问题": {"true_count": 0, "pred_count": 2, "support": 0}, "运用口算乘法解决实际问题": {"true_count": 5, "pred_count": 4, "support": 5}, "运用数与形总结规律": {"true_count": 0, "pred_count": 1, "support": 0}, "运用积的变化规律解决问题": {"true_count": 0, "pred_count": 1, "support": 0}, "运用除数是一位数的除法估算解决问题": {"true_count": 0, "pred_count": 1, "support": 0}, "进一、去尾解决问题": {"true_count": 0, "pred_count": 2, "support": 0}, "连减与除法(2-6)": {"true_count": 2, "pred_count": 0, "support": 2}, "连减的应用（10以内）": {"true_count": 5, "pred_count": 4, "support": 5}, "连减解决实际问题（100以内）": {"true_count": 0, "pred_count": 1, "support": 0}, "连加解决实际问题": {"true_count": 1, "pred_count": 1, "support": 1}, "连加计算(10以内)": {"true_count": 2, "pred_count": 1, "support": 2}, "连续数(100以内)": {"true_count": 0, "pred_count": 1, "support": 0}, "选择合适的长度单位(分米、毫米)": {"true_count": 3, "pred_count": 5, "support": 3}, "选择合适的长度单位(千米、米、厘米、毫米)": {"true_count": 7, "pred_count": 1, "support": 7}, "选择合适的长度单位(米和厘米)": {"true_count": 5, "pred_count": 6, "support": 5}, "选择合适的面积单位(公顷、平方千米)": {"true_count": 1, "pred_count": 1, "support": 1}, "速度、时间、路程之间的关系": {"true_count": 2, "pred_count": 2, "support": 2}, "锐角、直角、钝角、平角和周角之间的大小关系": {"true_count": 1, "pred_count": 3, "support": 1}, "错中求解(100以内)": {"true_count": 0, "pred_count": 1, "support": 0}, "错中求解(表内混合运算)": {"true_count": 6, "pred_count": 3, "support": 6}, "除加除减应用题(表内混合运算)": {"true_count": 4, "pred_count": 3, "support": 4}, "除数不接近整十数的试商": {"true_count": 6, "pred_count": 1, "support": 6}, "除数接近整十数的笔算方法(用四舍\"法试商)\"": {"true_count": 0, "pred_count": 1, "support": 0}, "除数是一位数的填数问题": {"true_count": 0, "pred_count": 3, "support": 0}, "除数是两位数的填数问题": {"true_count": 0, "pred_count": 2, "support": 0}, "除数是两位数的除法数字谜": {"true_count": 0, "pred_count": 1, "support": 0}, "除法应用题(2-6)（旧版）": {"true_count": 0, "pred_count": 2, "support": 0}, "除法意义的理解与运用（移后删）": {"true_count": 1, "pred_count": 1, "support": 1}, "除法的意义": {"true_count": 0, "pred_count": 3, "support": 0}, "除法计算与运用(2-6)": {"true_count": 0, "pred_count": 1, "support": 0}, "露出部分推整体": {"true_count": 0, "pred_count": 1, "support": 0}}, "leaf_class_report": {"100以内数的组成": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "10的分与合": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "10的加减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "10的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "11-20各数的组成": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "11-20各数的读写": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "11-20各数的顺序": {"precision": 1.0, "recall": 0.75, "f1-score": 0.8571428571428571, "support": 4}, "2-4乘法口诀的应用题-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "2-5的乘加乘减应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "2-5的乘法应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "24点游戏": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "5、4、3、2加几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "5的乘法口诀应用题": {"precision": 1.0, "recall": 0.4, "f1-score": 0.5714285714285715, "support": 5}, "5的乘法口诀理解": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "6 、7的加减法的应用": {"precision": 0.3333333333333333, "recall": 0.3333333333333333, "f1-score": 0.3333333333333333, "support": 3}, "6-9的认识": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "6、7的减法的实际应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "6的乘法口诀应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "7-9的认识与读写": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "7-9连减与除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "8、7、6加几": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "8、9的分合的应用": {"precision": 0.25, "recall": 0.5, "f1-score": 0.3333333333333333, "support": 2}, "8、9的加减法的应用": {"precision": 0.75, "recall": 0.75, "f1-score": 0.75, "support": 4}, "8、9的加减法的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "8的乘法应用题（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "9的乘法口诀（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "一个因数变化的规律": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "三位数乘一位数(乘数中间有0)的笔算": {"precision": 0.6666666666666666, "recall": 0.5, "f1-score": 0.5714285714285715, "support": 8}, "三位数乘两位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "三位数乘两位数笔算": {"precision": 1.0, "recall": 0.8, "f1-score": 0.888888888888889, "support": 5}, "三位数加两、三位数(连续进位)的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以两位数(商是两位数)的笔算除法": {"precision": 0.6, "recall": 1.0, "f1-score": 0.7499999999999999, "support": 3}, "三位数除以两位数的笔算(有余数)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以整十数商是一位数的笔算除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以整十数的笔算除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三角尺拼角": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "不含括号的四则混合运算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "不含括号的四则混合运算的运算顺序": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "不含括号的表内混合运算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "与公顷、平方千米有关的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两、三位数乘一位数(不连续进位)的笔算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两、三位数乘一位数(连续进位)的笔算": {"precision": 0.5, "recall": 0.7142857142857143, "f1-score": 0.588235294117647, "support": 7}, "两、三位数乘一位数的估算": {"precision": 0.5, "recall": 0.25, "f1-score": 0.3333333333333333, "support": 4}, "两、三位数乘一位数的估算应用": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "两位数乘一位数的口算(不进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "两位数乘一位数的口算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两位数乘一位数的口算(有进位)的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘一位数的口算的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的笔算(不进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数减一位数（退位）解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数加两位数的口算的应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数除三位数的估算方法": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 2}, "两位数除以一位数的口算除法的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两点间的距离(三角形)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘、除法各部分间的关系的计算与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘加乘减应用题(表内混合运算)": {"precision": 0.3333333333333333, "recall": 0.6666666666666666, "f1-score": 0.4444444444444444, "support": 3}, "乘法交换律和结合律的综合运用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘法的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘除法的应用题(表内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "乘除法的计算与应用(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "亿以内数的读法": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "从不同位置观察同一几何组合体": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "以一当一的条形统计图": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "以一当二的条形统计图": {"precision": 0.5, "recall": 0.5, "f1-score": 0.5, "support": 2}, "以一当多的条形统计图": {"precision": 0.5, "recall": 0.2, "f1-score": 0.28571428571428575, "support": 5}, "优惠方案问题(表内除法)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "优惠策略": {"precision": 1.0, "recall": 0.5714285714285714, "f1-score": 0.7272727272727273, "support": 7}, "体积单位的认识": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "关于平行的相关判断": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "减号后添/去括号": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "减法的运算性质": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "几分之一的大小比较": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "几百几十数乘一位数的口算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数与除法关系的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘分数的实际应用": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "分数乘小数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "分数乘整数": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 14}, "分数乘整数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘法中因数与积的大小关系": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数加减乘混合运算的应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数方程解决实际应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "分数比大小综合(同分母/同分子)": {"precision": 1.0, "recall": 0.14285714285714285, "f1-score": 0.25, "support": 7}, "分数的大小比较": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数连乘的实际应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数量的理解（如3/4千克）-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数除法之和倍、差倍问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "分步解决问题的策略": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分米、厘米和毫米之间的单位换算": {"precision": 0.3333333333333333, "recall": 1.0, "f1-score": 0.5, "support": 2}, "分米、厘米和毫米之间的单位计算": {"precision": 0.3333333333333333, "recall": 0.5, "f1-score": 0.4, "support": 2}, "列多个算式的应用题（2-6含除法）-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "列方程解决实际问题ax=b(a≠0)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "初步认识立体图形": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "初步认识除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "判断商是几位数(除数是两位数)": {"precision": 1.0, "recall": 0.5333333333333333, "f1-score": 0.6956521739130436, "support": 15}, "利用小数点移动引起小数大小的变化解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "利用整体的几分之几解决问题": {"precision": 0.5, "recall": 0.09090909090909091, "f1-score": 0.15384615384615385, "support": 11}, "加、减、乘、除各部分间的关系的计算与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加减混合应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加减混合运用(10以内加减混合)": {"precision": 1.0, "recall": 0.16666666666666666, "f1-score": 0.2857142857142857, "support": 6}, "加减混合运算(10以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加小括号或省略括号问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加小括号的综合问题(表内混合运算)": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "加法应用题(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "包含分求份数（列除法算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "十几减5、4、3、2的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "十几减5、4、3、2的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "十几减7、6的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "十几减几的不退位减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "千克与克的相关应用题": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "千克和克之间的进率及换算": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "千米与米的换算": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "千米的认识": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "反比例的应用（补全题、选算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "口算两位数加一位数(进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "合理安排时间": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数减法在实际生活的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数减法的计算、运用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数加、减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "同分母分数加、减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "同分母分数加法的含义及计算方法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数加法的计算、运用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同级运算的运算顺序": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 2}, "吨、千克和克之间的进率及换算": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "含有两级运算的运算顺序": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "含有小括号的表内混合运算": {"precision": 0.8, "recall": 0.6666666666666666, "f1-score": 0.7272727272727272, "support": 6}, "商不变的性质（除数是一位数）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "商不变的规律": {"precision": 0.7142857142857143, "recall": 0.7142857142857143, "f1-score": 0.7142857142857143, "support": 14}, "商不变规律中余数的变化": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 5}, "商的变化规律": {"precision": 0.8, "recall": 1.0, "f1-score": 0.888888888888889, "support": 4}, "四个方向描述简单的行走路线": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "图形的变化规律": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "在平面图上辨认东、南、西、北": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 2}, "在统计表中计算平均数": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "填质量单位(克和千克)": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "多个小数的大小比较": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "多种立体图形的拼搭": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "够不够问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "封闭路线植树问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "将分步算式改写成带小括号或中括号的综合算式": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数乘分数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "已知一个数的几分之几是多少，求这个数（考法需整合）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "已知总量和分量比，求分量": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "已知经过时间，求开始（结束）时刻": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分与分数-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分求每份数（列除法算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分的综合理解与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "应用几、第几及左右/前后关系解决问题": {"precision": 0.6666666666666666, "recall": 0.5, "f1-score": 0.5714285714285715, "support": 4}, "异分母分数加法在实际生活的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "按角分类的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "数的组成（20以内）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "数线段、直线和射线": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "数角(角的初步认识)": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 6}, "整十、整百、整千数乘一位数的口算": {"precision": 1.0, "recall": 0.4, "f1-score": 0.5714285714285715, "support": 10}, "整十、整百、整千数除以一位数的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整数乘分数": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "整百、整千数、几百几十数的加减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "方向变化推理题（四个方向）": {"precision": 0.3333333333333333, "recall": 0.5, "f1-score": 0.4, "support": 2}, "有隐藏条件的除法应用题(1-9)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "有隐藏条件的除法应用题(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "根据公式求时间": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "根据公式求路程": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "根据分步运算列综合算式(含括号)(表内混合运算)": {"precision": 1.0, "recall": 0.4, "f1-score": 0.5714285714285715, "support": 5}, "根据单式折线统计图分析、预测": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "根据描述确定位置（八个方向）": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "根据描述确定位置（四个方向）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "梯形周长的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "梯形的概念": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "求一个数是另一个数的几分之几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "求一个数的几倍是多少": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "求两数公因数、最大公因数的特殊情况": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "沏茶问题": {"precision": 1.0, "recall": 0.75, "f1-score": 0.8571428571428571, "support": 4}, "洒水车、收割机问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "测量方式": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "添加分数单位后，变成某数": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "灵活选择估算策略解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "点到直线的距离应用": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3}, "烙饼问题": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 5}, "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "理解一个数的几分之几是多少(分数乘法)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "理解加法与减法之间的互逆关系（十几减5、4、3、2）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "理解平均分": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "用三位数除以两位数(商是两位数)解决简单的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 8}, "用三位数除以整十数解决简单的实际问题": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 9}, "用两步计算解决问题(表内混合运算)(不含括号)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 10}, "用乘法口诀求商(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用乘法和除法两步计算解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "用乘除混合解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用公倍数、最小公倍数解决简单的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用含有括号的四则混合运算解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用平移解决面积问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用连除解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "田忌赛马": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "画线段、直线和射线": {"precision": 0.75, "recall": 1.0, "f1-score": 0.8571428571428571, "support": 3}, "画线添角": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "画角": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "直尺测量（厘米）": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "相同正方体拼搭": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "相对方向(四个方向)": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1-score": 0.6666666666666666, "support": 3}, "看图列综合算式并计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "租船/车问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "积不变的规律的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "积末尾或中间0的个数的问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "稍复杂的求一个数的几分之几是多少": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "立体图形中的平面图形": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "笔算两位数加两位数(进位加)解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "等分(按指定的份数平均分)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "等腰梯形周长相关的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "简单求一个数的几分之几是多少": {"precision": 0.25, "recall": 0.14285714285714285, "f1-score": 0.18181818181818182, "support": 7}, "算式中有0的加减法": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "线段的再认识": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "组合图形数小正方体数量的规律": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "组合思想解决问题": {"precision": 0.6666666666666666, "recall": 1.0, "f1-score": 0.8, "support": 2}, "绳长对折问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "角度的简单计算--折叠": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "角度直接运算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "角的初步认识的应用(直角、锐角、钝角)": {"precision": 1.0, "recall": 0.2, "f1-score": 0.33333333333333337, "support": 5}, "角的大小比较(初步认识角)": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 2}, "解决两、三位数乘一位数(不进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "解决两、三位数乘一位数(连续进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "解决千米相关的实际问题": {"precision": 1.0, "recall": 0.14285714285714285, "f1-score": 0.25, "support": 7}, "解决吨相关的实际问题": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1-score": 0.6666666666666666, "support": 3}, "解决小数与单位换算的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "解决有关分米、厘米、毫米的问题": {"precision": 1.0, "recall": 0.6, "f1-score": 0.7499999999999999, "support": 5}, "认识三角尺": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识东北、东南、西北、西南四个方向": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识克": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "认识几分之几": {"precision": 1.0, "recall": 0.16666666666666666, "f1-score": 0.2857142857142857, "support": 6}, "认识厘米": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识平行": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识折线统计图及其特点": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识整体的几分之几(整体是多个物体)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "认识毫米": {"precision": 0.5, "recall": 0.5, "f1-score": 0.5, "support": 2}, "认识直角": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "认识直角、锐角、钝角": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识米": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识线段": {"precision": 0.5, "recall": 0.3333333333333333, "f1-score": 0.4, "support": 3}, "认识角": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识锐角、钝角": {"precision": 0.25, "recall": 0.3333333333333333, "f1-score": 0.28571428571428575, "support": 3}, "读取复式条形统计图并回答问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "质量单位比较大小(吨、千克、克)": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "购物问题(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "辨析情景中分数大小（单位1不同）-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "辨认东、西、南、北四个方向": {"precision": 1.0, "recall": 0.6, "f1-score": 0.7499999999999999, "support": 5}, "运用三位数除以一位数的笔算解决实际问题(商是两位数)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用乘、除法的意义和各部分之间的关系解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用乘法交换律和结合律解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用乘法分配律解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用估算判断积的可能性": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "运用减法的运算性质解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用分数通分比较大小来解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用口算乘法解决实际问题": {"precision": 0.5, "recall": 0.4, "f1-score": 0.4444444444444445, "support": 5}, "运用数与形总结规律": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用积的变化规律解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用除数是一位数的除法估算解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "进一、去尾解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "连减与除法(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "连减的应用（10以内）": {"precision": 0.75, "recall": 0.6, "f1-score": 0.6666666666666665, "support": 5}, "连减解决实际问题（100以内）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "连加解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "连加计算(10以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "连续数(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "选择合适的长度单位(分米、毫米)": {"precision": 0.2, "recall": 0.3333333333333333, "f1-score": 0.25, "support": 3}, "选择合适的长度单位(千米、米、厘米、毫米)": {"precision": 1.0, "recall": 0.14285714285714285, "f1-score": 0.25, "support": 7}, "选择合适的长度单位(米和厘米)": {"precision": 0.8333333333333334, "recall": 1.0, "f1-score": 0.9090909090909091, "support": 5}, "选择合适的面积单位(公顷、平方千米)": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "速度、时间、路程之间的关系": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 2}, "锐角、直角、钝角、平角和周角之间的大小关系": {"precision": 0.3333333333333333, "recall": 1.0, "f1-score": 0.5, "support": 1}, "错中求解(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "错中求解(表内混合运算)": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 6}, "除加除减应用题(表内混合运算)": {"precision": 0.6666666666666666, "recall": 0.5, "f1-score": 0.5714285714285715, "support": 4}, "除数不接近整十数的试商": {"precision": 1.0, "recall": 0.16666666666666666, "f1-score": 0.2857142857142857, "support": 6}, "除数接近整十数的笔算方法(用四舍\"法试商)\"": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除数是一位数的填数问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除数是两位数的填数问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除数是两位数的除法数字谜": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法应用题(2-6)（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法意义的理解与运用（移后删）": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "除法的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法计算与运用(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "露出部分推整体": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}}}, "per_class_metrics": {"100以内数的组成": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "10的分与合": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "10的加减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "10的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "11-20各数的组成": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "11-20各数的读写": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "11-20各数的顺序": {"precision": 1.0, "recall": 0.75, "f1-score": 0.8571428571428571, "support": 4}, "2-4乘法口诀的应用题-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "2-5的乘加乘减应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "2-5的乘法应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "24点游戏": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "5、4、3、2加几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "5的乘法口诀应用题": {"precision": 1.0, "recall": 0.4, "f1-score": 0.5714285714285715, "support": 5}, "5的乘法口诀理解": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "6 、7的加减法的应用": {"precision": 0.3333333333333333, "recall": 0.3333333333333333, "f1-score": 0.3333333333333333, "support": 3}, "6-9的认识": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "6、7的减法的实际应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "6的乘法口诀应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "7-9的认识与读写": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "7-9连减与除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "8、7、6加几": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "8、9的分合的应用": {"precision": 0.25, "recall": 0.5, "f1-score": 0.3333333333333333, "support": 2}, "8、9的加减法的应用": {"precision": 0.75, "recall": 0.75, "f1-score": 0.75, "support": 4}, "8、9的加减法的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "8的乘法应用题（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "9的乘法口诀（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "一个因数变化的规律": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "三位数乘一位数(乘数中间有0)的笔算": {"precision": 0.6666666666666666, "recall": 0.5, "f1-score": 0.5714285714285715, "support": 8}, "三位数乘两位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "三位数乘两位数笔算": {"precision": 1.0, "recall": 0.8, "f1-score": 0.888888888888889, "support": 5}, "三位数加两、三位数(连续进位)的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以两位数(商是两位数)的笔算除法": {"precision": 0.6, "recall": 1.0, "f1-score": 0.7499999999999999, "support": 3}, "三位数除以两位数的笔算(有余数)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以整十数商是一位数的笔算除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以整十数的笔算除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三角尺拼角": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "不含括号的四则混合运算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "不含括号的四则混合运算的运算顺序": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "不含括号的表内混合运算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "与公顷、平方千米有关的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两、三位数乘一位数(不连续进位)的笔算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两、三位数乘一位数(连续进位)的笔算": {"precision": 0.5, "recall": 0.7142857142857143, "f1-score": 0.588235294117647, "support": 7}, "两、三位数乘一位数的估算": {"precision": 0.5, "recall": 0.25, "f1-score": 0.3333333333333333, "support": 4}, "两、三位数乘一位数的估算应用": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "两位数乘一位数的口算(不进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "两位数乘一位数的口算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两位数乘一位数的口算(有进位)的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘一位数的口算的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的笔算(不进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数减一位数（退位）解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数加两位数的口算的应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数除三位数的估算方法": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 2}, "两位数除以一位数的口算除法的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两点间的距离(三角形)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘、除法各部分间的关系的计算与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘加乘减应用题(表内混合运算)": {"precision": 0.3333333333333333, "recall": 0.6666666666666666, "f1-score": 0.4444444444444444, "support": 3}, "乘法交换律和结合律的综合运用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘法的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘除法的应用题(表内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "乘除法的计算与应用(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "亿以内数的读法": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "从不同位置观察同一几何组合体": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "以一当一的条形统计图": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "以一当二的条形统计图": {"precision": 0.5, "recall": 0.5, "f1-score": 0.5, "support": 2}, "以一当多的条形统计图": {"precision": 0.5, "recall": 0.2, "f1-score": 0.28571428571428575, "support": 5}, "优惠方案问题(表内除法)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "优惠策略": {"precision": 1.0, "recall": 0.5714285714285714, "f1-score": 0.7272727272727273, "support": 7}, "体积单位的认识": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "关于平行的相关判断": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "减号后添/去括号": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "减法的运算性质": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "几分之一的大小比较": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "几百几十数乘一位数的口算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数与除法关系的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘分数的实际应用": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "分数乘小数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "分数乘整数": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 14}, "分数乘整数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘法中因数与积的大小关系": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数加减乘混合运算的应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数方程解决实际应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "分数比大小综合(同分母/同分子)": {"precision": 1.0, "recall": 0.14285714285714285, "f1-score": 0.25, "support": 7}, "分数的大小比较": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数连乘的实际应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数量的理解（如3/4千克）-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数除法之和倍、差倍问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "分步解决问题的策略": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分米、厘米和毫米之间的单位换算": {"precision": 0.3333333333333333, "recall": 1.0, "f1-score": 0.5, "support": 2}, "分米、厘米和毫米之间的单位计算": {"precision": 0.3333333333333333, "recall": 0.5, "f1-score": 0.4, "support": 2}, "列多个算式的应用题（2-6含除法）-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "列方程解决实际问题ax=b(a≠0)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "初步认识立体图形": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "初步认识除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "判断商是几位数(除数是两位数)": {"precision": 1.0, "recall": 0.5333333333333333, "f1-score": 0.6956521739130436, "support": 15}, "利用小数点移动引起小数大小的变化解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "利用整体的几分之几解决问题": {"precision": 0.5, "recall": 0.09090909090909091, "f1-score": 0.15384615384615385, "support": 11}, "加、减、乘、除各部分间的关系的计算与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加减混合应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加减混合运用(10以内加减混合)": {"precision": 1.0, "recall": 0.16666666666666666, "f1-score": 0.2857142857142857, "support": 6}, "加减混合运算(10以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加小括号或省略括号问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加小括号的综合问题(表内混合运算)": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "加法应用题(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "包含分求份数（列除法算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "十几减5、4、3、2的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "十几减5、4、3、2的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "十几减7、6的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "十几减几的不退位减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "千克与克的相关应用题": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "千克和克之间的进率及换算": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "千米与米的换算": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "千米的认识": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "反比例的应用（补全题、选算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "口算两位数加一位数(进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "合理安排时间": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数减法在实际生活的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数减法的计算、运用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数加、减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "同分母分数加、减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "同分母分数加法的含义及计算方法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数加法的计算、运用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同级运算的运算顺序": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 2}, "吨、千克和克之间的进率及换算": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "含有两级运算的运算顺序": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "含有小括号的表内混合运算": {"precision": 0.8, "recall": 0.6666666666666666, "f1-score": 0.7272727272727272, "support": 6}, "商不变的性质（除数是一位数）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "商不变的规律": {"precision": 0.7142857142857143, "recall": 0.7142857142857143, "f1-score": 0.7142857142857143, "support": 14}, "商不变规律中余数的变化": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 5}, "商的变化规律": {"precision": 0.8, "recall": 1.0, "f1-score": 0.888888888888889, "support": 4}, "四个方向描述简单的行走路线": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "图形的变化规律": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "在平面图上辨认东、南、西、北": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 2}, "在统计表中计算平均数": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "填质量单位(克和千克)": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "多个小数的大小比较": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "多种立体图形的拼搭": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "够不够问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "封闭路线植树问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "将分步算式改写成带小括号或中括号的综合算式": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数乘分数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "已知一个数的几分之几是多少，求这个数（考法需整合）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "已知总量和分量比，求分量": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "已知经过时间，求开始（结束）时刻": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分与分数-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分求每份数（列除法算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分的综合理解与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "应用几、第几及左右/前后关系解决问题": {"precision": 0.6666666666666666, "recall": 0.5, "f1-score": 0.5714285714285715, "support": 4}, "异分母分数加法在实际生活的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "按角分类的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "数的组成（20以内）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "数线段、直线和射线": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 3}, "数角(角的初步认识)": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 6}, "整十、整百、整千数乘一位数的口算": {"precision": 1.0, "recall": 0.4, "f1-score": 0.5714285714285715, "support": 10}, "整十、整百、整千数除以一位数的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整数乘分数": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "整百、整千数、几百几十数的加减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "方向变化推理题（四个方向）": {"precision": 0.3333333333333333, "recall": 0.5, "f1-score": 0.4, "support": 2}, "有隐藏条件的除法应用题(1-9)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "有隐藏条件的除法应用题(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "根据公式求时间": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "根据公式求路程": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "根据分步运算列综合算式(含括号)(表内混合运算)": {"precision": 1.0, "recall": 0.4, "f1-score": 0.5714285714285715, "support": 5}, "根据单式折线统计图分析、预测": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "根据描述确定位置（八个方向）": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "根据描述确定位置（四个方向）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "梯形周长的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "梯形的概念": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "求一个数是另一个数的几分之几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "求一个数的几倍是多少": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "求两数公因数、最大公因数的特殊情况": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "沏茶问题": {"precision": 1.0, "recall": 0.75, "f1-score": 0.8571428571428571, "support": 4}, "洒水车、收割机问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "测量方式": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "添加分数单位后，变成某数": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "灵活选择估算策略解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "点到直线的距离应用": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3}, "烙饼问题": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 5}, "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "理解一个数的几分之几是多少(分数乘法)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "理解加法与减法之间的互逆关系（十几减5、4、3、2）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "理解平均分": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "用三位数除以两位数(商是两位数)解决简单的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 8}, "用三位数除以整十数解决简单的实际问题": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 9}, "用两步计算解决问题(表内混合运算)(不含括号)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 10}, "用乘法口诀求商(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用乘法和除法两步计算解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "用乘除混合解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用公倍数、最小公倍数解决简单的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用含有括号的四则混合运算解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用平移解决面积问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用连除解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "田忌赛马": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "画线段、直线和射线": {"precision": 0.75, "recall": 1.0, "f1-score": 0.8571428571428571, "support": 3}, "画线添角": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "画角": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "直尺测量（厘米）": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "相同正方体拼搭": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "相对方向(四个方向)": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1-score": 0.6666666666666666, "support": 3}, "看图列综合算式并计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "租船/车问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "积不变的规律的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "积末尾或中间0的个数的问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "稍复杂的求一个数的几分之几是多少": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "立体图形中的平面图形": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "笔算两位数加两位数(进位加)解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "等分(按指定的份数平均分)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "等腰梯形周长相关的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "简单求一个数的几分之几是多少": {"precision": 0.25, "recall": 0.14285714285714285, "f1-score": 0.18181818181818182, "support": 7}, "算式中有0的加减法": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "线段的再认识": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "组合图形数小正方体数量的规律": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "组合思想解决问题": {"precision": 0.6666666666666666, "recall": 1.0, "f1-score": 0.8, "support": 2}, "绳长对折问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "角度的简单计算--折叠": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "角度直接运算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "角的初步认识的应用(直角、锐角、钝角)": {"precision": 1.0, "recall": 0.2, "f1-score": 0.33333333333333337, "support": 5}, "角的大小比较(初步认识角)": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 2}, "解决两、三位数乘一位数(不进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "解决两、三位数乘一位数(连续进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "解决千米相关的实际问题": {"precision": 1.0, "recall": 0.14285714285714285, "f1-score": 0.25, "support": 7}, "解决吨相关的实际问题": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1-score": 0.6666666666666666, "support": 3}, "解决小数与单位换算的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "解决有关分米、厘米、毫米的问题": {"precision": 1.0, "recall": 0.6, "f1-score": 0.7499999999999999, "support": 5}, "认识三角尺": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识东北、东南、西北、西南四个方向": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识克": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "认识几分之几": {"precision": 1.0, "recall": 0.16666666666666666, "f1-score": 0.2857142857142857, "support": 6}, "认识厘米": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识平行": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识折线统计图及其特点": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识整体的几分之几(整体是多个物体)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "认识毫米": {"precision": 0.5, "recall": 0.5, "f1-score": 0.5, "support": 2}, "认识直角": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "认识直角、锐角、钝角": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识米": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识线段": {"precision": 0.5, "recall": 0.3333333333333333, "f1-score": 0.4, "support": 3}, "认识角": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识锐角、钝角": {"precision": 0.25, "recall": 0.3333333333333333, "f1-score": 0.28571428571428575, "support": 3}, "读取复式条形统计图并回答问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "质量单位比较大小(吨、千克、克)": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "购物问题(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "辨析情景中分数大小（单位1不同）-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "辨认东、西、南、北四个方向": {"precision": 1.0, "recall": 0.6, "f1-score": 0.7499999999999999, "support": 5}, "运用三位数除以一位数的笔算解决实际问题(商是两位数)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用乘、除法的意义和各部分之间的关系解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用乘法交换律和结合律解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用乘法分配律解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用估算判断积的可能性": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "运用减法的运算性质解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用分数通分比较大小来解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用口算乘法解决实际问题": {"precision": 0.5, "recall": 0.4, "f1-score": 0.4444444444444445, "support": 5}, "运用数与形总结规律": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用积的变化规律解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用除数是一位数的除法估算解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "进一、去尾解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "连减与除法(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "连减的应用（10以内）": {"precision": 0.75, "recall": 0.6, "f1-score": 0.6666666666666665, "support": 5}, "连减解决实际问题（100以内）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "连加解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "连加计算(10以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "连续数(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "选择合适的长度单位(分米、毫米)": {"precision": 0.2, "recall": 0.3333333333333333, "f1-score": 0.25, "support": 3}, "选择合适的长度单位(千米、米、厘米、毫米)": {"precision": 1.0, "recall": 0.14285714285714285, "f1-score": 0.25, "support": 7}, "选择合适的长度单位(米和厘米)": {"precision": 0.8333333333333334, "recall": 1.0, "f1-score": 0.9090909090909091, "support": 5}, "选择合适的面积单位(公顷、平方千米)": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "速度、时间、路程之间的关系": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 2}, "锐角、直角、钝角、平角和周角之间的大小关系": {"precision": 0.3333333333333333, "recall": 1.0, "f1-score": 0.5, "support": 1}, "错中求解(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "错中求解(表内混合运算)": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 6}, "除加除减应用题(表内混合运算)": {"precision": 0.6666666666666666, "recall": 0.5, "f1-score": 0.5714285714285715, "support": 4}, "除数不接近整十数的试商": {"precision": 1.0, "recall": 0.16666666666666666, "f1-score": 0.2857142857142857, "support": 6}, "除数接近整十数的笔算方法(用四舍\"法试商)\"": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除数是一位数的填数问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除数是两位数的填数问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除数是两位数的除法数字谜": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法应用题(2-6)（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法意义的理解与运用（移后删）": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "除法的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法计算与运用(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "露出部分推整体": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}}, "evaluation_summary": {"total_samples": 471, "unique_true_labels": 147, "unique_pred_labels": 245, "avg_similarity_score": 1.0, "num_classes": 282}, "model_info": {"model_type": "LLaMA-Factory Fine-tuned Model", "model_path": "Qwen3-8B-Thinking/lora", "evaluation_date": "2025-09-15-09-13-54", "total_samples": 471}}