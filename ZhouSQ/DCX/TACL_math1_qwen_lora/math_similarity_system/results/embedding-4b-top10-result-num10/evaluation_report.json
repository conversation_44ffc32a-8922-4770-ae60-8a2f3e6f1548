{"overall_metrics": {"micro_precision": 0.21810699588477367, "micro_recall": 0.21810699588477367, "micro_f1": 0.21810699588477367, "macro_precision": 0.15841666666666668, "macro_recall": 0.08389285714285714, "macro_f1": 0.09995915032679738, "weighted_precision": 0.21810699588477367, "weighted_recall": 0.21810699588477367, "weighted_f1": 0.21810699588477367, "exact_match_accuracy": 0.21810699588477367, "hamming_loss": 0.7818930041152263, "num_samples": 243, "num_classes": 200}, "prediction_source_metrics": {"vector_db": {"count": 243, "percentage": 100.0, "accuracy": 0.21810699588477367}}, "similarity_threshold_analysis": {"above_threshold_count": 243, "below_threshold_count": 0, "threshold_value": 1e-05, "above_threshold_avg_similarity": 0.7691565612216055, "above_threshold_min_similarity": 0.3137668967247009, "above_threshold_max_similarity": 0.9718872308731079}, "prediction_accuracy": {"overall_accuracy_topk": 0.4691358024691358, "overall_accuracy_top1": 0.21810699588477367, "total_correct_topk": 114, "total_correct_top1": 53, "total_samples": 243, "vector_db_accuracy_topk": 0.4691358024691358, "vector_db_correct": 114, "vector_db_total": 243, "model_accuracy": 0.0, "model_correct": 0, "model_total": 0, "improvement_topk_vs_top1": 0.25102880658436216}, "leaf_node_metrics": {"leaf_node_accuracy": 0.21810699588477367, "leaf_node_precision": 0.21810699588477367, "leaf_node_recall": 0.21810699588477367, "leaf_node_f1": 0.21810699588477367, "leaf_node_macro_precision": 0.15841666666666668, "leaf_node_macro_recall": 0.08389285714285714, "leaf_node_macro_f1": 0.09995915032679738, "leaf_node_micro_precision": 0.21810699588477367, "leaf_node_micro_recall": 0.21810699588477367, "leaf_node_micro_f1": 0.21810699588477367, "num_leaf_classes": 200, "leaf_class_distribution": {"10以内数的比大小": {"true_count": 0, "pred_count": 1, "support": 0}, "10的分与合": {"true_count": 0, "pred_count": 1, "support": 0}, "10的加减法的应用": {"true_count": 3, "pred_count": 0, "support": 3}, "11-20各数的顺序": {"true_count": 4, "pred_count": 1, "support": 4}, "2-4乘法口诀的应用题-": {"true_count": 1, "pred_count": 1, "support": 1}, "2-5的乘加、乘减计算": {"true_count": 0, "pred_count": 1, "support": 0}, "2-5的乘加乘减应用题": {"true_count": 3, "pred_count": 1, "support": 3}, "24时计算经过的时间": {"true_count": 0, "pred_count": 1, "support": 0}, "24点游戏": {"true_count": 0, "pred_count": 1, "support": 0}, "6 、7的加减法的应用": {"true_count": 3, "pred_count": 1, "support": 3}, "6的乘法口诀应用题": {"true_count": 3, "pred_count": 0, "support": 3}, "7-9连减与除法": {"true_count": 1, "pred_count": 0, "support": 1}, "7的乘法应用题（旧版）": {"true_count": 0, "pred_count": 1, "support": 0}, "8、7、6加几": {"true_count": 1, "pred_count": 0, "support": 1}, "8、9的分合的应用": {"true_count": 2, "pred_count": 2, "support": 2}, "一个数除以分数的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "三位数乘两位数的估算": {"true_count": 3, "pred_count": 1, "support": 3}, "三位数乘两位数笔算": {"true_count": 5, "pred_count": 1, "support": 5}, "三位数除以一位数(每一位都能整除)的笔算": {"true_count": 0, "pred_count": 1, "support": 0}, "三位数除以一位数的应用(商是两位数)": {"true_count": 0, "pred_count": 2, "support": 0}, "三位数除以两位数(商是两位数)的笔算除法": {"true_count": 0, "pred_count": 1, "support": 0}, "三角形面积公式逆用": {"true_count": 0, "pred_count": 1, "support": 0}, "不含括号的表内混合运算": {"true_count": 3, "pred_count": 1, "support": 3}, "两、三位数乘一位数(不连续进位)的笔算": {"true_count": 1, "pred_count": 0, "support": 1}, "两、三位数乘一位数的估算": {"true_count": 4, "pred_count": 0, "support": 4}, "两、三位数乘一位数的估算应用": {"true_count": 4, "pred_count": 0, "support": 4}, "两位数乘一位数的口算(不进位)": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数乘一位数的口算(有进位)": {"true_count": 1, "pred_count": 0, "support": 1}, "两位数乘两位数的估算": {"true_count": 0, "pred_count": 3, "support": 0}, "两位数乘两位数的笔算(不进位)": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数乘两位数的笔算(有进位)": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数乘两位数的笔算(有进位)的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数乘整十数的应用": {"true_count": 0, "pred_count": 2, "support": 0}, "两位数乘整十（百）数的口算": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数乘整百数的口算及应用": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数减一位数（退位）的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数减两位数(退位减)的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数除以一位数(被除数首位能被整除)的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数除以一位数的口算除法的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "两位数除以一位数的口算除法的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "乘加乘减应用题(表内混合运算)": {"true_count": 3, "pred_count": 3, "support": 3}, "乘除法的应用题(表内)": {"true_count": 1, "pred_count": 0, "support": 1}, "乘除法的计算与应用(2-6)": {"true_count": 0, "pred_count": 2, "support": 0}, "优惠策略": {"true_count": 7, "pred_count": 1, "support": 7}, "几百几十数乘一位数的口算(有进位)": {"true_count": 0, "pred_count": 2, "support": 0}, "分数与除法关系的应用-": {"true_count": 0, "pred_count": 3, "support": 0}, "分数乘分数的实际应用": {"true_count": 0, "pred_count": 1, "support": 0}, "分数乘小数的计算": {"true_count": 0, "pred_count": 1, "support": 0}, "分数乘整数": {"true_count": 14, "pred_count": 3, "support": 14}, "分数单位的意义": {"true_count": 0, "pred_count": 1, "support": 0}, "分数的意义": {"true_count": 0, "pred_count": 2, "support": 0}, "分步解决问题的策略": {"true_count": 0, "pred_count": 1, "support": 0}, "列多个算式的应用题（2-6含除法）-": {"true_count": 3, "pred_count": 0, "support": 3}, "初步认识除法": {"true_count": 2, "pred_count": 0, "support": 2}, "利用小数加减法解决综合问题": {"true_count": 0, "pred_count": 1, "support": 0}, "利用整体的几分之几解决问题": {"true_count": 0, "pred_count": 1, "support": 0}, "加减法(整十数加一位数及相应的减法)解决实际问题": {"true_count": 0, "pred_count": 2, "support": 0}, "包含分求份数（列除法算式）": {"true_count": 1, "pred_count": 1, "support": 1}, "十几减5、4、3、2的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "十几减几的不退位减法": {"true_count": 1, "pred_count": 0, "support": 1}, "千克和克之间的进率及换算": {"true_count": 2, "pred_count": 1, "support": 2}, "口算两位数加一位数(不进位)的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "口算两位数加整十数(不进位)解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "合理安排时间": {"true_count": 0, "pred_count": 2, "support": 0}, "同分母分数减法的含义及计算方法": {"true_count": 0, "pred_count": 1, "support": 0}, "同分母分数减法的计算、运用-": {"true_count": 0, "pred_count": 1, "support": 0}, "同分母分数加、减法的应用": {"true_count": 2, "pred_count": 0, "support": 2}, "含有小括号的表内混合运算": {"true_count": 6, "pred_count": 3, "support": 6}, "将分步算式改写成带小括号或中括号的综合算式": {"true_count": 0, "pred_count": 2, "support": 0}, "小数加、减法混合运算解决问题（一位小数）": {"true_count": 0, "pred_count": 1, "support": 0}, "小数加减法应用题": {"true_count": 0, "pred_count": 3, "support": 0}, "小数加减法应用题(涉及小数简算)": {"true_count": 0, "pred_count": 1, "support": 0}, "小数连减简算": {"true_count": 0, "pred_count": 1, "support": 0}, "平均分与分数-": {"true_count": 0, "pred_count": 1, "support": 0}, "平均分求每份数（列除法算式）": {"true_count": 0, "pred_count": 1, "support": 0}, "平均分的综合理解与应用": {"true_count": 1, "pred_count": 1, "support": 1}, "异分母分数连减在实际生活中的应用-": {"true_count": 0, "pred_count": 1, "support": 0}, "数的相对大小关系(100以内)": {"true_count": 0, "pred_count": 2, "support": 0}, "整万数的改写": {"true_count": 0, "pred_count": 1, "support": 0}, "整十、整百、整千数乘一位数的口算": {"true_count": 10, "pred_count": 0, "support": 10}, "整十、整百、整千数除以一位数的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "整十、整百、整千数除以一位数的应用": {"true_count": 0, "pred_count": 1, "support": 0}, "整数的四则混合运算": {"true_count": 0, "pred_count": 1, "support": 0}, "整百、整千数、几百几十数的加减法": {"true_count": 1, "pred_count": 1, "support": 1}, "有余数除法竖式": {"true_count": 0, "pred_count": 1, "support": 0}, "有隐藏条件的除法应用题(2-6)": {"true_count": 0, "pred_count": 1, "support": 0}, "根据公式求路程": {"true_count": 1, "pred_count": 0, "support": 1}, "根据分步运算列综合算式(含括号)(表内混合运算)": {"true_count": 5, "pred_count": 1, "support": 5}, "求一个数比另一个数多（少）几": {"true_count": 0, "pred_count": 1, "support": 0}, "求两数公因数、最大公因数的特殊情况": {"true_count": 0, "pred_count": 2, "support": 0}, "沏茶问题": {"true_count": 4, "pred_count": 2, "support": 4}, "灵活选择估算策略解决问题": {"true_count": 0, "pred_count": 2, "support": 0}, "烙饼问题": {"true_count": 5, "pred_count": 4, "support": 5}, "理解平均分": {"true_count": 2, "pred_count": 1, "support": 2}, "用三位数除以两位数(商是两位数)解决简单的实际问题": {"true_count": 8, "pred_count": 0, "support": 8}, "用三位数除以整十数解决简单的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "用两步计算解决问题(表内混合运算)(不含括号)": {"true_count": 10, "pred_count": 2, "support": 10}, "用乘法和除法两步计算解决问题": {"true_count": 4, "pred_count": 2, "support": 4}, "用乘除混合解决实际问题": {"true_count": 0, "pred_count": 4, "support": 0}, "用求公因数的方法解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "用连乘解决实际问题": {"true_count": 0, "pred_count": 2, "support": 0}, "用连除解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "用除法解决问题": {"true_count": 0, "pred_count": 1, "support": 0}, "田忌赛马": {"true_count": 1, "pred_count": 1, "support": 1}, "看图列综合算式并计算": {"true_count": 3, "pred_count": 0, "support": 3}, "积末尾0的个数（口算）": {"true_count": 0, "pred_count": 1, "support": 0}, "笔算两位数加两位数(进位加)解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "等分(按指定的份数平均分)": {"true_count": 0, "pred_count": 1, "support": 0}, "简单求一个数的几分之几是多少": {"true_count": 7, "pred_count": 1, "support": 7}, "补充知识点1690": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点1771": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点1815": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点1817": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点1841": {"true_count": 0, "pred_count": 2, "support": 0}, "补充知识点1843": {"true_count": 2, "pred_count": 0, "support": 2}, "补充知识点1845": {"true_count": 2, "pred_count": 4, "support": 2}, "补充知识点1849": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点1853": {"true_count": 1, "pred_count": 1, "support": 1}, "补充知识点1881": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点1901": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2040": {"true_count": 14, "pred_count": 1, "support": 14}, "补充知识点2043": {"true_count": 4, "pred_count": 4, "support": 4}, "补充知识点2070": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2275": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2336": {"true_count": 1, "pred_count": 0, "support": 1}, "补充知识点2405": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2457": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2465": {"true_count": 0, "pred_count": 2, "support": 0}, "补充知识点2505": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2654": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2680": {"true_count": 1, "pred_count": 0, "support": 1}, "补充知识点2686": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2688": {"true_count": 4, "pred_count": 1, "support": 4}, "补充知识点2826": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2828": {"true_count": 0, "pred_count": 3, "support": 0}, "补充知识点2879": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点2897": {"true_count": 2, "pred_count": 1, "support": 2}, "补充知识点2919": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点3105": {"true_count": 0, "pred_count": 2, "support": 0}, "补充知识点3131": {"true_count": 2, "pred_count": 1, "support": 2}, "补充知识点3153": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点320": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点3201": {"true_count": 3, "pred_count": 1, "support": 3}, "补充知识点3207": {"true_count": 3, "pred_count": 0, "support": 3}, "补充知识点3410": {"true_count": 0, "pred_count": 2, "support": 0}, "补充知识点3418": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点3563": {"true_count": 0, "pred_count": 2, "support": 0}, "补充知识点3648": {"true_count": 1, "pred_count": 2, "support": 1}, "补充知识点3709": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点392": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点3983": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点4004": {"true_count": 1, "pred_count": 0, "support": 1}, "补充知识点4212": {"true_count": 1, "pred_count": 0, "support": 1}, "补充知识点4214": {"true_count": 2, "pred_count": 0, "support": 2}, "补充知识点4222": {"true_count": 5, "pred_count": 5, "support": 5}, "补充知识点4224": {"true_count": 3, "pred_count": 3, "support": 3}, "补充知识点4228": {"true_count": 2, "pred_count": 2, "support": 2}, "补充知识点4230": {"true_count": 1, "pred_count": 2, "support": 1}, "补充知识点4234": {"true_count": 1, "pred_count": 0, "support": 1}, "补充知识点4238": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点4244": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点4351": {"true_count": 3, "pred_count": 1, "support": 3}, "补充知识点4383": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点4478": {"true_count": 3, "pred_count": 1, "support": 3}, "补充知识点4482": {"true_count": 5, "pred_count": 1, "support": 5}, "补充知识点4484": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点4502": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点4525": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点4820": {"true_count": 0, "pred_count": 2, "support": 0}, "补充知识点484": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点4841": {"true_count": 6, "pred_count": 4, "support": 6}, "补充知识点4942": {"true_count": 2, "pred_count": 1, "support": 2}, "补充知识点5014": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点5254": {"true_count": 3, "pred_count": 3, "support": 3}, "补充知识点5284": {"true_count": 0, "pred_count": 1, "support": 0}, "补充知识点5448": {"true_count": 0, "pred_count": 1, "support": 0}, "解决两、三位数乘一位数(连续进位)的实际问题": {"true_count": 1, "pred_count": 0, "support": 1}, "解决有关2的倍数的实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "计数器表示数(1000以内)": {"true_count": 0, "pred_count": 1, "support": 0}, "认识几分之几": {"true_count": 6, "pred_count": 0, "support": 6}, "认识毫米": {"true_count": 2, "pred_count": 0, "support": 2}, "认识质量单位吨\"\"": {"true_count": 0, "pred_count": 1, "support": 0}, "质量单位比较大小(吨、千克、克)": {"true_count": 0, "pred_count": 1, "support": 0}, "运用一位小数加法解决问题": {"true_count": 0, "pred_count": 1, "support": 0}, "运用两位数乘整十（百）数的口算解决实际问题": {"true_count": 0, "pred_count": 1, "support": 0}, "运用分数通分比较大小来解决问题": {"true_count": 0, "pred_count": 2, "support": 0}, "进一、去尾解决问题": {"true_count": 0, "pred_count": 1, "support": 0}, "连乘的计算与应用": {"true_count": 0, "pred_count": 1, "support": 0}, "连减与除法(2-6)": {"true_count": 2, "pred_count": 0, "support": 2}, "连加解决实际问题": {"true_count": 1, "pred_count": 0, "support": 1}, "连续数(100以内)": {"true_count": 0, "pred_count": 3, "support": 0}, "速度、时间、路程之间的关系": {"true_count": 2, "pred_count": 1, "support": 2}, "间隔问题的相关应用": {"true_count": 0, "pred_count": 1, "support": 0}, "除加除减应用题(表内混合运算)": {"true_count": 4, "pred_count": 0, "support": 4}, "除数不接近整十数的试商": {"true_count": 0, "pred_count": 1, "support": 0}, "除数是一位数的估算": {"true_count": 0, "pred_count": 5, "support": 0}, "除法应用题(2-6)（旧版）": {"true_count": 0, "pred_count": 2, "support": 0}, "除法应用题(7、8、9)": {"true_count": 0, "pred_count": 3, "support": 0}, "除法意义的理解与运用（移后删）": {"true_count": 1, "pred_count": 1, "support": 1}, "除法计算与运用(2-6)": {"true_count": 0, "pred_count": 3, "support": 0}}, "leaf_class_report": {"10以内数的比大小": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "10的分与合": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "10的加减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "11-20各数的顺序": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "2-4乘法口诀的应用题-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "2-5的乘加、乘减计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "2-5的乘加乘减应用题": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "24时计算经过的时间": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "24点游戏": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "6 、7的加减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "6的乘法口诀应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "7-9连减与除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "7的乘法应用题（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "8、7、6加几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "8、9的分合的应用": {"precision": 0.5, "recall": 0.5, "f1-score": 0.5, "support": 2}, "一个数除以分数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数乘两位数的估算": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "三位数乘两位数笔算": {"precision": 1.0, "recall": 0.2, "f1-score": 0.33333333333333337, "support": 5}, "三位数除以一位数(每一位都能整除)的笔算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以一位数的应用(商是两位数)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以两位数(商是两位数)的笔算除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三角形面积公式逆用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "不含括号的表内混合运算": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "两、三位数乘一位数(不连续进位)的笔算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两、三位数乘一位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "两、三位数乘一位数的估算应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "两位数乘一位数的口算(不进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘一位数的口算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两位数乘两位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的笔算(不进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的笔算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的笔算(有进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘整十数的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘整十（百）数的口算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘整百数的口算及应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数减一位数（退位）的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数减两位数(退位减)的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数除以一位数(被除数首位能被整除)的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数除以一位数的口算除法的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数除以一位数的口算除法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘加乘减应用题(表内混合运算)": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1-score": 0.6666666666666666, "support": 3}, "乘除法的应用题(表内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "乘除法的计算与应用(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "优惠策略": {"precision": 1.0, "recall": 0.14285714285714285, "f1-score": 0.25, "support": 7}, "几百几十数乘一位数的口算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数与除法关系的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘分数的实际应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘小数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘整数": {"precision": 1.0, "recall": 0.21428571428571427, "f1-score": 0.35294117647058826, "support": 14}, "分数单位的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分步解决问题的策略": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "列多个算式的应用题（2-6含除法）-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "初步认识除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "利用小数加减法解决综合问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "利用整体的几分之几解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加减法(整十数加一位数及相应的减法)解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "包含分求份数（列除法算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "十几减5、4、3、2的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "十几减几的不退位减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "千克和克之间的进率及换算": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "口算两位数加一位数(不进位)的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "口算两位数加整十数(不进位)解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "合理安排时间": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数减法的含义及计算方法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数减法的计算、运用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数加、减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "含有小括号的表内混合运算": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 6}, "将分步算式改写成带小括号或中括号的综合算式": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数加、减法混合运算解决问题（一位小数）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数加减法应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数加减法应用题(涉及小数简算)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数连减简算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分与分数-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分求每份数（列除法算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分的综合理解与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "异分母分数连减在实际生活中的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "数的相对大小关系(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整万数的改写": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整十、整百、整千数乘一位数的口算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 10}, "整十、整百、整千数除以一位数的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整十、整百、整千数除以一位数的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整数的四则混合运算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整百、整千数、几百几十数的加减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "有余数除法竖式": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "有隐藏条件的除法应用题(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "根据公式求路程": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "根据分步运算列综合算式(含括号)(表内混合运算)": {"precision": 1.0, "recall": 0.2, "f1-score": 0.33333333333333337, "support": 5}, "求一个数比另一个数多（少）几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "求两数公因数、最大公因数的特殊情况": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "沏茶问题": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 4}, "灵活选择估算策略解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "烙饼问题": {"precision": 1.0, "recall": 0.8, "f1-score": 0.888888888888889, "support": 5}, "理解平均分": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "用三位数除以两位数(商是两位数)解决简单的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 8}, "用三位数除以整十数解决简单的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用两步计算解决问题(表内混合运算)(不含括号)": {"precision": 0.5, "recall": 0.1, "f1-score": 0.16666666666666669, "support": 10}, "用乘法和除法两步计算解决问题": {"precision": 0.5, "recall": 0.25, "f1-score": 0.3333333333333333, "support": 4}, "用乘除混合解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用求公因数的方法解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用连乘解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用连除解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用除法解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "田忌赛马": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "看图列综合算式并计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "积末尾0的个数（口算）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "笔算两位数加两位数(进位加)解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "等分(按指定的份数平均分)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "简单求一个数的几分之几是多少": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 7}, "补充知识点1690": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1771": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1815": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1817": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1841": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1843": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "补充知识点1845": {"precision": 0.25, "recall": 0.5, "f1-score": 0.3333333333333333, "support": 2}, "补充知识点1849": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1853": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "补充知识点1881": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1901": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2040": {"precision": 1.0, "recall": 0.07142857142857142, "f1-score": 0.13333333333333333, "support": 14}, "补充知识点2043": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "补充知识点2070": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2275": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2336": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点2405": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2457": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2465": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2505": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2654": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2680": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点2686": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2688": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "补充知识点2826": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2828": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2879": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2897": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "补充知识点2919": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3105": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3131": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "补充知识点3153": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点320": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3201": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "补充知识点3207": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "补充知识点3410": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3418": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3563": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3648": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "补充知识点3709": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点392": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3983": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4004": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点4212": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点4214": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "补充知识点4222": {"precision": 0.6, "recall": 0.6, "f1-score": 0.6, "support": 5}, "补充知识点4224": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1-score": 0.6666666666666666, "support": 3}, "补充知识点4228": {"precision": 0.5, "recall": 0.5, "f1-score": 0.5, "support": 2}, "补充知识点4230": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点4234": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点4238": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4244": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4351": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "补充知识点4383": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4478": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "补充知识点4482": {"precision": 1.0, "recall": 0.2, "f1-score": 0.33333333333333337, "support": 5}, "补充知识点4484": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4502": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4525": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4820": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点484": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4841": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 6}, "补充知识点4942": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "补充知识点5014": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点5254": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3}, "补充知识点5284": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点5448": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "解决两、三位数乘一位数(连续进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "解决有关2的倍数的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "计数器表示数(1000以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识几分之几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 6}, "认识毫米": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "认识质量单位吨\"\"": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "质量单位比较大小(吨、千克、克)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用一位小数加法解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用两位数乘整十（百）数的口算解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用分数通分比较大小来解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "进一、去尾解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "连乘的计算与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "连减与除法(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "连加解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "连续数(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "速度、时间、路程之间的关系": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "间隔问题的相关应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除加除减应用题(表内混合运算)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "除数不接近整十数的试商": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除数是一位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法应用题(2-6)（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法应用题(7、8、9)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法意义的理解与运用（移后删）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "除法计算与运用(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}}}, "per_class_metrics": {"10以内数的比大小": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "10的分与合": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "10的加减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "11-20各数的顺序": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "2-4乘法口诀的应用题-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "2-5的乘加、乘减计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "2-5的乘加乘减应用题": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "24时计算经过的时间": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "24点游戏": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "6 、7的加减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "6的乘法口诀应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "7-9连减与除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "7的乘法应用题（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "8、7、6加几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "8、9的分合的应用": {"precision": 0.5, "recall": 0.5, "f1-score": 0.5, "support": 2}, "一个数除以分数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数乘两位数的估算": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "三位数乘两位数笔算": {"precision": 1.0, "recall": 0.2, "f1-score": 0.33333333333333337, "support": 5}, "三位数除以一位数(每一位都能整除)的笔算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以一位数的应用(商是两位数)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三位数除以两位数(商是两位数)的笔算除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "三角形面积公式逆用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "不含括号的表内混合运算": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "两、三位数乘一位数(不连续进位)的笔算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两、三位数乘一位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "两、三位数乘一位数的估算应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "两位数乘一位数的口算(不进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘一位数的口算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "两位数乘两位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的笔算(不进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的笔算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘两位数的笔算(有进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘整十数的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘整十（百）数的口算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数乘整百数的口算及应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数减一位数（退位）的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数减两位数(退位减)的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数除以一位数(被除数首位能被整除)的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数除以一位数的口算除法的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "两位数除以一位数的口算除法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "乘加乘减应用题(表内混合运算)": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1-score": 0.6666666666666666, "support": 3}, "乘除法的应用题(表内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "乘除法的计算与应用(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "优惠策略": {"precision": 1.0, "recall": 0.14285714285714285, "f1-score": 0.25, "support": 7}, "几百几十数乘一位数的口算(有进位)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数与除法关系的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘分数的实际应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘小数的计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数乘整数": {"precision": 1.0, "recall": 0.21428571428571427, "f1-score": 0.35294117647058826, "support": 14}, "分数单位的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分数的意义": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "分步解决问题的策略": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "列多个算式的应用题（2-6含除法）-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "初步认识除法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "利用小数加减法解决综合问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "利用整体的几分之几解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "加减法(整十数加一位数及相应的减法)解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "包含分求份数（列除法算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "十几减5、4、3、2的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "十几减几的不退位减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "千克和克之间的进率及换算": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "口算两位数加一位数(不进位)的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "口算两位数加整十数(不进位)解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "合理安排时间": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数减法的含义及计算方法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数减法的计算、运用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "同分母分数加、减法的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "含有小括号的表内混合运算": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 6}, "将分步算式改写成带小括号或中括号的综合算式": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数加、减法混合运算解决问题（一位小数）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数加减法应用题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数加减法应用题(涉及小数简算)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "小数连减简算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分与分数-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分求每份数（列除法算式）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "平均分的综合理解与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "异分母分数连减在实际生活中的应用-": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "数的相对大小关系(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整万数的改写": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整十、整百、整千数乘一位数的口算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 10}, "整十、整百、整千数除以一位数的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整十、整百、整千数除以一位数的应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整数的四则混合运算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "整百、整千数、几百几十数的加减法": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "有余数除法竖式": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "有隐藏条件的除法应用题(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "根据公式求路程": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "根据分步运算列综合算式(含括号)(表内混合运算)": {"precision": 1.0, "recall": 0.2, "f1-score": 0.33333333333333337, "support": 5}, "求一个数比另一个数多（少）几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "求两数公因数、最大公因数的特殊情况": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "沏茶问题": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 4}, "灵活选择估算策略解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "烙饼问题": {"precision": 1.0, "recall": 0.8, "f1-score": 0.888888888888889, "support": 5}, "理解平均分": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "用三位数除以两位数(商是两位数)解决简单的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 8}, "用三位数除以整十数解决简单的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用两步计算解决问题(表内混合运算)(不含括号)": {"precision": 0.5, "recall": 0.1, "f1-score": 0.16666666666666669, "support": 10}, "用乘法和除法两步计算解决问题": {"precision": 0.5, "recall": 0.25, "f1-score": 0.3333333333333333, "support": 4}, "用乘除混合解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用求公因数的方法解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用连乘解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用连除解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "用除法解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "田忌赛马": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "看图列综合算式并计算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "积末尾0的个数（口算）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "笔算两位数加两位数(进位加)解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "等分(按指定的份数平均分)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "简单求一个数的几分之几是多少": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 7}, "补充知识点1690": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1771": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1815": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1817": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1841": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1843": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "补充知识点1845": {"precision": 0.25, "recall": 0.5, "f1-score": 0.3333333333333333, "support": 2}, "补充知识点1849": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1853": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1}, "补充知识点1881": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点1901": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2040": {"precision": 1.0, "recall": 0.07142857142857142, "f1-score": 0.13333333333333333, "support": 14}, "补充知识点2043": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "补充知识点2070": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2275": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2336": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点2405": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2457": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2465": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2505": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2654": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2680": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点2686": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2688": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4}, "补充知识点2826": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2828": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2879": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点2897": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "补充知识点2919": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3105": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3131": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "补充知识点3153": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点320": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3201": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "补充知识点3207": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3}, "补充知识点3410": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3418": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3563": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3648": {"precision": 0.5, "recall": 1.0, "f1-score": 0.6666666666666666, "support": 1}, "补充知识点3709": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点392": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点3983": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4004": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点4212": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点4214": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "补充知识点4222": {"precision": 0.6, "recall": 0.6, "f1-score": 0.6, "support": 5}, "补充知识点4224": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1-score": 0.6666666666666666, "support": 3}, "补充知识点4228": {"precision": 0.5, "recall": 0.5, "f1-score": 0.5, "support": 2}, "补充知识点4230": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点4234": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "补充知识点4238": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4244": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4351": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "补充知识点4383": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4478": {"precision": 1.0, "recall": 0.3333333333333333, "f1-score": 0.5, "support": 3}, "补充知识点4482": {"precision": 1.0, "recall": 0.2, "f1-score": 0.33333333333333337, "support": 5}, "补充知识点4484": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4502": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4525": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4820": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点484": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点4841": {"precision": 1.0, "recall": 0.6666666666666666, "f1-score": 0.8, "support": 6}, "补充知识点4942": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "补充知识点5014": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点5254": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3}, "补充知识点5284": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "补充知识点5448": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "解决两、三位数乘一位数(连续进位)的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "解决有关2的倍数的实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "计数器表示数(1000以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "认识几分之几": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 6}, "认识毫米": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "认识质量单位吨\"\"": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "质量单位比较大小(吨、千克、克)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用一位小数加法解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用两位数乘整十（百）数的口算解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "运用分数通分比较大小来解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "进一、去尾解决问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "连乘的计算与应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "连减与除法(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2}, "连加解决实际问题": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "连续数(100以内)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "速度、时间、路程之间的关系": {"precision": 1.0, "recall": 0.5, "f1-score": 0.6666666666666666, "support": 2}, "间隔问题的相关应用": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除加除减应用题(表内混合运算)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 4}, "除数不接近整十数的试商": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除数是一位数的估算": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法应用题(2-6)（旧版）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法应用题(7、8、9)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}, "除法意义的理解与运用（移后删）": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1}, "除法计算与运用(2-6)": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0}}, "evaluation_summary": {"total_samples": 243, "unique_true_labels": 76, "unique_pred_labels": 169, "avg_similarity_score": 0.7691565612216055, "num_classes": 200}}