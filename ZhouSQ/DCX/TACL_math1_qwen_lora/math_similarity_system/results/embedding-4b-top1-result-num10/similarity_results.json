[{"query": "专家建议每人每天糖摄入量最好控制在 $\\frac{1}{40}$ kg以内否则易导致蛀牙、肥胖和一些慢性疾病根据专家建议成人每个月（按30天算）的糖摄入量不超过多少千克已知每人每天糖摄入量最好控制在 $\\frac{1}{40}$ kg以内一个月按30天算求每个月糖摄入量不超过多少千克就是求30个 $\\frac{1}{40}$ kg是多少用乘法计算列式为 $\\frac{1}{40}\\times30=\\frac{3}{4}$ （kg）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.6655939817428589, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "整数除法的性质", "运用除法的运算性质解决实际问题", "补充知识点1901"], "topk_results": [{"similarity_score": 0.6655939817428589, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "整数除法的性质", "运用除法的运算性质解决实际问题", "补充知识点1901"], "sample_id": 30314}], "prediction_correct": false}, {"query": "大约从一万年前开始青藏高原平均每年上升约 $\\frac{7}{100}$ 米按照这个速度50年它能长高多少米100年呢本题可根据每年上升的高度乘以年数来计算长高的高度计算50年长高的高度已知青藏高原平均每年上升约 $\\frac{7}{100}$ 米那么50年长高的高度为每年上升高度乘以50年即 $\\frac{7}{100}\\times50=\\frac{7}{2}$ （米）计算100年长高的高度同理100年长高的高度为每年上升高度乘以100年即 $\\frac{7}{100}\\times100=7$ （米）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.5845036506652832, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "整数的改写与近似数", "整数的改写", "整万数的改写"], "topk_results": [{"similarity_score": 0.5845036506652832, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "整数的改写与近似数", "整数的改写", "整万数的改写"], "sample_id": 14047}], "prediction_correct": false}, {"query": "毛毛的学习桌有点不平稳他就用一张厚 $\\frac{3}{16}$ mm的牛皮纸对折三次后垫在桌子的一条腿下面恰好放稳了这条桌子腿短了多少毫米本题可先分析对折三次后纸的层数再结合每层纸的厚度求出桌子腿短的长度分析对折三次后纸的层数将纸对折1次纸会变成2层对折2次这2层纸又分别被对折此时纸的层数变为2×2 = 4层对折3次这4层纸又分别被对折纸的层数变为4×2 = 8层计算桌子腿短的长度已知每张牛皮纸的厚度是 $\\frac{3}{16}$ mm对折三次后纸有8层那么桌子腿短的长度就是8个 $\\frac{3}{16}$ mm用乘法计算可得 $\\frac{3}{16}×8=\\frac{3}{2}$ （mm）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.6086477041244507, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "利用整体的几分之几解决问题"], "topk_results": [{"similarity_score": 0.6086477041244507, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "利用整体的几分之几解决问题"], "sample_id": 33156}], "prediction_correct": false}, {"query": "捆一摞书用一卷彩带的 $\\frac{2}{9}$ 捆3摞书一共需要用这卷彩带的几分之几本题可根据乘法的意义来求解捆 $3$ 摞书所需彩带占这卷彩带的比例已知捆一摞书用这卷彩带的 $\\frac{2}{9}$ 要求捆 $3$ 摞书一共需要用这卷彩带的几分之几就是求 $3$ 个 $\\frac{2}{9}$ 是多少根据乘法的意义可列式为 $\\frac{2}{9}\\times3$ 计算 $\\frac{2}{9}\\times3$ 时整数 $3$ 与分子 $2$ 相乘分母不变可得 $\\frac{2\\times3}{9}=\\frac{6}{9}$ 再对 $\\frac{6}{9}$ 进行约分分子分母同时除以 $3$ 得到 $\\frac{6\\div3}{9\\div3}=\\frac{2}{3}$ 所以捆 $3$ 摞书一共需要用这卷彩带的 $\\frac{2}{3}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.6234302520751953, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数与除法的关系", "分数与除法关系的应用-"], "topk_results": [{"similarity_score": 0.6234302520751953, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数与除法的关系", "分数与除法关系的应用-"], "sample_id": 21876}], "prediction_correct": false}, {"query": "  $\\frac{1}{6}\\times10$ $\\frac{1}{6}$    $\\frac{3}{4}\\times1$ $\\frac{3}{4}$   $\\frac{7}{13}\\times0$ $\\frac{7}{13}$ $\\frac{9}{44}\\times11$ $3$ 本题可根据分数乘法的计算方法分别算出式子左边乘法算式的结果再与右边的数比较大小 - 比较 $\\frac{1}{6}\\times10$ 与 $\\frac{1}{6}$ 的大小 先计算 $\\frac{1}{6}\\times10=\\frac{10}{6}$ 因为 $\\frac{10}{6}>\\frac{1}{6}$ 所以 $\\frac{1}{6}\\times10>\\frac{1}{6}$  - 比较 $\\frac{3}{4}\\times1$ 与 $\\frac{3}{4}$ 的大小 根据任何数乘 $1$ 都得原数可得 $\\frac{3}{4}\\times1=\\frac{3}{4}$ 所以 $\\frac{3}{4}\\times1=\\frac{3}{4}$  - 比较 $\\frac{7}{13}\\times0$ 与 $\\frac{7}{13}$ 的大小 因为任何数乘 $0$ 都得 $0$ 所以 $\\frac{7}{13}\\times0=0$ 又因为 $0<\\frac{7}{13}$ 所以 $\\frac{7}{13}\\times0<\\frac{7}{13}$  - 比较 $\\frac{9}{44}\\times11$ 与 $3$ 的大小 先计算 $\\frac{9}{44}\\times11=\\frac{9}{4}=2.25$ 因为 $2.25<3$ 所以 $\\frac{9}{44}\\times11<3$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.8275914192199707, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数除法", "分数与分数的除法", "一个数除以分数的计算"], "topk_results": [{"similarity_score": 0.8275914192199707, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数除法", "分数与分数的除法", "一个数除以分数的计算"], "sample_id": 15035}], "prediction_correct": false}, {"query": "$\\frac{2}{13}$ ×5= $\\frac{3}{19}$ ×6=       $\\frac{5}{6}$ ×12=　　　　　　5× $\\frac{4}{11}$ =  $\\frac{5}{12}$ ×8=        $\\frac{2}{7}$ ×3=　　　　　　 6× $\\frac{3}{5}$ =       $\\frac{4}{21}$ ×9=1. 对于 $\\frac{2}{13}$ ×5分数乘整数用分子乘整数的积作分子分母不变即 $\\frac{2×5}{13}=\\frac{10}{13}$ 2. 对于 $\\frac{3}{19}$ ×6同理可得 $\\frac{3×6}{19}=\\frac{18}{19}$ 3. 对于 $\\frac{5}{6}$ ×12 $\\frac{5×12}{6}=5×2=10$ 4. 对于5× $\\frac{4}{11}$  $\\frac{5×4}{11}=\\frac{20}{11}$ 5. 对于 $\\frac{5}{12}$ ×8 $\\frac{5×8}{12}=\\frac{10}{3}$ 6. 对于 $\\frac{2}{7}$ ×3 $\\frac{2×3}{7}=\\frac{6}{7}$ 7. 对于6× $\\frac{3}{5}$  $\\frac{6×3}{5}=\\frac{18}{5}$ 8. 对于 $\\frac{4}{21}$ ×9 $\\frac{4×9}{21}=\\frac{12}{7}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.8552001118659973, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "topk_results": [{"similarity_score": 0.8552001118659973, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "sample_id": 15065}], "prediction_correct": true}, {"query": "一瓶饮料有 $\\frac{9}{20}$ 升5瓶饮料有多少升求5瓶饮料有多少升就是求5个 $\\frac{9}{20}$ 升是多少用乘法计算列式为 $\\frac{9}{20}×5$ 根据分数乘整数的计算方法用分数的分子和整数相乘的积作分子分母不变能约分的先约分再计算 $\\frac{9}{20}×5=\\frac{9}{4}$ 升", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.8024856448173523, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "topk_results": [{"similarity_score": 0.8024856448173523, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "sample_id": 15066}], "prediction_correct": true}, {"query": "1只树袋熊一天大约吃 $\\frac{6}{7}$ kg的桉树叶10只树袋熊一天大约能吃多少千克桉树叶本题可根据乘法的意义来求解求几个相同加数的和的简便运算用乘法已知1只树袋熊一天大约吃 $\\frac{6}{7}$ kg的桉树叶求10只树袋熊一天吃的桉树叶重量就是求10个 $\\frac{6}{7}$ 是多少用乘法计算列式为 $\\frac{6}{7}\\times10=\\frac{60}{7}$ （kg）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.6114222407341003, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "用连除解决实际问题", "用连除解决实际问题"], "topk_results": [{"similarity_score": 0.6114222407341003, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "用连除解决实际问题", "用连除解决实际问题"], "sample_id": 12374}], "prediction_correct": false}, {"query": "陈红看一本120页的故事书每天看这本书的 $\\frac{1}{10}$ , 8天看了这本书的几分之几已知每天看这本书的 $\\frac{1}{10}$ 要求8天看了这本书的几分之几就是求8个 $\\frac{1}{10}$ 是多少用乘法计算即 $\\frac{1}{10}\\times8=\\frac{8}{10}=\\frac{4}{5}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.7330539226531982, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "用两步连乘解决实际问题", "用连乘解决实际问题"], "topk_results": [{"similarity_score": 0.7330539226531982, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "用两步连乘解决实际问题", "用连乘解决实际问题"], "sample_id": 6936}], "prediction_correct": false}, {"query": "王强从三楼爬到四楼用了 $\\frac{1}{5}$ 分钟照这样计算他从一楼爬到五楼用多少分钟从三楼到四楼爬了 $1$ 层楼梯用时 $\\frac{1}{5}$ 分钟从一楼到五楼要爬 $5-1=4$ 层楼梯每层用时 $\\frac{1}{5}$ 分钟那么 $4$ 层用时 $\\frac{1}{5}\\times4=\\frac{4}{5}$ 分钟", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.7379145622253418, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "有隐藏条件的除法应用题(2-6)"], "topk_results": [{"similarity_score": 0.7379145622253418, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "有隐藏条件的除法应用题(2-6)"], "sample_id": 2057}], "prediction_correct": false}, {"query": "  $\\frac{2}{9}$ ×3=        $\\frac{7}{100}$ ×20=        $\\frac{2}{11}$ ×12=　　　　　　　5× $\\frac{4}{11}$ =  $\\frac{5}{21}$ ×7=        $\\frac{3}{4}$ ×16=         $\\frac{3}{5}$ ×80=　　　　　　　60× $\\frac{7}{15}$ =1. 计算 $\\frac{2}{9}$ ×3分数乘整数用分数的分子和整数相乘的积作分子分母不变 $\\frac{2}{9}$ ×3 = $\\frac{2×3}{9}$ = $\\frac{6}{9}$ 约分后得 $\\frac{2}{3}$ 2. 计算 $\\frac{7}{100}$ ×20同理 $\\frac{7}{100}$ ×20 = $\\frac{7×20}{100}$ = $\\frac{140}{100}$ 约分后得 $\\frac{7}{5}$ 3. 计算 $\\frac{2}{11}$ ×12 $\\frac{2}{11}$ ×12 = $\\frac{2×12}{11}$ = $\\frac{24}{11}$ 4. 计算5× $\\frac{4}{11}$ 5× $\\frac{4}{11}$ = $\\frac{5×4}{11}$ = $\\frac{20}{11}$ 5. 计算 $\\frac{5}{21}$ ×7 $\\frac{5}{21}$ ×7 = $\\frac{5×7}{21}$ = $\\frac{35}{21}$ 约分后得 $\\frac{5}{3}$ 6. 计算 $\\frac{3}{4}$ ×16 $\\frac{3}{4}$ ×16 = $\\frac{3×16}{4}$ = 127. 计算 $\\frac{3}{5}$ ×80 $\\frac{3}{5}$ ×80 = $\\frac{3×80}{5}$ = 488. 计算60× $\\frac{7}{15}$ 60× $\\frac{7}{15}$ = $\\frac{60×7}{15}$ = 28", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.81638103723526, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "topk_results": [{"similarity_score": 0.81638103723526, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "sample_id": 15064}], "prediction_correct": true}, {"query": "将这块木板锯成5段需要多少分钟将木板锯成2段需要 $\\frac{3}{5}$ 分钟即锯一次需要 $\\frac{3}{5}$ 分钟锯成5段需要锯4次因此总时间为 $4\\times\\frac{3}{5}=\\frac{12}{5}=2.4$ 分钟", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.8344473838806152, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "topk_results": [{"similarity_score": 0.8344473838806152, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "sample_id": 23441}], "prediction_correct": false}, {"query": "小华和小明骑自行车上学小华每分钟行 $\\frac{4}{15}$ 千米小明每小时行14千米他俩谁的速度快本题可先将两人的速度单位统一再比较两人速度的大小统一单位已知小华每分钟行 $\\frac{4}{15}$ 千米因为 $1$ 小时 $=60$ 分钟所以小华每小时行驶的距离为 $\\frac{4}{15}×60=16$ （千米）比较两人速度大小小华每小时行 $16$ 千米小明每小时行 $14$ 千米因为 $16＞14$ 所以小华的速度比小明快", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.7798762321472168, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数大小的比较", "异分母异分子分数比大小", "运用分数通分比较大小来解决问题"], "topk_results": [{"similarity_score": 0.7798762321472168, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数大小的比较", "异分母异分子分数比大小", "运用分数通分比较大小来解决问题"], "sample_id": 26677}], "prediction_correct": false}, {"query": "一种钢材每米重 $\\frac{8}{25}$ 千克现在有这种钢材125米共重多少千克要求125米钢材的重量因为每米重 $\\frac{8}{25}$ 千克也就是求125个 $\\frac{8}{25}$ 是多少用乘法计算列式为 $\\frac{8}{25}\\times125=8\\times5=40$ （千克）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数与整数的乘法", "分数乘整数"], "similarity_score": 0.666191816329956, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "立体图形", "长方体和正方体", "长方体的体积", "长方体体积公式实际应用", "补充知识点3709"], "topk_results": [{"similarity_score": 0.666191816329956, "predicted_label": ["小学数学新知识树", "图形与几何", "立体图形", "长方体和正方体", "长方体的体积", "长方体体积公式实际应用", "补充知识点3709"], "sample_id": 11095}], "prediction_correct": false}, {"query": "右下图是一张长方形纸折起来以后形成的图形已知 $∠1=25^{\\circ}$ 求 $∠2$ 的度数长方形的每个角都是 $90^{\\circ}$ 根据折叠的性质折叠后的角与原来的角相等已知 $∠1=25^{\\circ}$ 则折叠部分的角（即与 $∠1$ 重合的角）也是 $25^{\\circ}$ 因此 $∠1$ 和它重合的角加起来是 $25^{\\circ}+25^{\\circ}=50^{\\circ}$ 而 $∠1$ 、它重合的角和 $∠2$ 这三个角共同组成了长方形的一个角即 $90^{\\circ}$ 所以 $∠2=90^{\\circ}-50^{\\circ}=40^{\\circ}$ ", "true_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "线段与角的综合", "角度直接运算", "补充知识点3207"], "similarity_score": 0.9159298539161682, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "图形的拼组", "图形的折叠问题", "角度的简单计算--折叠", "补充知识点3982", "补充知识点3983"], "topk_results": [{"similarity_score": 0.9159298539161682, "predicted_label": ["小学数学新知识树", "图形与几何", "图形的拼组", "图形的折叠问题", "角度的简单计算--折叠", "补充知识点3982", "补充知识点3983"], "sample_id": 43087}], "prediction_correct": false}, {"query": "已知 $∠1=90^{\\circ},∠2=∠3$ 求 $∠2、∠3$ 的度数根据题目条件我们知道 $∠1=90^\\circ$ 且 $∠2=∠3$ 因为 $∠1$ 、 $∠2$ 和 $∠3$ 在同一条直线上它们的和为 $180^\\circ$ 因此我们可以得到 $∠1+∠2+∠3=180^\\circ$ 将 $∠1=90^\\circ$ 代入得到 $90^\\circ+∠2+∠3=180^\\circ$ 简化得到 $∠2+∠3=90^\\circ$ 又因为 $∠2=∠3$ 我们可以得到 $∠2=∠3=90^\\circ÷2=45^\\circ$ ", "true_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "线段与角的综合", "角度直接运算", "补充知识点3207"], "similarity_score": 0.8255517482757568, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "三角形", "三角形的内角和", "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)", "补充知识点3410"], "topk_results": [{"similarity_score": 0.8255517482757568, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "三角形", "三角形的内角和", "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)", "补充知识点3410"], "sample_id": 3547}], "prediction_correct": false}, {"query": "写出三角尺上各个角的度数第一张图片中的三角尺是一个含有30°、60°和90°角的直角三角尺因此∠1=30°∠2=60°∠3=90°第二张图片中的三角尺是一个等腰直角三角尺两个底角相等且为45°顶角为90°因此∠1=45°∠2=45°∠3=90°", "true_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "用三角尺画角", "三角尺拼角", "补充知识点3201"], "similarity_score": 0.7999905943870544, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "三角形", "多边形的内角和", "四边形内角和的应用", "补充知识点3418"], "topk_results": [{"similarity_score": 0.7999905943870544, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "三角形", "多边形的内角和", "四边形内角和的应用", "补充知识点3418"], "sample_id": 13605}], "prediction_correct": false}, {"query": "有（ ） 条直线有（ ） 条射线有（ ） 条线段根据题目中的图形分析1. 直线直线没有端点可以向两端无限延伸图中只有一条直线即通过点A、B、C的直线2. 射线射线有一个端点可以向一端无限延伸以每个点为端点可以得到射线具体为以A为端点的射线有2条（向左和向右）以B为端点的射线有2条（向左和向右）以C为端点的射线有2条（向左和向右）因此共有2+2+2=6条射线3. 线段线段有两个端点长度有限图中的线段有AB、BC、AC共3条线段因此答案分别为1条直线6条射线3条线段", "true_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "similarity_score": 0.8970786929130554, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "topk_results": [{"similarity_score": 0.8970786929130554, "predicted_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "sample_id": 21285}], "prediction_correct": true}, {"query": "① 8 个百② 80 个十③ 8 个十④ 8 个一根据题目中的箭头指向608中的'8'是在个位但实际上在竖式计算中4位于个位2位于十位因此4×2实际上表示的是4个一乘以2个十结果为8个十答案选③", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "三位数与两位数的进位乘法", "三位数乘两位数笔算"], "similarity_score": 0.7712042927742004, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "三位数除以一位数，首位能除尽", "三位数除以一位数(每一位都能整除)的笔算"], "topk_results": [{"similarity_score": 0.7712042927742004, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "三位数除以一位数，首位能除尽", "三位数除以一位数(每一位都能整除)的笔算"], "sample_id": 27004}], "prediction_correct": false}, {"query": "用竖式计算再用计算器验算 $\n15\\times201=\n$    $104\\times30=$    $305\\times23=$ 1. 计算15×201 - 先计算15×1 = 15再计算15×200 = 3000最后3000 + 15 = 30152. 计算104×30 - 先计算104×3 = 312再在积的末尾添上1个0得到31203. 计算305×23 - 先计算305×3 = 915再计算305×20 = 6100最后915 + 6100 = 7015用计算器验算时依次输入相应的数字和运算符号得出的结果应与上面计算的结果一致", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "三位数与两位数的进位乘法", "三位数乘两位数笔算"], "similarity_score": 0.746171236038208, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "三位数除以两位数(商是两位数)的笔算除法"], "topk_results": [{"similarity_score": 0.746171236038208, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "三位数除以两位数(商是两位数)的笔算除法"], "sample_id": 34522}], "prediction_correct": false}, {"query": "一共行了多长的路叫作 ___ 每小时（或每分钟等）行的路程叫作___ 行了几小时（或几分钟等）叫作 ___在数学里一共走的路的长度就叫路程每小时或者每分钟等这样的单位时间里走的路程这就是速度而走了几小时或者几分钟等指的就是时间", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "行程问题", "普通行程问题", "速度、时间、路程之间的关系"], "similarity_score": 0.9718872308731079, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "行程问题", "普通行程问题", "速度、时间、路程之间的关系"], "topk_results": [{"similarity_score": 0.9718872308731079, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "行程问题", "普通行程问题", "速度、时间、路程之间的关系"], "sample_id": 32950}], "prediction_correct": true}, {"query": "一本童话书有 296 页 31 本童话书大约有 ___页本题可采用估算的方法来计算 $31$ 本童话书大约的页数在估算时我们可以把 $296$ 看作与它接近的整百数 $300$ 把 $31$ 看作与它接近的整十数 $30$ 然后根据乘法的意义求 $31$ 本童话书大约的页数就是求 $31$ 个 $296$ 大约是多少用乘法计算列式为 $296\\times31$ 估算时可转化为 $300\\times30=9000$ （页）所以 $31$ 本童话书大约有 $9000$ 页", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘两位数的估算", "三位数乘两位数的估算"], "similarity_score": 0.7372092604637146, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数的相对大小关系", "数的相对大小关系(100以内)"], "topk_results": [{"similarity_score": 0.7372092604637146, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数的相对大小关系", "数的相对大小关系(100以内)"], "sample_id": 12948}], "prediction_correct": false}, {"query": "点 $A$ 表示的数可能是算式（ ） 的积① $401\\times51$ ② $399\\times45$ ③ $399\\times51$ ④ $401\\times50$ 由图可知点A在靠近20000的位置并且点A表示的数字小于20000① 401 × 51 ≈ 400 × 50 = 20000和题意不符② 399 × 45 ≈ 400 × 45 = 18000符合题意③ 399 × 51 ≈ 400 × 50 = 20000和题意不符④ 401 × 50 ≈ 400 × 50 = 20000和题意不符所以点A表示的数可能是算式②的积", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘两位数的估算", "三位数乘两位数的估算"], "similarity_score": 0.8903785943984985, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘两位数的估算", "三位数乘两位数的估算"], "topk_results": [{"similarity_score": 0.8903785943984985, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘两位数的估算", "三位数乘两位数的估算"], "sample_id": 43378}], "prediction_correct": true}, {"query": "用竖式计算 $\n308\\times25=\n$       $\n27\\times235=\n$        $\n480\\times50=\n$ 1. 计算308×25 - 先用25个位上的5去乘3085×308 = 1540 - 再用25十位上的2去乘3082×308 = 616这里的616表示616个十即6160 - 最后把两次乘得的积相加1540 + 6160 = 77002. 计算27×235 - 先用27个位上的7去乘2357×235 = 1645 - 再用27十位上的2去乘2352×235 = 470这里的470表示470个十即4700 - 最后把两次乘得的积相加1645+4700 = 63453. 计算480×50 - 先计算48×5 = 240 - 因为两个因数末尾一共有2个0就在积240的末尾添上2个0结果是24000", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "三位数与两位数的进位乘法", "三位数乘两位数笔算"], "similarity_score": 0.8755980730056763, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "三位数与两位数的进位乘法", "三位数乘两位数笔算"], "topk_results": [{"similarity_score": 0.8755980730056763, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "三位数与两位数的进位乘法", "三位数乘两位数笔算"], "sample_id": 34516}], "prediction_correct": true}, {"query": "飞机每小时飞行 500 千米这是飞机飞行的 ___ 可以写成 ___ 读作 ___每小时飞行500千米这是飞机每小时飞行的路程也就是飞机飞行的速度速度的写法是把路程写在前面然后用斜线隔开后面写时间所以写成500千米/时读作就是按照从左到右的顺序读出来读作500千米每时", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "行程问题", "普通行程问题", "速度、时间、路程之间的关系"], "similarity_score": 0.7222680449485779, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "运用两位数乘整十（百）数的口算解决实际问题"], "topk_results": [{"similarity_score": 0.7222680449485779, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "运用两位数乘整十（百）数的口算解决实际问题"], "sample_id": 4606}], "prediction_correct": false}, {"query": "用竖式计算 $\n29\\times130=\n$    $360\\times53=$    $12\\times305=$ 1. 计算29×130 - 先计算29×13按照两位数乘两位数的竖式计算方法9×13 = 11720×13 = 260117 + 260 = 377 - 因为130末尾有1个0所以在377后面添上1个0结果是37702. 计算360×53 - 先计算36×5336×3 = 10836×50 = 1800108+1800 = 1908 - 因为360末尾有1个0所以在1908后面添上1个0结果是190803. 计算12×305 - 用12分别去乘305的每一位12×5 = 6012×300 = 360060 + 3600 = 3660", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "三位数与两位数的进位乘法", "三位数乘两位数笔算"], "similarity_score": 0.7631190419197083, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘两位数的进位乘法", "两位数乘两位数的笔算(有进位)"], "topk_results": [{"similarity_score": 0.7631190419197083, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘两位数的进位乘法", "两位数乘两位数的笔算(有进位)"], "sample_id": 16587}], "prediction_correct": false}, {"query": "算一算           1. 第一个算式 $490\\times20=9800$ 2. 第二个算式 $108\\times30=3240$ 3. 第三个算式 $270\\times34=9180$ 4. 第四个算式 $33\\times230=7590$", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "三位数与两位数的进位乘法", "三位数乘两位数笔算"], "similarity_score": 0.7749333381652832, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "三位数与一位数的乘法口算", "几百几十数乘一位数的口算(有进位)"], "topk_results": [{"similarity_score": 0.7749333381652832, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "三位数与一位数的乘法口算", "几百几十数乘一位数的口算(有进位)"], "sample_id": 34601}], "prediction_correct": false}, {"query": "龙眼是漳州市的特色水果李叔叔开车从宁德去漳州采购龙眼（1） 2 小时后距离两地的中点还有 40 千米请在下面的线段图中用＂ $\\triangle$ ＂标出此时汽车所在的位置（2）李叔叔要采购 3 吨龙眼已经买了 150 箱龙眼每箱 15 千克还要再采购多少千克（3）返回时汽车每小时行 75 千米 3 小时后超过两地中点 40 千米①在上面的线段图中用标出此时汽车所在的位置②汽车从漳州到宁德一共要行驶多少千米（1）首先根据题目描述2小时后距离两地的中点还有40千米这意味着汽车此时位于中点前40千米的位置因此在线段图中从宁德到漳州的中点位置向前40千米处标记“ $\\triangle$ ”（2）李叔叔要采购3吨龙眼即3000千克已经买了150箱每箱15千克因此已经采购的龙眼重量为 $150\\times15=2250$ 千克还需要采购的龙眼重量为 $3000-2250=750$ 千克（3）①返回时汽车每小时行75千米3小时后超过两地中点40千米即在中点后40千米处因此在线段图中从宁德到漳州的中点位置向后40千米处标记“ $\\P$ ”②已知返回时汽车速度为每小时75千米行驶3小时根据“路程=速度×时间”可得行驶的路程为75×3=225千米因为此时超过中点40千米所以到中点的距离是225-40=185千米那么漳州到宁德的总距离为185×2=370千米", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "行程问题", "普通行程问题", "根据公式求路程"], "similarity_score": 0.6504148244857788, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "比例尺的意义", "线段比例尺与数值比例尺的互化", "补充知识点2505"], "topk_results": [{"similarity_score": 0.6504148244857788, "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "比例尺的意义", "线段比例尺与数值比例尺的互化", "补充知识点2505"], "sample_id": 29080}], "prediction_correct": false}, {"query": " ___统计图能清楚、直观地看出各种数量的多少便于比较条形统计图是用直条的长短来表示数量的多少这样我们一眼就能清楚、直观地看出各种数量的多少而且很便于对不同数量进行比较", "true_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示一个单位的单式条形统计图", "以一当一的条形统计图", "补充知识点4478"], "similarity_score": 0.8932307362556458, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示一个单位的单式条形统计图", "以一当一的条形统计图", "补充知识点4478"], "topk_results": [{"similarity_score": 0.8932307362556458, "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示一个单位的单式条形统计图", "以一当一的条形统计图", "补充知识点4478"], "sample_id": 15505}], "prediction_correct": true}, {"query": "下图是某一物体在不同时刻影子长度的统计图下午130这个物体影子长度大约是（ ） 厘米① 80② 135③ 150④ 160根据统计图13时（下午1时）的影子长度为80厘米14时（下午2时）的影子长度为160厘米下午130位于13时和14时之间影子长度应介于80厘米和160厘米之间因此下午130时这个物体影子长度大约是135厘米选②", "true_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当多的条形统计图", "补充知识点4482"], "similarity_score": 0.690509557723999, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "100以内数的加减混合运算", "求一个数比另一个数多（少）几"], "topk_results": [{"similarity_score": 0.690509557723999, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "100以内数的加减混合运算", "求一个数比另一个数多（少）几"], "sample_id": 10321}], "prediction_correct": false}, {"query": "下面是四（2）班六个小组的同学创作的手抄报数量统计表\n|   |   |   |   |   |   |   |\n| --- | --- | --- | --- | --- | --- | --- |\n| 小组 | 一 | 二 | 三 | 四 | 五 | 六 |\n| 数量／份 | 3 | 5 | 7 | 6 | 4 | 5 |\n（1）用条形图把统计结果表示出来（2）第 ___小组的同学创作的手抄报数量最多（3）第 ___小组的同学创作的手抄报数量最少（4）平均每个小组创作多少份手抄报（1）根据统计表中的数据可以绘制条形图如下（2）观察条形图第三小组的手抄报数量最多为7份（3）观察条形图第一小组的手抄报数量最少为3份（4）平均每个小组创作的手抄报数量计算如下总数量 = 3 + 5 + 7 + 6 + 4 + 5 = 30份平均数量 = 30 ÷ 6 = 5（份）", "true_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示一个单位的单式条形统计图", "以一当一的条形统计图", "补充知识点4478"], "similarity_score": 0.769229531288147, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "平均数、中位数、众数", "平均数的意义及求法", "在统计表中计算平均数", "补充知识点4525"], "topk_results": [{"similarity_score": 0.769229531288147, "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "平均数、中位数、众数", "平均数的意义及求法", "在统计表中计算平均数", "补充知识点4525"], "sample_id": 37360}], "prediction_correct": false}, {"query": "由右边的统计图可知 $a$ 的值大约是（ ） ① 10② 14③ 16④ 24由图知D＞B且D＜B+C则a＞10a＜10+6即10＜a＜16a的值大约是14故选②", "true_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当多的条形统计图", "补充知识点4482"], "similarity_score": 0.8272709846496582, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当二的条形统计图", "补充知识点4484"], "topk_results": [{"similarity_score": 0.8272709846496582, "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当二的条形统计图", "补充知识点4484"], "sample_id": 43897}], "prediction_correct": false}, {"query": "根据小猫钓鱼的统计表回答问题（1）把上面的数据用条形统计图表示出来（2） 4 只猫一共钓了 ___条鱼（3）比多钓 ___条鱼 的鱼比的鱼少 ___条（1）根据题目提供的数据可以得到每只猫钓鱼的数量分别为7条、5条、3条、6条（2）4只猫一共钓鱼的数量为 $7+5+3+6=21$ 条（3）第一只猫比第二只猫多钓的鱼数量为 $7-5=2$ 条第三只猫的鱼比第四只猫的鱼少的数量为 $6-3=3$ 条", "true_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示一个单位的单式条形统计图", "以一当一的条形统计图", "补充知识点4478"], "similarity_score": 0.7790604829788208, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数的相对大小关系", "数的相对大小关系(100以内)"], "topk_results": [{"similarity_score": 0.7790604829788208, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数的相对大小关系", "数的相对大小关系(100以内)"], "sample_id": 12914}], "prediction_correct": false}, {"query": "下面是四（2）班同学最喜欢的早餐统计表\n| 最喜欢的早餐 | 牛奶 | 豆浆 | 包子 | 人数 | 6 | 12 | 20 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 最喜欢的早餐 | 牛奶 | 豆浆 | 包子 |\n| 人数 | 6 | 12 | 20 |\n如果要制成条形统计图那么每格表示（ ） 人比较合适① 1② 2③ 5④ 10分析数据特点已知喜欢牛奶的有6人喜欢豆浆的有12人喜欢包子的有20人这些数据都是2的倍数（6÷2=312÷2=620÷2=10）选项①若每格表示1人绘制条形统计图时直条会比较长 虽然能表示数据但不够简洁选项②因为6、12、20都是2的倍数每格表示2人6÷2=3格12÷2=6格20÷2=10格 这样绘制的条形统计图简洁且能清晰表示数据选项③若每格表示5人6÷5=1……1不能完整表示6人的数据不合适选项④若每格表示10人6÷10=0.1无法准确表示数据不合适综上每格表示2人比较合适答案选②", "true_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当多的条形统计图", "补充知识点4482"], "similarity_score": 0.8797560930252075, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当多的条形统计图", "补充知识点4482"], "topk_results": [{"similarity_score": 0.8797560930252075, "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当多的条形统计图", "补充知识点4482"], "sample_id": 15508}], "prediction_correct": true}, {"query": "根据下列条形统计图填一填 纵轴1格表示 ___  纵轴1格表示 ___  纵轴1格表示 ___  纵轴1格表示 ___ 直条表示 ___ 直条表示 ___ 直条表示 ___ 直条表示 ___第一张图中纵轴从0到10分为2格因此每格表示 $10÷2=5$ 直条高度为8格表示 $5\\times8=40$ 第二张图中纵轴从0到20分为2格因此每格表示 $20÷2=10$ 直条高度为3格表示 $3\\times10=30$ 第三张图中纵轴从0到4分为2格因此每格表示 $4÷2=2$ 直条高度为5格表示 $2\\times5=10$ 第四张图中纵轴从0到50分为2格因此每格表示 $50÷2=25$ 直条高度为6格表示 $25\\times6=150$ ", "true_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当多的条形统计图", "补充知识点4482"], "similarity_score": 0.7546648979187012, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "单式折线统计图", "认识折线统计图及其特点", "补充知识点4502"], "topk_results": [{"similarity_score": 0.7546648979187012, "predicted_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "单式折线统计图", "认识折线统计图及其特点", "补充知识点4502"], "sample_id": 35957}], "prediction_correct": false}, {"query": "张师傅 3 小时可以做 27 个零件照这个速度 5 小时他能做多少个零件本题可先根据已知条件算出张师傅每小时做零件的个数再计算5小时做零件的个数已知张师傅3小时可以做27个零件要求每小时做零件的个数就是把27平均分成3份求每份是多少用除法计算可得 $27\\div3=9$ （个）即张师傅每小时能做9个零件那么5小时做零件的个数就是5个9相加用乘法计算可得 $9\\times5=45$ （个）列综合算式是27÷3×5=9×5=45（个）综上5小时他能做45个零件", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.7639721632003784, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数与两位数的乘除混合运算", "用乘除混合解决实际问题"], "topk_results": [{"similarity_score": 0.7639721632003784, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数与两位数的乘除混合运算", "用乘除混合解决实际问题"], "sample_id": 4774}], "prediction_correct": false}, {"query": "1、前置信息看图列式计算2、请回答（2）根据图片可以得到以下信息1. 前四个部分每部分长度为7即 $7×4=28$ 2. 第五个部分长度为16因此总长度为 $28+16=44$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "看图列综合算式并计算"], "similarity_score": 0.7793179750442505, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数的加、减法", "同分母分数加、减法", "同分母分数减法的含义及计算方法"], "topk_results": [{"similarity_score": 0.7793179750442505, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数的加、减法", "同分母分数加、减法", "同分母分数减法的含义及计算方法"], "sample_id": 19113}], "prediction_correct": false}, {"query": "毛毛在计算＂ $18+\\square\\div9$ ＂时弄错了运算顺序先算加法后算除法了结果得数是 8 正确的得数应该是多少本题可先根据错误的运算顺序求出□里的数再按照正确的运算顺序计算出结果已知毛毛弄错了运算顺序先算加法后算除法即 $( 18+\\square )\\div9=8$ 根据“被除数 $=$ 除数 $\\times$ 商”可得 $18+\\square=8\\times9=72$ 再根据“加数 $=$ 和 $-$ 另一个加数”可得 $\\square=72-18=54$ 将 $\\square=54$ 代入原式 $18+\\square\\div9$ 得到 $18+54\\div9$ 根据数学运算顺序先算除法再算加法即 $18+54\\div9=18+6=24$ ", "true_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "similarity_score": 0.9560234546661377, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "topk_results": [{"similarity_score": 0.9560234546661377, "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "sample_id": 38115}], "prediction_correct": true}, {"query": "1、前置信息看图列式计算2、请回答（1）根据图片女生有28人女生比男生多4人因此男生有 $28-4=24$ 人男生和女生总共有 $28+24=52$ 人", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "看图列综合算式并计算"], "similarity_score": 0.7584977149963379, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与两位数的退位减法", "两位数减两位数(退位减)的应用"], "topk_results": [{"similarity_score": 0.7584977149963379, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与两位数的退位减法", "两位数减两位数(退位减)的应用"], "sample_id": 39726}], "prediction_correct": false}, {"query": "根据下表列出相应的算式并计算\n| 乘数 | 乘数 | 综合算式 | 积 |   | 9 |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 乘数 | 乘数 | 综合算式 | 积 |\n|   | 9 |   |   |\n| 6 |   |   |   |\n根据表格第一个乘数为9第二个乘数为6因此综合算式为9×6计算得到积为54", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "根据分步运算列综合算式(含括号)(表内混合运算)"], "similarity_score": 0.7607583403587341, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "根据分步运算列综合算式(含括号)(表内混合运算)"], "topk_results": [{"similarity_score": 0.7607583403587341, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "根据分步运算列综合算式(含括号)(表内混合运算)"], "sample_id": 42185}], "prediction_correct": true}, {"query": "爸爸今年几岁根据题目当亮亮9岁时爸爸38岁因此爸爸比亮亮大 $38-9=29$ 岁现在亮亮3岁所以爸爸的年龄为 $3+29=32$ 岁", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.6359407901763916, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "topk_results": [{"similarity_score": 0.6359407901763916, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "sample_id": 23437}], "prediction_correct": true}, {"query": "先判一判对的在括号里画＂ $\\sqrt{}$ ＂错的在括号里画＂ $\\times$ ＂再订正（1） 54-22+8 订正 （2） 12 ÷2×2 订正=54-30                                                                =12÷4=24（        ）            =3 （        ）1. 对于式子 $54-22+8$       在数学运算中加减法属于同级运算要按照从左到右的顺序依次计算      原答案先计算了 $22+8=30$ 再用 $54-30=24$ 这不符合运算顺序      正确的计算是先算 $54-22=32$ 再算 $32+8=40$ 2. 对于式子 $12\\div2\\times2$       乘除法也属于同级运算同样要按照从左到右的顺序依次计算      原答案先计算了 $2\\times2=4$ 再用 $12\\div54-22+80$ 不符合运算顺序      正确的计算是先算 $12\\div54-22+81$ 再算 $6\\times54-22+82$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "similarity_score": 0.8585361838340759, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "topk_results": [{"similarity_score": 0.8585361838340759, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "sample_id": 34971}], "prediction_correct": true}, {"query": "小明在计算＂ $\\square-25+15$ ＂时先算 $25+15=40$ 再算 $\\square-40$ 结果与实际相比相差多少本题可先分别求出正确结果和错误结果再计算二者的差值计算正确结果按照正确的运算顺序从左到右依次计算□ - 25 + 15可得□ - 25 + 15 = □ - 10计算错误结果小明先算25 + 15 = 40再算□ - 40所以错误结果为□ - 40计算结果差值用正确结果减去错误结果可得( □ - 10 ) - ( □ - 40 )=□ - 10 - □ + 40=30所以结果与实际相比相差30", "true_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "similarity_score": 0.7493585348129272, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(乘法分配律)", "补充知识点4819", "补充知识点4820"], "topk_results": [{"similarity_score": 0.7493585348129272, "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(乘法分配律)", "补充知识点4819", "补充知识点4820"], "sample_id": 27767}], "prediction_correct": false}, {"query": "1、前置信息看图列式解答2、请回答（1）根据题目中的图片初始金额为50元先花费12元再花费18元因此剩余金额可以通过以下计算得到50 - 12 - 18 = 20元所以剩余金额为20元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "看图列综合算式并计算"], "similarity_score": 0.7220072746276855, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数与两位数的乘除混合运算", "用乘除混合解决实际问题"], "topk_results": [{"similarity_score": 0.7220072746276855, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数与两位数的乘除混合运算", "用乘除混合解决实际问题"], "sample_id": 4778}], "prediction_correct": false}, {"query": "1、前置信息商场开展促销活动每满 60 元减 12 元2、请回答（1）英英买一个铅笔盒、一盒铅笔、一本日记本50元够吗英英买一个铅笔盒、一盒铅笔、一本日记本的总价为23元 + 15元 + 18元 = 56元虽然总价为56元但未满60元不能享受每满60元减12元的优惠因此需要支付56元50元不够", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "similarity_score": 0.699389636516571, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加、减法混合运算解决问题（一位小数）"], "topk_results": [{"similarity_score": 0.699389636516571, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加、减法混合运算解决问题（一位小数）"], "sample_id": 20994}], "prediction_correct": false}, {"query": "数学游戏＂ 24 点＂是一种数学游戏下图为抽取的 4 张牌请利用牌面上的数字和加、减、乘、除四种运算符（可加括号）把这 4 个数算成 24 每张牌必须使用一次且只能使用一次试着列式计算本题可以从等于24的一些算式入手如3×8=24,则组成的算式是4-1=36+2=83×8=24列综合算式是( 4 - 1 ) × ( 6 + 2 ) = 24", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "similarity_score": 0.8063598871231079, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "24点", "24点游戏"], "topk_results": [{"similarity_score": 0.8063598871231079, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "24点", "24点游戏"], "sample_id": 18149}], "prediction_correct": false}, {"query": "脱式计算（48-18）÷6 48-18÷6 72-（45-19） 9×（72÷8）1. 计算（48 - 18）÷6 - 先算括号里的48 - 18 = 30 - 再算30÷6 = 52. 计算48 - 18÷6 - 先算除法18÷6 = 3 - 再算减法48 - 3 = 453. 计算72 - （48-180） - 先算括号里的48-180 = 26 - 再算48-1824. 计算9×（72÷8） - 先算括号里的72÷48-183 - 再算9×48-184", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "similarity_score": 0.7987850308418274, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "topk_results": [{"similarity_score": 0.7987850308418274, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "sample_id": 40848}], "prediction_correct": true}, {"query": "1、前置信息商场开展促销活动每满 60 元减 12 元2、请回答（2）亮亮带 100 元买了一个削笔刀、一个铅笔盒和一本日记本还剩多少钱亮亮购买的物品总价为削笔刀 35 元 + 铅笔盒 23 元 + 日记本 18 元 = 76 元根据促销活动每满 60 元减 12 元因此可以减 12 元实际支付金额为76 元 - 12 元 = 64 元亮亮带了 100 元因此还剩100 元 - 64 元 = 36 元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "similarity_score": 0.6632436513900757, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加减法应用题"], "topk_results": [{"similarity_score": 0.6632436513900757, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加减法应用题"], "sample_id": 18701}], "prediction_correct": false}, {"query": "下面算式中的图形代表多少（1） $10\\div\\triangle+5=7,\\triangle=( \\quad )$ （2） $38-\\square\\times8=6,\\square=( \\quad )$ （1）对于等式 $10\\div\\triangle+5=7$ 我们可以把 $10\\div\\triangle$ 看成一个整体因为一个加数+另一个加数 $=$ 和那么 $10\\div\\triangle$ 就等于和 $7$ 减去另一个加数 $5$ 即 $10\\div\\triangle=7-5=2$ 根据被除数÷除数=商那么除数 $\\triangle$ 就等于被除数 $10$ 除以商 $2$ 即 $\\triangle=10\\div2=5$ （2） 对于等式 $38-\\square\\times8=6$ 把 $\\square\\times8$ 看成一个整体因为被减数-减数=差那么 $\\square\\times8$ 就等于被减数 $38$ 减去差 $6$ 即 $\\square\\times8=38-6=32$ 根据因数 $\\times$ 因数 $=$ 积那么一个因数□就等于积 $32$ 除以另一个因数 $8$ 即 $\\square=32\\div8=4$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "不带括号的混合运算", "不含括号的表内混合运算"], "similarity_score": 0.8481215238571167, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "不带括号的混合运算", "不含括号的表内混合运算"], "topk_results": [{"similarity_score": 0.8481215238571167, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "不带括号的混合运算", "不含括号的表内混合运算"], "sample_id": 33826}], "prediction_correct": true}, {"query": "一本书 78 页还剩多少页没有看第六天从第几页开始看每天看8页看了5天共看了 $8\\times5=40$ 页这本书共有78页还剩 $78-40=38$ 页没有看第六天从第41页开始看", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "similarity_score": 0.7753448486328125, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "topk_results": [{"similarity_score": 0.7753448486328125, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "sample_id": 13817}], "prediction_correct": true}, {"query": "请在适当的位置加上小括号使等式成立4＋3×9=63 20-12÷4=2 24÷3+1=61. 对于式子4＋3×9=63 - 按照原来的运算顺序是先算乘法3×9 = 27再算加法4 + 27 = 31结果不等于63 - 我们想要结果是63因为9乘7等于63所以把4 + 3用小括号括起来先算小括号里的4 + 3 = 7再算7×9=630等式就成立了2. 对于式子9=631÷4=2 - 原来的运算顺序是先算除法12÷9=633再算减法9=634结果不等于2 - 我们想要结果是2因为8除以4等于2所以把9=631用小括号括起来先算小括号里的9=631 = 8再算8÷9=637等式成立3. 对于式子24÷9=638 - 原来的运算顺序是先算除法24÷9=639再算加法20-120结果不等于6 - 我们想要结果是6因为24除以4等于6所以把20-121用小括号括起来先算小括号里的20-121 = 4再算24÷20-123等式成立", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "有括号的运算顺序", "加小括号的综合问题(表内混合运算)", "补充知识点1853"], "similarity_score": 0.7710191011428833, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "有括号的运算顺序", "加小括号的综合问题(表内混合运算)", "补充知识点1853"], "topk_results": [{"similarity_score": 0.7710191011428833, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "有括号的运算顺序", "加小括号的综合问题(表内混合运算)", "补充知识点1853"], "sample_id": 42410}], "prediction_correct": true}, {"query": "想一想每道题先算什么再计算 $57-36\\div9$ $4+5\\times4$ $31+7\\times7$ $50-30\\div5$ 1. 对于 $57-36\\div9$  - 根据数学运算顺序先算除法再算减法 - 先计算 $36\\div9=4$ 再计算 $57-4=53$ 2. 对于 $4+5\\times4$  - 先算乘法再算加法 - 先计算 $5\\times4=20$ 再计算 $57-360$ 3. 对于 $31+7\\times7$  - 先算乘法再算加法 - 先计算 $7\\times57-362$ 再计算 $57-363$ 4. 对于 $50-30\\div5$  - 先算除法再算减法 - 先计算 $30\\div57-365$ 再计算 $57-366$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "含有两级运算的运算顺序", "补充知识点1843"], "similarity_score": 0.8132614493370056, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "topk_results": [{"similarity_score": 0.8132614493370056, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "sample_id": 34975}], "prediction_correct": false}, {"query": "国庆节到了超市开展优惠促销活动彩笔原价 12 元一盒（1）促销的彩笔一盒多少钱（2）每盒比原来便宜多少钱（1）促销时买2盒送1盒即3盒只需要支付2盒的钱因此3盒彩笔的总价为 $12\\times2=24$ 元每盒彩笔的促销价为 $24\\div3=8$ 元（2）原价为12元一盒促销价为8元一盒因此每盒比原来便宜 $12-8=4$ 元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "similarity_score": 0.8566170930862427, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "topk_results": [{"similarity_score": 0.8566170930862427, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "sample_id": 26291}], "prediction_correct": true}, {"query": "先填一填再列式算一算在没有括号的算式里只有加、减法或只有乘、除法从（        ） 往（        ） 按顺序计算分析第一个综合算式1. $25+14=39$ 2. $39-6=33$ 因此综合算式为 $25+14-6=39-6=33$ 分析第二个综合算式1. $6\\times4=24$ 2. $24\\div8=3$ 因此综合算式为 $6\\times4\\div8=24\\div8=3$ 在没有括号的算式里只有加、减法或只有乘、除法从左往右按顺序计算", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "similarity_score": 0.8787108063697815, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "不含括号的四则混合运算的运算顺序", "补充知识点1841"], "topk_results": [{"similarity_score": 0.8787108063697815, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "不含括号的四则混合运算的运算顺序", "补充知识点1841"], "sample_id": 36983}], "prediction_correct": false}, {"query": "海海把＂ $\\square-12+8$ ＂计算成＂ $\\square-( 12+8 )$ ＂结果和实际相比相差多少请算一算首先按照正确的运算顺序 $\\square-12+8$ 就是先减12再加8而海海算成了 $\\square-( 12+8 )=\\square-12-8$ 也就是先减12再减8我们来比较这两个式子正确结果是加8错误结果是减8那么结果相差就是 $8+8=16$ ", "true_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "similarity_score": 0.7094532251358032, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(乘法分配律)", "补充知识点4819", "补充知识点4820"], "topk_results": [{"similarity_score": 0.7094532251358032, "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(乘法分配律)", "补充知识点4819", "补充知识点4820"], "sample_id": 27748}], "prediction_correct": false}, {"query": "先填一填再列式算一算在没有括号的算式里如果既有乘、除法又有加、减法要先算（        ） 法再算（        ）法1. 左边的算式4 × 9 = 36然后 27 + 36 = 63因此综合算式为 27 + 4 × 9 = 632. 右边的算式48 ÷ 6 = 8然后 98 - 8 = 90因此综合算式为 98 - 48 ÷ 6 = 903. 在没有括号的算式里如果既有乘、除法又有加、减法要先算乘、除法再算加、减法", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "含有两级运算的运算顺序", "补充知识点1843"], "similarity_score": 0.8158763647079468, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "不含括号的四则混合运算的运算顺序", "补充知识点1841"], "topk_results": [{"similarity_score": 0.8158763647079468, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "不含括号的四则混合运算的运算顺序", "补充知识点1841"], "sample_id": 36984}], "prediction_correct": false}, {"query": "一条彩带对折 2 次后每段长 6 分米如果对折 3 次每段长多少分米对折 1 次彩带被平均分成 2 段对折 2 次彩带被平均分成 $2×2=4$ 段对折 3 次彩带被平均分成 $2×2×2=8$ 段已知对折 2 次后每段长 6 分米因为对折 2 次后彩带被分成 4 段所以彩带总长度为 $6×4=24$ 分米对折 3 次后彩带被分成 8 段那么每段长度是 $24÷8=3$ 分米", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.8817930221557617, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数与除法的关系", "分数与除法关系的应用-"], "topk_results": [{"similarity_score": 0.8817930221557617, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数与除法的关系", "分数与除法关系的应用-"], "sample_id": 21843}], "prediction_correct": false}, {"query": "1、前置信息如果每 8 根胡萝卜装一篮一共装几篮2、请回答（2）列式解答首先计算总共有多少根胡萝卜 $12+28=40$ 根然后根据每8根装一篮计算可以装几篮 $40\\div8=5$ 篮因此一共装5篮", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "除加、除减混合运算", "除加除减应用题(表内混合运算)"], "similarity_score": 0.6912379264831543, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数与除法的关系", "平均分与分数-"], "topk_results": [{"similarity_score": 0.6912379264831543, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数与除法的关系", "平均分与分数-"], "sample_id": 23423}], "prediction_correct": false}, {"query": "7 的4 倍是（        ） （        ） 的5 倍是 4052 比（        ） 少 121. 求 7 的 4 倍是多少根据乘法的意义求一个数的几倍是多少用乘法计算所以 7 的 4 倍就是 7×4 = 282. 已知一个数的 5 倍是 40求这个数根据除法的意义已知一个数的几倍是多少求这个数用除法计算所以这个数是 40÷5 = 83. 求 52 比哪个数少 1252 比这个数少 12说明这个数比 52 大 12求比一个数大几的数是多少用加法计算所以这个数是 52 + 12 = 64", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "不带括号的混合运算", "不含括号的表内混合运算"], "similarity_score": 0.6564311981201172, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "两位数乘整十数的应用"], "topk_results": [{"similarity_score": 0.6564311981201172, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "两位数乘整十数的应用"], "sample_id": 12449}], "prediction_correct": false}, {"query": "1、前置信息根据题意将线段图补充完整并列式解答2、请回答（1）小雅计划折 99 只千纸鹤已经折了 50 只剩下的如果要在 7 天折完平均每天需要折几只根据题目小雅计划折 99 只千纸鹤已经折了 50 只因此剩下的千纸鹤数量为 $99-50=49$ 只剩下的 49 只需要在 7 天内折完因此平均每天需要折的千纸鹤数量为 $49\\div7=7$ 只", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "除加、除减混合运算", "除加除减应用题(表内混合运算)"], "similarity_score": 0.7409753799438477, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "7的乘法口诀及应用", "7的乘法应用题（旧版）"], "topk_results": [{"similarity_score": 0.7409753799438477, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "7的乘法口诀及应用", "7的乘法应用题（旧版）"], "sample_id": 15823}], "prediction_correct": false}, {"query": "将导图填写完整并列式解答（1）首先计算3天内修了多少千米每天修6千米修了3天因此修了 $6\\times3=18$ 千米水渠总长20千米已经修了18千米因此还剩 $20-18=2$ 千米（2）首先计算玫瑰花和康乃馨共有多少朵玫瑰花15朵康乃馨12朵因此共有 $15+12=27$ 朵每束9朵因此可以扎 $27\\div9=3$ 束", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "根据分步运算列综合算式(含括号)(表内混合运算)"], "similarity_score": 0.6690717339515686, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "式与方程", "列方程解决稍复杂的实际问题", "线段图与方程", "补充知识点2274", "补充知识点2275"], "topk_results": [{"similarity_score": 0.6690717339515686, "predicted_label": ["小学数学新知识树", "数与代数", "式与方程", "列方程解决稍复杂的实际问题", "线段图与方程", "补充知识点2274", "补充知识点2275"], "sample_id": 14275}], "prediction_correct": false}, {"query": "要使算式 $( \\square-3 )\\times6=36$ 成立 里应填 （        ） A． 9B． 8C． 6要使算式 $( \\square-3 )\\times6=36$ 成立首先将等式两边同时除以6得到 $\\square-3=6$ 然后将等式两边同时加上3得到 $\\square=9$ 因此□里应填 9", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "similarity_score": 0.8169492483139038, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "topk_results": [{"similarity_score": 0.8169492483139038, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "sample_id": 40847}], "prediction_correct": true}, {"query": "下面不可以用 $4\\times3+2$ 解决的是（        ）A.B.C.选项A中有4个三角形每个3根小棒再加上2根小棒可以用 $4\\times3+2$ 来表示选项B中有3个正方形每个4根小棒再加上2根小棒也可以用 $4\\times3+2$ 来表示选项C中有2个正方形每个4根小棒再加上1个三角形每个3根小棒用4×2+3来表示综上不可以用 $4\\times3+2$ 解决的是选项C", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "similarity_score": 0.7509722709655762, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "topk_results": [{"similarity_score": 0.7509722709655762, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "sample_id": 13784}], "prediction_correct": true}, {"query": "我会算56-35÷7 （24+16）÷8 72÷9×3 5+4×91. 计算56 - 35÷7 - 先算除法35÷7 = 5 - 再算减法56 - 5 = 512. 计算（24 + 16）÷8 - 先算括号里的加法24 + 16 = 40 - 再算除法40÷8 = 53. 计算72÷9×3 - 按照从左到右的顺序先算除法72÷56-350 - 再算乘法8×56-3514. 计算56-352×9 - 先算乘法4×56-353 - 再算加法56-354", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "similarity_score": 0.7716588973999023, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "有余数的除法", "竖式计算", "有余数除法竖式"], "topk_results": [{"similarity_score": 0.7716588973999023, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "有余数的除法", "竖式计算", "有余数除法竖式"], "sample_id": 40643}], "prediction_correct": false}, {"query": "一道综合算式先算 $32-18=14$ 再算 $14\\div7=2$ 它是（                    ） 题目先算32 - 18再用所得的差除以7所以要把32 - 18用括号括起来这个综合算式就是( 32 - 18 )÷7=2", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "根据分步运算列综合算式(含括号)(表内混合运算)"], "similarity_score": 0.7343189716339111, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "有括号的运算顺序", "含有中括号的四则混合运算的运算顺序", "补充知识点1849"], "topk_results": [{"similarity_score": 0.7343189716339111, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "有括号的运算顺序", "含有中括号的四则混合运算的运算顺序", "补充知识点1849"], "sample_id": 18093}], "prediction_correct": false}, {"query": "1、前置信息面包店每天晚上 9 点就开始搞促销活动2、请回答（1）李叔叔带了 50 元买 3 个火腿肠面包需付多少钱首先计算3个火腿肠面包的总价 $3\\times8=24$ 元因为总价为24元未满30元所以不享受每满30元减8元的优惠因此李叔叔需付24元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "similarity_score": 0.6287025213241577, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "经济问题", "优惠方案问题(表内除法)", "补充知识点2828"], "topk_results": [{"similarity_score": 0.6287025213241577, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "经济问题", "优惠方案问题(表内除法)", "补充知识点2828"], "sample_id": 30664}], "prediction_correct": false}, {"query": "1、前置信息如果每 8 根胡萝卜装一篮一共装几篮2、请回答（1）根据题意将线段图填写完整 根据题目信息小白兔拔了12根胡萝卜小灰兔拔了28根胡萝卜因此线段图中应填写小白兔拔的为12根小灰兔拔的为28根", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.7129676342010498, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "topk_results": [{"similarity_score": 0.7129676342010498, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "sample_id": 15529}], "prediction_correct": false}, {"query": "根据右下图亮亮列出的算式为 $8\\times4-22$ 那么他解决的问题是（        ） A． 1 盒水彩笔比 1 本练习本贵多少钱B．买 1 盒水彩笔比买 4 本练习本多花多少钱C．买 1 盒水彩笔比买 4 本练习本少花多少钱根据题目中的算式 $8\\times4-22$ 我们可以进行如下分析1. $8\\times4$ 表示买 4 本练习本的总费用即 $8\\times4=32$ 元2. $32-22$ 表示买 4 本练习本的费用与买 1 盒水彩笔的费用之差即 $32-22=10$ 元因此算式 $8\\times4-22$ 解决的问题是“买 1 盒水彩笔比买 4 本练习本少花多少钱”故正确答案为 C", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.7530882358551025, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数加减法解决实际问题", "利用小数加减法解决综合问题"], "topk_results": [{"similarity_score": 0.7530882358551025, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数加减法解决实际问题", "利用小数加减法解决综合问题"], "sample_id": 13389}], "prediction_correct": false}, {"query": "1、前置信息面包店每天晚上 9 点就开始搞促销活动2、请回答（2）张阿姨买了 4 个牛角包和 2 个火腿肠面包需付多少钱张阿姨买了4个牛角包和2个火腿肠面包每个牛角包4元4个牛角包的总价为 $4\\times4=16$ 元每个火腿肠面包8元2个火腿肠面包的总价为 $8\\times2=16$ 元因此张阿姨购买的总价为 $16+16=32$ 元根据促销活动每满30元减8元张阿姨的总价为32元满足满30元的条件可以减8元所以张阿姨实际需付 $32-8=24$ 元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "similarity_score": 0.6458724737167358, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "经济问题", "优惠方案问题(表内除法)", "补充知识点2828"], "topk_results": [{"similarity_score": 0.6458724737167358, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "经济问题", "优惠方案问题(表内除法)", "补充知识点2828"], "sample_id": 30664}], "prediction_correct": false}, {"query": "如果 $\\square\\div3+5=10$ 则 $\\square=( \\quad )$ 如果 $20-\\triangle\\times3=8$ 则 $\\triangle=( \\quad )$ 本题可根据等式的性质分别求出□和 $\\triangle$ 的值步骤一求解□的值已知 $\\square\\div3+5=10$ 根据等式的性质等式两边同时减去同一个数等式仍然成立所以在等式两边同时减去 $5$ 可得 $\\square\\div3+5-5=10-5$ 即 $\\square\\div3=5$ 再根据等式的性质等式两边同时乘同一个数等式仍然成立在等式两边同时乘 $3$ 可得 $\\square\\div3\\times3=5\\times3$ 即 $\\square=15$ 步骤二求解 $\\triangle$ 的值已知 $20-\\triangle\\times3=8$ 根据等式的性质等式两边同时加上同一个数等式仍然成立在等式两边同时加上 $\\triangle\\times3$ 可得 $20-\\triangle\\times3+\\triangle\\times3=8+\\triangle\\times3$ 即 $20=8+\\triangle\\times3$ 然后等式两边同时减去 $8$ 可得 $3+5=100+\\triangle\\times3+5=101$ 即 $12=\\triangle\\times3$ 最后根据等式的性质等式两边同时除以同一个不为 $0$ 的数等式仍然成立在等式两边同时除以 $3$ 可得 $12\\div3=\\triangle\\times3\\div3$ 即 $\\triangle=4$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "不带括号的混合运算", "不含括号的表内混合运算"], "similarity_score": 0.8373446464538574, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "乘、除法的意义和各部分间的关系", "加、减、乘、除各部分间的关系的计算与应用", "补充知识点1815"], "topk_results": [{"similarity_score": 0.8373446464538574, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "乘、除法的意义和各部分间的关系", "加、减、乘、除各部分间的关系的计算与应用", "补充知识点1815"], "sample_id": 27209}], "prediction_correct": false}, {"query": "红红在计算＂ $24-\\square\\div4$ ＂时弄错了运算顺序先算减法再算除法结果得数是 3 正确的得数应该是多少本题可先根据错误的运算顺序求出 $□$ 里的数再按照正确的运算顺序计算出结果步骤一根据错误的运算顺序求出 $□$ 里的数红红弄错了运算顺序先算减法再算除法即 $( 24-□ )÷4=3$ 根据“被除数 $=$ 除数 $\\times$ 商”可得 $24-□=3\\times4=12$ 再根据“减数 $=$ 被减数 $-$ 差”可得 $□=24-12=12$ 步骤二按照正确的运算顺序计算结果将 $□=12$ 代入原式 $24-□÷4$ 得到 $24-12÷4$ 根据数学运算顺序先算除法再算减法即 $12\\div4=3$  $24-3=21$ ", "true_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "similarity_score": 0.8274967670440674, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "topk_results": [{"similarity_score": 0.8274967670440674, "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "sample_id": 38117}], "prediction_correct": true}, {"query": "在同一平面内两条直线都和第三条直线垂直那么这两条直线（ ） ① 相交② 互相平行③ 互相垂直④ 都有可能", "true_label": ["小学数学新知识树", "图形与几何", "平面图形", "平行与垂直的特征、性质", "平行的特征及性质", "关于平行的相关判断", "补充知识点3131"], "similarity_score": 0.768141508102417, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "平行与垂直的特征、性质", "平行的特征及性质", "关于平行的相关判断", "补充知识点3131"], "topk_results": [{"similarity_score": 0.768141508102417, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "平行与垂直的特征、性质", "平行的特征及性质", "关于平行的相关判断", "补充知识点3131"], "sample_id": 38889}], "prediction_correct": true}, {"query": "数一数各有几组平行线 第一张图中有三条平行线因此有3组平行线第二张图是一个平行四边形有两组平行线第三张图是一个梯形只有一组平行线", "true_label": ["小学数学新知识树", "图形与几何", "平面图形", "平行与垂直的特征、性质", "平行的特征及性质", "关于平行的相关判断", "补充知识点3131"], "similarity_score": 0.7224063873291016, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "枚举法数正方形", "补充知识点5283", "补充知识点5284"], "topk_results": [{"similarity_score": 0.7224063873291016, "predicted_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "枚举法数正方形", "补充知识点5283", "补充知识点5284"], "sample_id": 43161}], "prediction_correct": false}, {"query": "如果除数除以 18要使商不变被除数应（ ） ① 除以 18② 乘 18③ 不变④ 乘 9根据商不变的规律被除数和除数同时乘或除以相同的数（0除外）商不变现在除数除以18要让商不变那被除数也得除以18所以答案选①", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.7537528276443481, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "topk_results": [{"similarity_score": 0.7537528276443481, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "sample_id": 17838}], "prediction_correct": false}, {"query": "解决问题学校食堂共有 436 名学生同时就餐如果每张桌子可以坐 12 人那么需要多少张桌子本题可根据总人数和每张桌子可坐人数用除法计算出需要桌子的数量若有余数则桌子数需要在商的基础上加 1已知共有 436 名学生每张桌子坐 12 人则可列出算式436÷12 = 36（张）……4（人）这意味着坐满 36 张桌子后还剩下 4 人这 4 人也需要 1 张桌子所以总共需要的桌子数为36 + 1 = 37（张）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "用三位数除以两位数(商是两位数)解决简单的实际问题"], "similarity_score": 0.706633448600769, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数与两位数的乘除混合运算", "用乘除混合解决实际问题"], "topk_results": [{"similarity_score": 0.706633448600769, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数与两位数的乘除混合运算", "用乘除混合解决实际问题"], "sample_id": 4759}], "prediction_correct": false}, {"query": "如果被除数乘 20要使商不变除数应（ ） ① 除以 20② 乘 40③ 不变④ 乘 20根据商不变的规律被除数和除数同时乘或除以相同的数（0除外）商不变现在被除数乘20要使商不变除数也应该乘20答案选④", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.7696880102157593, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "topk_results": [{"similarity_score": 0.7696880102157593, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "sample_id": 17834}], "prediction_correct": false}, {"query": "两个数的商是 8 如果被除数和除数都扩大为原来的 8 倍商是（ ） ① 64② 8③ 1④ 无法确定被除数和除数同时扩大相同的倍数（ $0$ 除外）商不变本题中被除数和除数都扩大为原来的 $8$ 倍所以商还是 $8$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.8737415075302124, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的性质（小数除法）-", "补充知识点2069", "补充知识点2070"], "topk_results": [{"similarity_score": 0.8737415075302124, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的性质（小数除法）-", "补充知识点2069", "补充知识点2070"], "sample_id": 14412}], "prediction_correct": false}, {"query": "在 里填上＂ $>$ ＂＂ $<$ ＂或＂ $=$ ＂360 $\\div$ 30360 $\\div$ 36 42 $\\div$ 7420 $\\div$ 70 75 $\\div$ 5 225 $\\div$ 15240 $\\div$ 80240 $\\div$ 8 480 $\\div$ 4048 $\\div$ 4 56 $\\div$ 8 560 $\\div$ 70对于360÷30○360÷36观察两边算式可知被除数不变除数越大则商越小因此应填“＞”  对于42÷7○420÷70观察两边算式可知被除数和除数同时扩大10倍则商不变因此应填“=”对于75÷5○225÷15观察两边算式可知被除数和除数同时扩大3倍则商不变 因此应填“=”      对于240÷80○240÷8观察两边算式可知被除数不变除数越小则商越大因此应填“＜”  对于480÷40○48÷4观察两边算式可知被除数和除数同时缩小10倍则商不变因此应填“=”对于56÷8○560÷70观察两边算式可知56÷8=560÷80被除数不变除数变小则商变大因此应填“＜”    ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.8357696533203125, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "整十、整百、整千数除以一位数的除法", "整十、整百、整千数除以一位数的应用"], "topk_results": [{"similarity_score": 0.8357696533203125, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "整十、整百、整千数除以一位数的除法", "整十、整百、整千数除以一位数的应用"], "sample_id": 29302}], "prediction_correct": false}, {"query": "直接写出得数 $380\\div20=$ $720\\div80=$ $66\\div33=$ $840\\div21=$ $560\\div14=$ $25\\times80=$ $50\\times9\\div45=$ $25\\times6\\div6=$ 1. 计算 $380\\div20$ 根据商不变的性质把被除数和除数同时缩小10倍变成 $38\\div2=19$ 2. 计算 $720\\div80$ 同样根据商不变的性质被除数和除数同时缩小10倍得到 $72\\div8=9$ 3. 计算 $66\\div33$ 直接计算可得 $66\\div33=2$ 4. 计算 $840\\div21$ 可以把840看成 $40\\times21$ 所以 $840\\div21=40$ 5. 计算 $560\\div14$ 把560看成 $40\\times14$ 那么 $560\\div14=40$ 6. 计算 $25\\times80$ 先算 $25\\times8=200$ 再在积的末尾添上1个0结果是20007. 计算 $50\\times9\\div45$ 先算 $50\\times9=450$ 再算 $450\\div45=10$ 8. 计算 $25\\times6\\div6$ 先算 $25\\times6=150$ 再算 $150\\div2=190$ 也可以根据乘除混合运算的性质先算 $6\\div2=191$ 再算 $25\\times2=192$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.8040293455123901, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "整数的四则混合运算", "整数的四则混合运算"], "topk_results": [{"similarity_score": 0.8040293455123901, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "整数的四则混合运算", "整数的四则混合运算"], "sample_id": 18183}], "prediction_correct": false}, {"query": "先找规律再写出商 $12\\div6=$ $8\\div2=$ $45\\div15=$ $72\\div8=$ $120\\div60=$ $80\\div20=$ $450\\div150=$ $720\\div80=$ $1200\\div600=$ $800\\div200=$ $4500\\div1500=$ $7200\\div800=$ 首先明确商不变的规律在除法里被除数和除数同时乘或除以相同的数（0除外）商不变①对于 $12\\div6$  $120\\div60$ 中被除数 $12$ 变为 $120$ 是乘 $10$ 除数 $6$ 变为 $60$ 也是乘 $10$  $1200\\div600$ 中被除数 $12$ 变为 $1200$ 是乘 $100$ 除数 $6$ 变为 $600$ 也是乘 $100$ 根据商不变规律 $12\\div6=120\\div60=1200\\div600=2$ ②对于 $8\\div2$  $80\\div20$ 中被除数 $8$ 变为 $80$ 是乘 $10$ 除数 $2$ 变为 $20$ 也是乘 $10$  $800\\div200$ 中被除数 $8$ 变为 $800$ 是乘 $100$ 除数 $2$ 变为 $200$ 也是乘 $100$ 根据商不变规律 $8\\div2=80\\div20=800\\div200=4$ ③对于 $45\\div15$  $450\\div150$ 中被除数 $45$ 变为 $450$ 是乘 $10$ 除数 $15$ 变为 $150$ 也是乘 $10$  $4500\\div1500$ 中被除数 $45$ 变为 $4500$ 是乘 $100$ 除数 $15$ 变为 $1500$ 也是乘 $100$ 根据商不变规律 $45\\div15=450\\div150=4500\\div1500=3$ ④对于 $72\\div8$  $720\\div80$ 中被除数 $72$ 变为 $720$ 是乘 $10$ 除数 $8$ 变为 $80$ 也是乘 $10$  $7200\\div800$ 中被除数 $72$ 变为 $7200$ 是乘 $100$ 除数 $8$ 变为 $800$ 也是乘 $100$ 根据商不变规律 $72\\div6=1200\\div6=1201\\div6=1202$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.7921870946884155, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数除以两位数的试商", "除数不接近整十数的试商"], "topk_results": [{"similarity_score": 0.7921870946884155, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数除以两位数的试商", "除数不接近整十数的试商"], "sample_id": 43737}], "prediction_correct": false}, {"query": "下列各算式的商与 $9600\\div640$ 相等的是（ ） ① $9600\\div64$ ② $96\\div64$ ③ $480\\div32$ ④ $960\\div640$ 根据商不变的规律被除数和除数同时乘或除以相同的数（0除外）商不变①中除数由 $640$ 变为 $64$ 被除数不变商改变②中被除数由 $9600$ 变为 $96$ 除数由 $640$ 变为 $64$ 被除数和除数变化倍数不同商改变③中由于 $9600\\div20=480$  $640\\div20=32$ 即被除数和除数同时除以20得到 $480\\div32$ 商不变④中被除数由 $9600$ 变为 $960$ 除数不变商改变综上与 $9600\\div640$ 相等的是 $480\\div32$ 答案选③", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.734542727470398, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "topk_results": [{"similarity_score": 0.734542727470398, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "sample_id": 13986}], "prediction_correct": true}, {"query": "两个数相除的商是 6 若两数都除以 3 则商是 ___根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变本题中两个数相除商是6当被除数和除数都除以3时商不变所以商还是6", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.7768666744232178, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用2-6的乘法口诀求商", "除法计算与运用(2-6)"], "topk_results": [{"similarity_score": 0.7768666744232178, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用2-6的乘法口诀求商", "除法计算与运用(2-6)"], "sample_id": 36964}], "prediction_correct": false}, {"query": "$A\\divB=12$ 若 $A、B$ 同时除以 4 则商是 ___ 若 $A$ 乘 $4,B$ 除以 2 则商是 ___本题可根据商的变化规律来求解1. 求 $A$ 、 $B$ 同时除以 $4$ 时的商根据商不变的规律被除数和除数同时乘或除以相同的数（ $0$ 除外）商不变已知 $A\\divB=12$ 当 $A$ 、 $B$ 同时除以 $4$ 时商不变所以商还是 $12$ 2. 求 $A$ 乘 $4$  $B$ 除以 $2$ 时的商当被除数 $A$ 乘 $4$ 时商也会乘 $4$ 此时商变为 $12×4=48$ 当除数 $B$ 除以 $2$ 时商反而会乘 $2$ 那么 $48$ 再乘 $2$ 即 $48×2=96$ 所以 $A$ 乘 $4$  $B$ 除以 $2$ 时商是 $96$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "similarity_score": 0.7654204368591309, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "因数与倍数", "公因数与最大公因数", "求两数公因数、最大公因数的特殊情况"], "topk_results": [{"similarity_score": 0.7654204368591309, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "因数与倍数", "公因数与最大公因数", "求两数公因数、最大公因数的特殊情况"], "sample_id": 36065}], "prediction_correct": false}, {"query": "两个数相除的商是 6 若两数都除以 3 则商是 ___根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变本题中两个数相除商是6当被除数和除数都除以3时商不变所以商还是6", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.7768666744232178, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用2-6的乘法口诀求商", "除法计算与运用(2-6)"], "topk_results": [{"similarity_score": 0.7768666744232178, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用2-6的乘法口诀求商", "除法计算与运用(2-6)"], "sample_id": 36964}], "prediction_correct": false}, {"query": "如果 $( A\\times20 )\\div( B\\times20 )=16$ 那么 $A\\divB=$ ___根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变在 $( A\\times20 )\\div( B\\times20 )$ 中被除数 $A$ 和除数 $B$ 都同时乘了20所以商不变那么 $A\\divB$ 的商和 $( A\\times20 )\\div( B\\times20 )$ 的商一样都是16", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.751858115196228, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "topk_results": [{"similarity_score": 0.751858115196228, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "sample_id": 17838}], "prediction_correct": false}, {"query": "$A\\divB=12$ 若 $A、B$ 同时除以 4 则商是 ___ 若 $A$ 乘 $4,B$ 除以 2 则商是 ___1. 求 $A$ 、 $B$ 同时除以 $4$ 时的商根据商不变的规律被除数和除数同时乘或除以相同的数（ $0$ 除外）商不变已知 $A\\divB=12$ 当 $A$ 、 $B$ 同时除以 $4$ 时商不变所以商还是 $12$ 2. 求 $A$ 乘 $4$  $B$ 除以 $2$ 时的商当被除数 $A$ 乘 $4$ 时商也会乘 $4$ 此时商变为 $12×4=48$ 当除数 $B$ 除以 $2$ 时商反而会乘 $2$ 那么 $48$ 再乘 $2$ 即 $48×2=96$ 所以 $A$ 乘 $4$  $B$ 除以 $2$ 时商是 $96$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "similarity_score": 0.7699320912361145, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "因数与倍数", "公因数与最大公因数", "求两数公因数、最大公因数的特殊情况"], "topk_results": [{"similarity_score": 0.7699320912361145, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "因数与倍数", "公因数与最大公因数", "求两数公因数、最大公因数的特殊情况"], "sample_id": 36065}], "prediction_correct": false}, {"query": "在 $\\bigcirc$ 里填上＂ $>$ ＂＂ $ 425÷452. 比较168÷12和329÷12 - 在除法运算中当除数相同时被除数越大商越大 - 因为168 < 329所以168÷12 < 329÷123. 比较42÷2和420÷20 - 根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变 - 420÷20中420是42乘10得到的20是2乘10得到的被除数和除数同时乘10所以42÷2 = 420÷20", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "similarity_score": 0.7565866708755493, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "两位数除以一位数", "两位数除以一位数的口算除法的应用"], "topk_results": [{"similarity_score": 0.7565866708755493, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "两位数除以一位数", "两位数除以一位数的口算除法的应用"], "sample_id": 42510}], "prediction_correct": false}, {"query": "如果 $( A\\times20 )\\div( B\\times20 )=16$ 那么 $A\\divB=$ ___根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变在 $( A\\times20 )\\div( B\\times20 )$ 中被除数 $A$ 和除数 $B$ 都同时乘了20所以商不变那么 $A\\divB$ 的商和 $( A\\times20 )\\div( B\\times20 )$ 的商一样都是16", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.751858115196228, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "topk_results": [{"similarity_score": 0.751858115196228, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "sample_id": 17838}], "prediction_correct": false}, {"query": "两个数相除的商是 18 若被除数和除数都乘 6 则商是（ ） ① 18② 108③ 3④ 54根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变本题中被除数和除数都乘6那么商不变还是18所以选①", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.8070201873779297, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用2-6的乘法口诀求商", "除法计算与运用(2-6)"], "topk_results": [{"similarity_score": 0.8070201873779297, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用2-6的乘法口诀求商", "除法计算与运用(2-6)"], "sample_id": 36964}], "prediction_correct": false}, {"query": "在 $\\bigcirc$ 里填上＂ $>$ ＂＂ $ 425÷45②比较168÷12和329÷12在除法运算中当除数相同时被除数越大商越大因为168 < 329所以168÷12 < 329÷12③比较42÷2和420÷20根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变420÷20中420是42乘10得到的20是2乘10得到的被除数和除数同时乘10所以42÷2 = 420÷20", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.7747013568878174, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "乘、除法的意义和各部分间的关系", "被除数、除数、商之间的关系（除数是一位数）", "补充知识点1817"], "topk_results": [{"similarity_score": 0.7747013568878174, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "乘、除法的意义和各部分间的关系", "被除数、除数、商之间的关系（除数是一位数）", "补充知识点1817"], "sample_id": 29286}], "prediction_correct": false}, {"query": "直接写出得数 $140\\div20=$ $300\\div60=$ $630\\div70=$ $450\\div90=$ $282\\div50\\approx$ $360\\div58\\approx$ $718\\div89\\approx$ $500\\div80\\approx$ $5600\\div800=$ $3500\\div700=$ $4800\\div1200=$ $24000\\div400=$ 1. 对于整十数相除如140÷20想14个十除以2个十就是14÷2 = 7同理300÷60 = 30÷6 = 5630÷70 = 63÷7 = 9450÷90 = 45÷9 = 55600÷800 = 56÷8 = 73500÷2 = 70÷2 = 714800÷2 = 72÷2 = 732. 对于估算 - 282÷50把282近似看成300300÷2 = 74 - 360÷58把58近似看成60360÷2 = 75 - 718÷89把718近似看成72089近似看成90720÷2 = 76 - 500÷80把500近似看成480480÷2 = 773. 24000÷40024000里面有240个百400里面有4个百240÷2 = 78", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商不变的规律", "补充知识点2039", "补充知识点2040"], "similarity_score": 0.8414645195007324, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "topk_results": [{"similarity_score": 0.8414645195007324, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "sample_id": 23999}], "prediction_correct": false}, {"query": "花苗每棵 8 元树苗每棵 12 元 176 元最多可以买（ ） 棵树苗① 22② 14③ 15④ 8本题可根据“数量 = 总价÷单价”来计算 $176$ 元最多可买树苗的数量已知树苗每棵 $12$ 元总价是 $176$ 元则可买树苗的数量为 $176\\div12=14$ （棵） $\\cdots\\cdots8$ （元）这意味着 $176$ 元买 $14$ 棵树苗后还剩 $8$ 元剩下的钱不够再买 $1$ 棵树苗所以最多可以买 $14$ 棵树苗", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "用三位数除以两位数(商是两位数)解决简单的实际问题"], "similarity_score": 0.6470115184783936, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "竞赛应用题", "归一归总问题", "解决归一归总问题", "补充知识点5447", "补充知识点5448"], "topk_results": [{"similarity_score": 0.6470115184783936, "predicted_label": ["小学数学新知识树", "数学竞赛", "竞赛应用题", "归一归总问题", "解决归一归总问题", "补充知识点5447", "补充知识点5448"], "sample_id": 15518}], "prediction_correct": false}, {"query": "实验小学有 580 名学生参加社会实践活动每辆客车限乘 40 人需要多少辆客车右图坚式中＂ $\\leftarrow$ ＂所指的数表示（ ） ① 坐满 14 辆客车还剩 18 人② 坐满 10 辆客车还剩 18 人③ 坐满 14 辆客车还剩 180 人④ 坐满 10 辆客车还剩 180 人题目要求分析竖式中箭头所指的数“18”表示的意义根据竖式580除以40商为14即坐满14辆客车然后用580减去14乘以40（即560）得到余数为20但是箭头指向的是“18”这是在计算过程中的一部分具体是58（580去掉一个0）除以4（40去掉一个0）时4乘以14得到56然后58减去56得到的差即18这表示在当前步骤中还剩18人（实际是180人因为之前都去掉了一个0）因此正确选项是① 坐满14辆客车还剩18人", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "用三位数除以两位数(商是两位数)解决简单的实际问题"], "similarity_score": 0.742535412311554, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "三位数除以一位数，首位不够除", "三位数除以一位数的应用(商是两位数)"], "topk_results": [{"similarity_score": 0.742535412311554, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "三位数除以一位数，首位不够除", "三位数除以一位数的应用(商是两位数)"], "sample_id": 7155}], "prediction_correct": false}, {"query": "实验小学有 580 名学生参加社会实践活动每辆客车限乘 40 人需要多少辆客车右图坚式中＂ $\\leftarrow$ ＂所指的数表示（ ） ① 坐满 14 辆客车还剩 18 人② 坐满 10 辆客车还剩 18 人③ 坐满 14 辆客车还剩 180 人④ 坐满 10 辆客车还剩 180 人竖式中箭头所指的“18”因为被除数和除数同时缩小了10倍（末尾同时去掉一个0）所以这里的18实际表示的是180它是580减去40×10的结果依次分析4个选项选项①说坐满14辆客车还剩18人与计算过程中箭头所指数字的意义不符错误选项②坐满10辆客车但剩余人数不是18人（实际是180人）错误选项③坐满14辆客车不符合因为这里是计算到十位上的1（表示10辆）错误选项④坐满10辆客车( 40×10=400 )还剩180人( 580-400=180 )与箭头所指数字实际意义（缩小10倍后显示为18实际是180）相符正确综上答案选④", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "用三位数除以两位数(商是两位数)解决简单的实际问题"], "similarity_score": 0.7356334924697876, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "三位数除以一位数，首位不够除", "三位数除以一位数的应用(商是两位数)"], "topk_results": [{"similarity_score": 0.7356334924697876, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "三位数除以一位数，首位不够除", "三位数除以一位数的应用(商是两位数)"], "sample_id": 7135}], "prediction_correct": false}, {"query": "学校开展图书捐赠公益活动共捐赠了 720 本图书要把这些图书每 15 本捆一捆每 6 捆装一箱需要装多少个箱子本题可先求出一共捆了多少捆再根据每箱装的捆数求出需要多少个箱子步骤一计算一共捆了多少捆已知共有 720 本图书每 15 本捆一捆求捆数就是求 720 里有多少个 15用除法计算可得捆数为 $720\\div15=48$ （捆）步骤二计算需要多少个箱子已知每 6 捆装一箱一共有 48 捆求箱子数就是求 48 里有多少个 6用除法计算可得箱子数为 $48\\div6=8$ （个）综上需要装 8 个箱子", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "用三位数除以两位数(商是两位数)解决简单的实际问题"], "similarity_score": 0.661725640296936, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "整数乘法结合律", "运用乘法交换律和结合律解决实际问题", "补充知识点1881"], "topk_results": [{"similarity_score": 0.661725640296936, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "整数乘法结合律", "运用乘法交换律和结合律解决实际问题", "补充知识点1881"], "sample_id": 29861}], "prediction_correct": false}, {"query": "某书店在国庆期间有优惠销售活动其中《趣味数学》每本 24 元买 4 本送 1 本 288 元最多能买多少本《趣味数学》首先计算不考虑赠送时288 元可以买几本《趣味数学》用总钱数除以每本书的单价即 288÷24 = 12 本然后因为买 4 本送 1 本看 12 本里面有几个 4 本12÷4 = 3也就是送 3 本最后把买的本数和送的本数相加12 + 3 = 15 本所以 288 元最多能买 15 本《趣味数学》", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "用三位数除以两位数(商是两位数)解决简单的实际问题"], "similarity_score": 0.7563755512237549, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加减法应用题(涉及小数简算)"], "topk_results": [{"similarity_score": 0.7563755512237549, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加减法应用题(涉及小数简算)"], "sample_id": 2184}], "prediction_correct": false}, {"query": "李大爷购买化肥的相关信息如下①李大爷带了 700 元去农技站买化肥②农技站离家 20 千米③买了 18 袋同样的化肥④剩下 160 元⑤用这些化肥给 12000平方米的稻田施肥每袋化肥多少钱（1）要解答上面的问题需要的信息有 ___（填序号）（2）列式解答（1）要求每袋化肥多少钱需要知道买化肥总共花的钱数和买的化肥袋数李大爷带的钱数减去剩下的钱数就是买化肥花的钱所以需要的信息是①李大爷带了700元去农技站买化肥③买了18袋同样的化肥④剩下160元（2）先算出买化肥花的钱 $700-160=540$ （元）再用花的钱除以买的袋数得到每袋化肥的价格即 $540÷18=30$ （元）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "用三位数除以两位数(商是两位数)解决简单的实际问题"], "similarity_score": 0.7210354804992676, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是整十数的笔算除法", "用三位数除以整十数解决简单的实际问题"], "topk_results": [{"similarity_score": 0.7210354804992676, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是整十数的笔算除法", "用三位数除以整十数解决简单的实际问题"], "sample_id": 43801}], "prediction_correct": false}, {"query": "下面是同一种钢笔的价格林老师要买 24 支这种钢笔至少要花多少钱若买 27 支呢首先我们来分析每种购买方式的单价1. 10支60元即每支 $60÷10=6$ 元2. 4支30元即每支 $30÷4=7.5$ 元3. 每支8元显然第一种方式最便宜第二种次之第三种最贵接下来计算买24支钢笔的最少花费1. 先买2组10支花费60×2=120元还差4支2. 再买1组4支花费30元因此买24支钢笔的最少花费为120+30=150元然后计算买27支钢笔的最少花费1. 先买2组10支花费60×2=120元还差7支2. 再买1组4支花费30元还差3支3. 最后买3支单支花费8×3=24元因此买27支钢笔的最少花费为120+30+24=174元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "除数是两位数的笔算除法", "用三位数除以两位数(商是两位数)解决简单的实际问题"], "similarity_score": 0.7051132917404175, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的除法", "用“去尾法”解决问题", "进一、去尾解决问题"], "topk_results": [{"similarity_score": 0.7051132917404175, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的除法", "用“去尾法”解决问题", "进一、去尾解决问题"], "sample_id": 7627}], "prediction_correct": false}, {"query": "一天小月妈妈要骑车外出外出之前必须做完以下几件事自行车打气需 2 分钟整理房间需 7 分钟擦鞋子需 2 分钟把衣服放进洗衣机需 1 分钟洗衣机自动洗涤需 20 分钟晾衣服需 5 分钟小月妈妈怎样做才能最节省时间至少要多少时间因为洗衣机自动洗涤的 20 分钟里可以同时做其他事情先把衣服放进洗衣机用 1 分钟然后洗衣机洗的时候去做自行车打气 2 分钟、整理房间 7 分钟、擦鞋子 2 分钟这几件事加起来2 + 7 + 2 = 11分钟11分钟小于20分钟能在洗衣机洗涤时完成最后再晾衣服用 5 分钟总共花费时间就是1 + 20 + 5 = 26分钟这样安排最节省时间", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "沏茶问题"], "similarity_score": 0.7171633243560791, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "合理安排时间"], "topk_results": [{"similarity_score": 0.7171633243560791, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "合理安排时间"], "sample_id": 23870}], "prediction_correct": false}, {"query": "用一只平底锅煎饼每次能同时放 2 块饼假定煎 1 块饼需要 4 分钟（煎正、反两面各需 2 分钟）（1）煎 6 块饼至少需要多少分钟（2）用同样的锅煎 108 块饼至少需要多少分钟（1）因为每次能同时放 2 块饼煎 1 块饼正、反两面各需 2 分钟也就是同时煎 2 块饼的正反面共需 4 分钟6 块饼可以分 6÷2 = 3 次煎完每次 4 分钟所以煎 6 块饼至少需要 4×3 = 12 分钟（2）同理108 块饼可以分 108÷2 = 54 次煎完每次 4 分钟所以煎 108 块饼至少需要 4×54 = 216 分钟", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "similarity_score": 0.8071361780166626, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "topk_results": [{"similarity_score": 0.8071361780166626, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "sample_id": 35220}], "prediction_correct": true}, {"query": "王涛感冒了姐姐要为王涛冲泡＂感冒冲剂＂她拿药需 1 分钟洗杯子需 1 分钟放药、搅拌均匀需 2 分钟饮水机烧水需 5 分钟姐姐最少需要（ ） 分钟① 9② 8③ 5④ 7因为饮水机烧水不需要一直盯着在烧水的 $5$ 分钟内可以同时完成拿药 $1$ 分钟和洗杯子 $1$ 分钟这二者加起来 $1+1=2$ 分钟 $2<5$ 即在烧水时间内可以完成这两项任务水烧好后再放药、搅拌均匀需 $2$ 分钟所以总共花费的时间就是烧水的 $5$ 分钟加上放药、搅拌均匀的 $2$ 分钟即 $5+2=7$ 分钟", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "沏茶问题"], "similarity_score": 0.7391615509986877, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "沏茶问题"], "topk_results": [{"similarity_score": 0.7391615509986877, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "沏茶问题"], "sample_id": 43598}], "prediction_correct": true}, {"query": "杨明与妹妹玩扑克牌比大小的游戏每人每次出一张牌各出三次赢两次者胜杨明手里的牌是 $9、7、5$ 妹妹手里的牌是 $8、6、3$ 如果杨明先出牌妹妹后出牌比赛结果是（ ） ① 妹妹可能赢② 妹妹不可能赢③ 杨明一定赢④ 杨明一定输这是一个关于扑克牌比大小策略的问题我们可以用杨明和妹妹手中的牌按照不同顺序出牌来分析比赛结果已知杨明先出牌妹妹后出牌若杨明出 $9$ 妹妹出 $3$ 杨明赢杨明出 $7$ 妹妹出 $8$ 妹妹赢杨明出 $5$ 妹妹出 $6$ 妹妹赢这样妹妹赢了两次最终妹妹获胜所以妹妹是有可能赢的", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "田忌赛马问题", "田忌赛马"], "similarity_score": 0.8106417059898376, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "田忌赛马问题", "田忌赛马"], "topk_results": [{"similarity_score": 0.8106417059898376, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "田忌赛马问题", "田忌赛马"], "sample_id": 35194}], "prediction_correct": true}, {"query": "用平底锅烙饼时同一张饼第一面需要烙 4 分钟第二面需要烙 3 分钟一只平底锅每次最多可以烙 2 张饼烙 3 张饼至少需要 ___分钟本题可通过合理安排烙饼的顺序来计算烙 $3$ 张饼所需的最少时间将 $3$ 张饼分别记为 $A$ 、 $B$ 、 $C$ 先同时烙 $A$ 、 $B$ 的第一面需要 $4$ 分钟把 $B$ 拿出来放入 $C$ 烙 $A$ 的第二面和 $C$ 的第一面 $C$ 的第一面需要 $4$ 分钟在这 $4$ 分钟内 $A$ 的第二面也在烙所以这一步需要 $4$ 分钟此时 $A$ 已经烙好再烙 $B$ 的第二面和 $C$ 的第二面 $B$ 的第二面需要 $3$ 分钟 $C$ 的第二面也需要 $3$ 分钟所以这一步需要 $3$ 分钟此时 $B$ 、 $C$ 也烙好了总共花费的时间为 $4+4+3=11$ （分钟）", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "similarity_score": 0.9161540269851685, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "topk_results": [{"similarity_score": 0.9161540269851685, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "sample_id": 35220}], "prediction_correct": true}, {"query": "玩具厂用机器给玩具贴图案每次只能同时贴两个玩具每个玩具都要求贴正、反两面贴一面需要 5 秒（1）怎样安排才能比较快地贴好 3 个玩具（2）贴 4 个玩具至少需要多少秒（3）贴 5 个玩具至少需要多少秒（4）把表格填完整你能发现什么信息\n| 玩具／个 | 3 | 4 | 5 | 20 | 100 |   | 所需的最少时间／秒 |   |   |   |   |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 玩具／个 | 3 | 4 | 5 | 20 | 100 |   |\n| 所需的最少时间／秒 |   |   |   |   |   |   |\n（1）为了较快贴好 3 个玩具要充分利用每次能同时贴两个玩具这个条件我们先把玩具 1 和玩具 2 的正面一起贴这用了 5 秒接着贴玩具 1 的反面和玩具 3 的正面又用了 5 秒最后贴玩具 2 和玩具 3 的反面还是 5 秒这样一共用了 15 秒是最快的贴法（2）贴 4 个玩具时我们可以两个两个地贴每次贴两个玩具的正反两面也就是一次贴好两个玩具需要 10 秒那么贴 4 个玩具分两次贴完总共就需要 20 秒（3）贴 5 个玩具时前面 3 个玩具按照（1）中的方法贴需要 15 秒剩下 2 个玩具一起贴正反两面需要 10 秒所以贴 5 个玩具至少需要 25 秒（4）通过前面的计算我们发现规律所需的最少时间等于玩具个数乘贴一面需要的时间所以 20 个玩具需要 20×5 = 100 秒100 个玩具需要 100×5 = 500 秒", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "similarity_score": 0.6485117077827454, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "正比例的应用", "正比例的应用（补全、平面图、切割）", "补充知识点2465"], "topk_results": [{"similarity_score": 0.6485117077827454, "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "正比例的应用", "正比例的应用（补全、平面图、切割）", "补充知识点2465"], "sample_id": 8089}], "prediction_correct": false}, {"query": "李明的妈妈早晨起床后要做以下几件事\n| ①刷牙洗脸 | 4 分钟 | ⑤准备煲汤 | 2 分钟 | ②和面 | 5 分钟 | ⑥用高压锅煲汤 | 20 分钟 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| ①刷牙洗脸 | 4 分钟 | ⑤准备煲汤 | 2 分钟 |\n| ②和面 | 5 分钟 | ⑥用高压锅煲汤 | 20 分钟 |\n| ③准备烙饼馅 | 6 分钟 | ⑦吃早饭 | 10 分钟 |\n| ④烙馅饼 | 9 分钟 | ⑧洗碗筷 | 4 分钟 |\n（1）李明的妈妈做完这些事至少需要多少分钟\n（2）李明的妈妈早上750 要到办公室并且每天上班骑车需要 20 分钟她每天早上最迟什么时间起床才能确保上班不迟到\n（3）假如李明的妈妈早上烙饼的数量由原来的 3 张增加到 5 张且每次只能烙一张馅饼烙好每张馅饼的时间相同做其他事的时间不变你觉得李明的妈妈每天的起床时间需不需要变化计算后说明理由（1）本题可根据各项事情所需时间及先后顺序通过合理安排时间来计算做完这些事的最少用时因为用高压锅煲汤不需要一直盯着在煲汤的20分钟内可以同时进行刷牙洗脸（4分钟）、准备烙饼馅（6分钟）、和面（5分钟）、烙馅饼（9分钟）这几件事总共花费 $4+6+5+9=24$ 分钟 $24>20$ 即在煲汤的时间内可以完成这些事然后再加上准备煲汤的2分钟、吃早饭的10分钟、洗碗筷的4分钟总共需要 $2+24+10+4=40$ 分钟（2）本题可根据上班时间、骑车时间和做这些事所需时间来计算最迟起床时间做完这些事需要40分钟骑车需要20分钟总共需要 $40+20=60$ 分钟7时50分往前推60分钟即7时50分 - 60分 = 6时50分所以她每天早上最迟650起床才能确保上班不迟到（3）本题可先计算出原来和现在烙饼所需时间再比较两者的差异从而判断起床时间是否需要变化原来烙3张饼需要9分钟则烙一张饼需要 $9÷3=3$ 分钟现在烙5张饼需要 $3×5=15$ 分钟比原来多了 $15-9=6$ 分钟所以起床时间要提前6分钟即每天的起床时间需要变化", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "沏茶问题"], "similarity_score": 0.7727189064025879, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "沏茶问题"], "topk_results": [{"similarity_score": 0.7727189064025879, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "沏茶问题"], "sample_id": 43602}], "prediction_correct": true}, {"query": "用一口锅烙饼每次只能放两块饼烙一块饼要 6 分钟（正、反面各需 3 分钟）（1）烙 1 块饼至少需要 ___分钟（2）烙 2 块饼至少需要 ___分钟（3）烙 3 块饼至少需要 ___分钟（4）烙 7 块饼至少需要 ___分钟（1）因为烙一块饼正、反面各需 3 分钟所以烙 1 块饼至少需要 $3+3=6$ 分钟（2）可以同时烙这 2 块饼的正面需要 3 分钟然后再同时烙这 2 块饼的反面也需要 3 分钟所以烙 2 块饼至少需要 $3+3=6$ 分钟（3）设这 3 块饼分别为 A、B、C第一步先烙 A 和 B 的正面需要 3 分钟第二步烙 A 的反面和 C 的正面需要 3 分钟第三步烙 B 和 C 的反面需要 3 分钟所以烙 3 块饼至少需要 $3×3=9$ 分钟（4）可以先 2 块 2 块地烙前 4 块饼每次烙 2 块共需烙 2 次每次 6 分钟所以前 4 块饼需要 $6×2=12$ 分钟剩下的 3 块饼按照上述烙 3 块饼的方法需要 9 分钟因此烙 7 块饼至少需要 $12+9=21$ 分钟", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "similarity_score": 0.8948243856430054, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "topk_results": [{"similarity_score": 0.8948243856430054, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "sample_id": 35207}], "prediction_correct": true}, {"query": "亮亮早晨起床后刷牙、洗脸需 5 分钟微波炉热早饭需 3 分钟吃早饭需 10 分钟他做完这些事至少要 ___分钟因为在微波炉热早饭的3分钟时间里可以同时进行刷牙、洗脸这二者不冲突刷牙、洗脸需5分钟热早饭3分钟5＞3即在刷牙、洗脸的过程中早饭就热好了然后再吃早饭10分钟所以一共至少需要5 + 10 = 15分钟", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "沏茶问题"], "similarity_score": 0.7827735543251038, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "合理安排时间"], "topk_results": [{"similarity_score": 0.7827735543251038, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "沏茶问题", "合理安排时间"], "sample_id": 23873}], "prediction_correct": false}, {"query": "王老师要复印 7 张获奖证书正、反面都要复印如果复印机里一次最多放两张证书至少要复印（ ） 次① 5② 6③ 7④ 8根据题意我们可先将前4张按照顺序2张2张地复印共需要4次最后3张可先复印两张的正面然后复印其中一张的反面与最后一张的正面最后再复印剩下两张的反面共需要3次故一共需要4+3=7（次）", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "similarity_score": 0.7594151496887207, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "topk_results": [{"similarity_score": 0.7594151496887207, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "优化问题", "烙饼问题", "烙饼问题"], "sample_id": 35217}], "prediction_correct": true}, {"query": "如右图所示 $∠1=35^{\\circ}$ 则 $∠2=$ ___  $∠3=$ ___  $∠4=$ ___  $∠5=$ ___（第 5 题）本题可根据直角、平角的度数以及角之间的关系来求解求∠2的度数由图可知∠2是直角根据直角的定义直角为90°所以∠2 = 90°求∠3的度数在三角形中三角形的内角和为180°已知∠1 = 35° ∠2 = 90°则∠3 = 180° - ∠1 -∠2 = 180° - 35° - 90° = 55°求∠4的度数因为∠3与∠4组成平角平角为180°所以∠4 = 180° - ∠3 = 180° - 55° = 125°求∠5的度数由于∠1与∠5组成平角平角是180°所以∠5 = 180° - ∠1=350° - 35° = 145°", "true_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "线段与角的综合", "角度直接运算", "补充知识点3207"], "similarity_score": 0.8162428736686707, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "三角形", "三角形的内角和", "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)", "补充知识点3410"], "topk_results": [{"similarity_score": 0.8162428736686707, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "三角形", "三角形的内角和", "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)", "补充知识点3410"], "sample_id": 3544}], "prediction_correct": false}, {"query": "估算下列各题 $402\\times8\\approx$ $89\\times31\\approx$ $408\\div52\\approx$ $638\\div69\\approx$ $202\\times90\\approx$ $139\\times19\\approx$ $299\\div62\\approx$ $320\\div39\\approx$ 1. 对于 $402\\times8$ 把402看作400 $400\\times8=3200$ 所以 $402\\times8\\approx3200$ 2. 对于 $89\\times31$ 把89看作9031看作30 $90\\times30=2700$ 所以 $89\\times31\\approx2700$ 3. 对于 $408\\div52$ 把408看作40052看作50 $400\\div50=8$ 所以 $408\\div52\\approx8$ 4. 对于 $638\\div69$ 把638看作63069看作70 $630\\div70=9$ 所以 $638\\div69\\approx9$ 5. 对于 $202\\times90$ 把202看作200 $200\\times90=18000$ 所以 $202\\times90\\approx18000$ 6. 对于 $139\\times19$ 把139看作14019看作20 $140\\times20=2800$ 所以 $139\\times19\\approx2800$ 7. 对于 $299\\div62$ 把299看作30062看作60 $300\\div60=5$ 所以 $299\\div62\\approx5$ 8. 对于 $320\\div39$ 把39看作40 $320\\div40=8$ 所以 $320\\div39\\approx8$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘两位数的估算", "三位数乘两位数的估算"], "similarity_score": 0.8070235252380371, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "topk_results": [{"similarity_score": 0.8070235252380371, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "sample_id": 7486}], "prediction_correct": false}, {"query": "已知 $A\\divB=18$ 若 $A$ 扩大 2 倍 $B$ 不变则商是 ___在除法算式中除数不变被除数扩大几倍商就扩大几倍已知 $A\\divB=18$ 现在 $A$ 扩大 $2$ 倍 $B$ 不变那么商也扩大 $2$ 倍 $18\\times2=36$ 所以商是 $36$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "商的变化规律", "商的变化规律", "补充知识点2042", "补充知识点2043"], "similarity_score": 0.782284140586853, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "比例的基本性质", "根据等式写比例", "补充知识点2405"], "topk_results": [{"similarity_score": 0.782284140586853, "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "比例的基本性质", "根据等式写比例", "补充知识点2405"], "sample_id": 9270}], "prediction_correct": false}, {"query": "观察思考回答问题四（1）班2024年5月学生借阅图书情况统计表\n| 图书种类 | 文学类 | 艺术类 | 科技类 | 漫画类 | 人数 | 18 | 6 | 9 | 15 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 图书种类 | 文学类 | 艺术类 | 科技类 | 漫画类 |\n| 人数 | 18 | 6 | 9 | 15 |\n．根据统计表将下面的统计图补充完整四（1）班学生借阅图书情况统计图2．喜欢借阅 ___图书的人数最多喜欢借阅 ___图书的人数最少3．若每人都只借阅一本图书则四（1）班一共有 ___人4．为了推进＂书香校园＂建设学校打算购买一批图书应该多购买 ___图书若将其他年级的学生也纳入调查 ___ （填＂会＂或＂不会＂）影响调查结果理由是 ___（1）（2）根据统计表喜欢借阅文学类图书的人数最多喜欢借阅艺术类图书的人数最少（3）若每人都只借阅一本图书则四（1）班一共有 18 + 6 + 9 + 15 = 48 人（4）为了推进＂书香校园＂建设学校应该多购买文学类图书因为文学类图书的借阅人数最多若将其他年级的学生也纳入调查会影响调查结果因为其他年级学生的借阅偏好可能与四（1）班不同从而影响整体的调查结果", "true_label": ["小学数学新知识树", "统计和概率", "统计", "统计图", "1格表示多个单位的单式条形统计图", "以一当多的条形统计图", "补充知识点4482"], "similarity_score": 0.7330876588821411, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数大小的比较", "异分母异分子分数比大小", "运用分数通分比较大小来解决问题"], "topk_results": [{"similarity_score": 0.7330876588821411, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数大小的比较", "异分母异分子分数比大小", "运用分数通分比较大小来解决问题"], "sample_id": 26696}], "prediction_correct": false}, {"query": "有一个正方形操场四周插彩旗要使每条边都插四面彩旗（每个顶点都要插）一共需要多少面彩旗（先画一画再计算）本题可通过两种方法来计算彩旗的总数方法一先计算四条边插彩旗的总数再减去四个顶点重复计算的彩旗数已知每条边插 $4$ 面彩旗正方形有四条边则四条边一共插了 $4\\times4=16$ 面彩旗由于每个顶点的彩旗都被重复计算了一次正方形有 $4$ 个顶点所以需要减去重复的 $4$ 面彩旗即 $16-4=12$ 面方法二先不考虑四个顶点的彩旗计算每条边中间插彩旗的数量再加上四个顶点的彩旗数每条边插 $4$ 面彩旗因为每个顶点都要插所以每条边中间插彩旗的数量是 $4-2=2$ 面正方形有四条边则四条边中间一共插了 $2\\times4=8$ 面彩旗再加上四个顶点的 $4$ 面彩旗可得彩旗总数为 $8+4=12$ 面综上一共需要 $12$ 面彩旗", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "表内乘加、乘减", "2-5的乘加乘减应用题"], "similarity_score": 0.6962597370147705, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "因数与倍数", "用最大公因数解决实际问题", "用求公因数的方法解决实际问题"], "topk_results": [{"similarity_score": 0.6962597370147705, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "因数与倍数", "用最大公因数解决实际问题", "用求公因数的方法解决实际问题"], "sample_id": 8024}], "prediction_correct": false}, {"query": "小猴开生日会邀请了小熊、小狗、小猪和小鸟来做客如果每只动物分到 4 个桃子准备 18 个桃子够吗口答准备 18 个桃子够 不够 （用画＂ $\\sqrt{}$ ＂表示）小猴邀请了4只动物来做客每只动物分到4个桃子那么总共需要的桃子数量为 $4\\times4=16$ 个桃子加上小猴自己也需要4个桃子因此总共需要 $16+4=20$ 个桃子准备了18个桃子显然不够因为 $18<20$ 因此答案是不够☑", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "2、3、4的乘法口诀及应用-", "2-4乘法口诀的应用题-"], "similarity_score": 0.7251671552658081, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "8、9的分与合", "8、9的分合的应用"], "topk_results": [{"similarity_score": 0.7251671552658081, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "8、9的分与合", "8、9的分合的应用"], "sample_id": 16145}], "prediction_correct": false}, {"query": "一堆糖果不足 20 粒如果平均分成 3 堆或 5 堆都剩下 1 粒这堆糖果有（ ） 粒（填所选答案序号）①25②19③16④10本题可先找出 $3$ 和 $5$ 的公倍数再结合糖果数量的条件来确定这堆糖果的数量 - 步骤一求 $3$ 和 $5$ 的公倍数因为 $3$ 和 $5$ 都是质数所以它们的最小公倍数为 $3\\times5=15$  - 步骤二根据条件确定糖果数量已知这堆糖果平均分成 $3$ 堆或 $5$ 堆都剩下 $1$ 粒所以糖果的数量是 $3$ 和 $5$ 的公倍数加 $1$ 即 $15+1=16$ 粒又因为这堆糖果不足 $20$ 粒 $16<20$ 满足条件而①选项 $25$ 粒不满足不足 $20$ 粒的条件②选项 $19$ 粒平均分成 $3$ 堆或 $5$ 堆不会都剩下 $1$ 粒④选项 $10$ 粒平均分成 $3$ 堆或 $5$ 堆也不会都剩下 $1$ 粒综上这堆糖果有 $16$ 粒答案选③", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "表内乘加、乘减", "2-5的乘加乘减应用题"], "similarity_score": 0.7923839688301086, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "表内乘加、乘减", "2-5的乘加乘减应用题"], "topk_results": [{"similarity_score": 0.7923839688301086, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "表内乘加、乘减", "2-5的乘加乘减应用题"], "sample_id": 43512}], "prediction_correct": true}, {"query": "在一个棋盘的四周摆上棋子每条边上摆 5 枚 4 个顶点上各摆 1 枚一共摆了（ ） 枚棋子解如图每条边上除过顶点有3枚共4条边除过顶点处总共棋子3×4=12枚加法上顶点的4枚棋子总数12+4=16（枚）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "表内乘加、乘减", "2-5的乘加乘减应用题"], "similarity_score": 0.6306518912315369, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "平均分的综合理解与应用"], "topk_results": [{"similarity_score": 0.6306518912315369, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "平均分的综合理解与应用"], "sample_id": 42552}], "prediction_correct": false}, {"query": "哪种分法对在对的 $\\square$ 里画＂ $\\sqrt{}$ ＂（1） 9 个橘子平均分成 3 盘 （2）8朵花平均分成2份解（1）图中都分成了3盘题中要求平均分每个盘子中的橘子数要一样多满足条件的是第2组（2）题中要求分成2份满足条件的是第2第3组还要满足平均分所以每盘中的花朵需一样多满足条件的是第3组", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "理解平均分"], "similarity_score": 0.7911921739578247, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "理解平均分"], "topk_results": [{"similarity_score": 0.7911921739578247, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "理解平均分"], "sample_id": 34314}], "prediction_correct": true}, {"query": "画一画在小动物下面用＂○＂表示相应分得的食物再填一填一共有（ ） 个 平均分给（ ） 只猴子每只猴子分得（ ） 个（2）把这些乏平均分给（ ） 只小狗每只小狗分到（ ） 根（1）图中有8个桃子4只猴子将8个桃子平均分给4只猴子每只猴子分得 $\\frac{8}{4}=2$ 个桃子（2）图中有12根骨头2只小狗将12根骨头平均分给2只小狗每只小狗分得 $\\frac{12}{2}=6$ 根骨头", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "理解平均分"], "similarity_score": 0.9254918098449707, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "等分(按指定的份数平均分)"], "topk_results": [{"similarity_score": 0.9254918098449707, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "等分(按指定的份数平均分)"], "sample_id": 36854}], "prediction_correct": false}, {"query": "根据算式 $20\\div4=5$ 画一幅图或编一道数学题略", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "初步认识除法"], "similarity_score": 0.7859538197517395, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "除法意义的理解与运用（移后删）"], "topk_results": [{"similarity_score": 0.7859538197517395, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "除法意义的理解与运用（移后删）"], "sample_id": 36925}], "prediction_correct": false}, {"query": "按照算式圈一圈填一填（1） $15\\div3=$ 我是把这些平均分成（ ） 份每份圈（ ） 个（2） $24\\div6=$ 我是把这些每（ ） 个圈一份圈了（ ） 份（1） $15\\div3=5$ 表示把15个星星平均分成3份每份圈5个（2） $24\\div6=4$ 表示把24个梨每6个圈一份圈了4份", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "初步认识除法"], "similarity_score": 0.8690844774246216, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "平均分求每份数（列除法算式）"], "topk_results": [{"similarity_score": 0.8690844774246216, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "平均分求每份数（列除法算式）"], "sample_id": 32263}], "prediction_correct": false}, {"query": "帮小朋友写出除法算式 —————————————— —————————————————————————————————— ——————————————————", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "除法意义的理解与运用（移后删）"], "similarity_score": 0.7700722217559814, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "用除法解决问题"], "topk_results": [{"similarity_score": 0.7700722217559814, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "用除法解决问题"], "sample_id": 1470}], "prediction_correct": false}, {"query": "李老师有一根长 8 米的绸带做一件教具要用 2 米（1）一共可以做（ ） 件这样的教具___（2）一段一段地剪一共要剪（ ） 次（用减法试试）___（1）已知绸带总长 8 米做一件教具用 2 米求能做几件教具就是求 8 里面有几个 2用除法计算 $8\\div2=4$ （件）（2）因为剪的次数比段数少 1由（1）可知能做成 4 件教具也就是能剪成 4 段用减法来想剪 1 次是 2 段剪 2 次是 3 段剪 3 次是 4 段所以一共要剪 3 次", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用2-6的乘法口诀求商", "连减与除法(2-6)"], "similarity_score": 0.7075490951538086, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "植树问题", "植树问题拓展应用", "间隔问题的相关应用"], "topk_results": [{"similarity_score": 0.7075490951538086, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "植树问题", "植树问题拓展应用", "间隔问题的相关应用"], "sample_id": 40892}], "prediction_correct": false}, {"query": "想一想填一填如果每只小兔分 4 根萝卜可以分给 只小兔12---=12÷=想4和几相乘得 12 （ ）四十二商是（ ） 题目中提到有12根萝卜每只小兔分4根萝卜首先计算可以分给几只小兔 $12\\div4=3$ 因此可以分给3只小兔然后根据减法验证 $12-4-4-4=0$ 即分给3只小兔后没有剩余最后根据乘法口诀“三四十二”确认商是3", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用2-6的乘法口诀求商", "连减与除法(2-6)"], "similarity_score": 0.7939662337303162, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(2-6)（旧版）"], "topk_results": [{"similarity_score": 0.7939662337303162, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(2-6)（旧版）"], "sample_id": 32414}], "prediction_correct": false}, {"query": "看图做一做（1）3 只小松鼠一起运松果一次运完平均每只运几个（2）如果小松鼠在路上吃掉了 6 个松果运到的松果有多少个（1）图中有 15 个松果3 只小松鼠一起运平均每只小松鼠运的松果数为 15 ÷ 3 = 5（个）（2）原来有 15 个松果小松鼠吃掉了 6 个运到的松果数为 15 - 6 = 9（个）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "列多个算式的应用题（2-6含除法）-"], "similarity_score": 0.7636435031890869, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "包含分求份数（列除法算式）"], "topk_results": [{"similarity_score": 0.7636435031890869, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "包含分求份数（列除法算式）"], "sample_id": 1876}], "prediction_correct": false}, {"query": "剪窗花时每张纸可以剪 2 个窗花（1）5 张纸可以剪（ ） 个窗花（2）宁宁要剪 12 个窗花需要用（ ）张纸（1）每张纸可以剪 2 个窗花5 张纸可以剪 $5\\times2=10$ 个窗花（2）宁宁要剪 12 个窗花需要用 $12\\div2=6$ 张纸", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "列多个算式的应用题（2-6含除法）-"], "similarity_score": 0.7514315843582153, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "乘除法的计算与应用(2-6)"], "topk_results": [{"similarity_score": 0.7514315843582153, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "乘除法的计算与应用(2-6)"], "sample_id": 36874}], "prediction_correct": false}, {"query": "买文具［第（2）小题可以只填写序号］①圆珠笔 ②笔盒 ③笔记本 ④水彩笔 3元 6 元 4 元 5 元（1） 20 元钱能买几筒水彩笔口答能买 ___筒水彩笔（2） 13 元最多能买几件不同的物品口答 ___件分别是 ___（1）20元钱能买几筒水彩笔解析每筒水彩笔5元20元可以买 $20\\div5=4$ 筒水彩笔（2）13元最多能买几件不同的物品解析要买最多的不同物品应选择价格较低的物品①圆珠笔3元③笔记本4元④水彩笔5元这三件物品总价为 $3+4+5=12$ 元不超过13元因此最多能买3件不同的物品分别是①③④", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "列多个算式的应用题（2-6含除法）-"], "similarity_score": 0.7501047253608704, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(2-6)（旧版）"], "topk_results": [{"similarity_score": 0.7501047253608704, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(2-6)（旧版）"], "sample_id": 32409}], "prediction_correct": false}, {"query": "观察下面右侧学校地图读一读填一填同学们好现在请跟着我一起来参观我们的学校进入大门以后向西走就是科技楼然后向北走是（ ） 最后我们回到存车处它的（ ） 边就是大门了根据学校地图进入大门后向西走到达科技楼然后向北走会到达（体育馆）最后回到存车处观察地图可以发现存车处的（西边）是大门", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "四个方向描述简单的行走路线", "补充知识点4212"], "similarity_score": 0.7330979108810425, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "根据描述确定位置（四个方向）", "补充知识点4230"], "topk_results": [{"similarity_score": 0.7330979108810425, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "根据描述确定位置（四个方向）", "补充知识点4230"], "sample_id": 27100}], "prediction_correct": false}, {"query": "教学楼在操场的北面看图填一填（1）体育馆在操场的（ ） 面操场在图书馆的（ ） 面图书馆在体育馆的（ ） 面（2）大门在教学楼的（ ） 面教学楼在大门的（ ） 面（1）根据图示体育馆在操场的东面操场在图书馆的东面图书馆在体育馆的西面（2）大门在教学楼的南面教学楼在大门的北面", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "在平面图上辨认东、南、西、北", "补充知识点4228"], "similarity_score": 0.8489462733268738, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "根据描述确定位置（四个方向）", "补充知识点4230"], "topk_results": [{"similarity_score": 0.8489462733268738, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "根据描述确定位置（四个方向）", "补充知识点4230"], "sample_id": 27100}], "prediction_correct": false}, {"query": "早上起来面向太阳（1）前面是（ ） 后面是（ ） 左面是（ ） 右面是（ ） （2）东与（ ） 相对（ ） 与北相对（3）如果向后转那么左面是（ ） 右面是（ ） 与面向太阳的人的左、右的方向（ ） （选填＂一样＂不一样＂）（1）早上太阳从东方升起当我们面向太阳时前面就是东因为东和西相对所以后面是西再根据“上北下南左西右东”当面向东时左面是北右面是南（2）在方向中东和西是相对的方向南和北是相对的方向（3）开始面向东向后转就变成面向西了此时根据“上北下南左西右东”面向西时左面是南右面是北开始面向东时左面是北右面是南和现在面向西时左、右方向不一样", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "similarity_score": 0.9054180383682251, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "topk_results": [{"similarity_score": 0.9054180383682251, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "sample_id": 20665}], "prediction_correct": true}, {"query": "早上起来面向太阳（1）前面是（ ） 后面是（ ） 左面是（ ） 右面是（ ） （2）东与（ ） 相对（ ） 与北相对（3）如果向后转那么左面是（ ） 右面是（ ） 与面向太阳的人的左、右的方向（ ） （选填＂一样＂不一样＂）（1）早上太阳从东方升起当我们面向太阳时前面就是东因为东和西相对所以后面是西再根据“上北下南左西右东”当面向东时左面是北右面是南（2）在方向中东和西是相对的方向南和北是相对的方向（3）开始面向东向后转就变成面向西了此时根据“上北下南左西右东”面向西时左面是南右面是北开始面向东时左面是北右面是南和现在面向西时左、右方向不一样", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "similarity_score": 0.9054180383682251, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "topk_results": [{"similarity_score": 0.9054180383682251, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "sample_id": 20665}], "prediction_correct": false}, {"query": "早上起来面向太阳（1）前面是（ ） 后面是（ ） 左面是（ ） 右面是（ ） （2）东与（ ） 相对（ ） 与北相对（3）如果向后转那么左面是（ ） 右面是（ ） 与面向太阳的人的左、右的方向（ ） （选填＂一样＂不一样＂）（1）早上太阳从东方升起当我们面向太阳时前面就是东因为东和西相对所以后面是西再根据“上北下南左西右东”当面向东时左面是北右面是南（2）在方向中东和西是相对的方向南和北是相对的方向（3）开始面向东向后转就变成面向西了此时根据“上北下南左西右东”面向西时左面是南右面是北开始面向东时左面是北右面是南和现在面向西时左、右方向不一样", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "方向变化推理题（四个方向）", "补充知识点4214"], "similarity_score": 0.9054180383682251, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "topk_results": [{"similarity_score": 0.9054180383682251, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "sample_id": 20665}], "prediction_correct": false}, {"query": "我国有五座名山合称五岳它们分别是东岳泰山、西岳华山、南岳衡山、北岳恒山、中岳嵩山请在第 2 题图中相应的括号里填上它们的名字略", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "根据描述确定位置（四个方向）", "补充知识点4230"], "similarity_score": 0.7676358819007874, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "小数的认识", "根据小数的意义进行单位换算", "带单位的小数比较", "补充知识点320"], "topk_results": [{"similarity_score": 0.7676358819007874, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "小数的认识", "根据小数的意义进行单位换算", "带单位的小数比较", "补充知识点320"], "sample_id": 23795}], "prediction_correct": false}, {"query": "地图通常是按＂上北、下南、左西、右东＂绘制的（1）把这种情况标在右边的方向牌上（2）右图中箭头指的方向与时针转动的方向（ ） （选填＂一样＂＂不一样＂）（3）按右图箭头所指的方向从＂东＂开始依次是东→（ ）→（ ）→（ ）（1）根据题目要求地图通常是按＂上北、下南、左西、右东＂绘制的因此在方向牌上标示为上-北下-南左-西右-东（2）观察右图中箭头指的方向与时针转动的方向进行比较时针转动的方向是顺时针即从上到右到下到左而图中的箭头方向是从上到左到下到右与顺时针方向相反因此答案为“不一样”（3）按右图箭头所指的方向从＂东＂开始依次是东→北→西→南", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "similarity_score": 0.8428211212158203, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "在平面图上辨认东、南、西、北", "补充知识点4228"], "topk_results": [{"similarity_score": 0.8428211212158203, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "在平面图上辨认东、南、西、北", "补充知识点4228"], "sample_id": 27089}], "prediction_correct": false}, {"query": "从 48 里连续减去 6 连续减（ ） 次后得数是 0 要知道从 48 里连续减去 6 减几次后得数是 0 就是看 48 里面有几个 6 二年级上学期学过乘法口诀因为六八四十八也就是 6×8 = 48 所以 48 里面有 8 个 6 那么从 48 里连续减去 6 连续减 8 次后得数是 0 ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "用7~9的乘法口诀求商", "7-9连减与除法"], "similarity_score": 0.809375524520874, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "两位数除以一位数，首位能除尽", "两位数除以一位数(被除数首位能被整除)的应用"], "topk_results": [{"similarity_score": 0.809375524520874, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "两位数除以一位数，首位能除尽", "两位数除以一位数(被除数首位能被整除)的应用"], "sample_id": 42274}], "prediction_correct": false}, {"query": "利嘉超市饮料促销降价后买 6 瓶可乐 36 元钱买 9 瓶要多少钱口答买 9 瓶要 元首先我们来计算每瓶可乐的价格买 6 瓶可乐需要 36 元那么每瓶可乐的价格为 $36\\div6=6$ 元接下来我们计算买 9 瓶可乐需要多少钱每瓶可乐 6 元因此 9 瓶可乐的总价为 $6\\times9=54$ 元所以买 9 瓶可乐需要 54 元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "乘除法的应用题(表内)"], "similarity_score": 0.7187812924385071, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "经济问题", "优惠方案问题(表内除法)", "补充知识点2828"], "topk_results": [{"similarity_score": 0.7187812924385071, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "经济问题", "优惠方案问题(表内除法)", "补充知识点2828"], "sample_id": 30660}], "prediction_correct": false}, {"query": "仔细阅读停车场收费标准回答问题（1） 7 辆小汽车 1 小时收费多少元口答7辆小汽车 1 小时收费 □元（2） 6 辆货车 1 小时收费多少元口答6辆货车 1 小时收费 □元（1）根据收费标准小汽车每小时收费5元因此7辆小汽车1小时的收费为 $7\\times5=35$ 元（2）根据收费标准货车每小时收费8元因此6辆货车1小时的收费为 $6\\times8=48$ 元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "6的乘法口诀及应用", "6的乘法口诀应用题"], "similarity_score": 0.806868314743042, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "时间单位及换算", "经过时间的计算", "24时计时法时间的计算", "24时计算经过的时间"], "topk_results": [{"similarity_score": 0.806868314743042, "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "时间单位及换算", "经过时间的计算", "24时计时法时间的计算", "24时计算经过的时间"], "sample_id": 5556}], "prediction_correct": false}, {"query": "树叶向北摆动说明起的是（ ）A．东风B．南风C．西风D．北风解吹风的方向和树叶摆动的方向是相反的现在树叶向北摆动那就说明风是从南边吹来的从南边吹来的风就叫南风所以答案选B", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "similarity_score": 0.7164976596832275, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "topk_results": [{"similarity_score": 0.7164976596832275, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "sample_id": 21237}], "prediction_correct": true}, {"query": "学校在喜羊羊家的东面喜羊羊放学回家应该（ ） A．从东往西走B．从西往东走C．从北往南走解根据方向的相对性喜羊羊去学校时往东走放学时会往西走选项A正确", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "similarity_score": 0.7486819624900818, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "topk_results": [{"similarity_score": 0.7486819624900818, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "sample_id": 21193}], "prediction_correct": true}, {"query": "小华站在操场上面朝西如果他向右转那么背朝（ ） 解如图原来面向西向右转后面向北与北相对的方向是南", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "方向变化推理题（四个方向）", "补充知识点4214"], "similarity_score": 0.8366855382919312, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东南、西南、东北、西北方向", "相对方向(八个方向)", "补充知识点4238"], "topk_results": [{"similarity_score": 0.8366855382919312, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东南、西南、东北、西北方向", "相对方向(八个方向)", "补充知识点4238"], "sample_id": 34453}], "prediction_correct": false}, {"query": "早晨你面向太阳后边是（ ） 右边是（ ） 解当我们面向太阳时前面就是东那和东相反的方向就是西所以后边是西按照“上北下南左西右东”的方位规则右边就是南", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "similarity_score": 0.8833776116371155, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "topk_results": [{"similarity_score": 0.8833776116371155, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "sample_id": 20659}], "prediction_correct": true}, {"query": "从东这个方向开始按顺时针方向移动依次经过（ ） 、（ ） 、 （ ） 解方向是按照一定顺序排列的从东开始按顺时针方向转动接下来就是南再继续转就是西最后是北所以依次经过南、西、北", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "similarity_score": 0.7804708480834961, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "topk_results": [{"similarity_score": 0.7804708480834961, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "sample_id": 20623}], "prediction_correct": true}, {"query": "太阳总是从（ ） 方升起在（ ） 方落下（ ）A．西东B．南北C．东西略", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "辨认东、西、南、北四个方向", "补充知识点4222"], "similarity_score": 0.8805361986160278, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "topk_results": [{"similarity_score": 0.8805361986160278, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "相对方向(四个方向)", "补充知识点4224"], "sample_id": 21235}], "prediction_correct": false}, {"query": "根据右图填空医院在乐乐家的（ ） 面超市在乐乐家的（ ） 面学校在乐乐家的（ ） 面解以乐乐家为中心医院在乐乐家的左边即西面超市在乐乐家的下面即南面学校在乐乐家的右边即东面", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "在平面图上辨认东、南、西、北", "补充知识点4228"], "similarity_score": 0.8124215006828308, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "在平面图上辨认东、南、西、北", "补充知识点4228"], "topk_results": [{"similarity_score": 0.8124215006828308, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东、南、西、北方向", "在平面图上辨认东、南、西、北", "补充知识点4228"], "sample_id": 27086}], "prediction_correct": true}, {"query": "从公园回家明明先向南走再向东走到家丽丽先向北走再向西走到家芳芳先向北走再向东走到家东东先向西走再向南走到家请你在下图中标出他们各自的家根据题目描述1. 明明先向南走再向东走到家因此明明的家在公园的东南方向即左上角的房子2. 丽丽先向北走再向西走到家因此丽丽的家在公园的西北方向即右上角的房子3. 芳芳先向北走再向东走到家因此芳芳的家在公园的东北方向即右下角的房子4. 东东先向西走再向南走到家因此东东的家在公园的西南方向即左下角的房子", "true_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东南、西南、东北、西北方向", "根据描述确定位置（八个方向）", "补充知识点4234"], "similarity_score": 0.7764624357223511, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东南、西南、东北、西北方向", "八个方向描述简单的行走路线", "补充知识点4244"], "topk_results": [{"similarity_score": 0.7764624357223511, "predicted_label": ["小学数学新知识树", "图形与几何", "位置与方向", "方向", "东南、西南、东北、西北方向", "八个方向描述简单的行走路线", "补充知识点4244"], "sample_id": 12588}], "prediction_correct": false}, {"query": "每只小猫钓 6 条鱼 8 只小猫钓鱼的条数比 50 条（ ） ①多②少③一样多解 $6×8=48$ （条）因为 $48＜50$ 所以8只小猫钓鱼的条数比50条少选②", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "6的乘法口诀及应用", "6的乘法口诀应用题"], "similarity_score": 0.8013277649879456, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "整十数加、减一位数", "加减法(整十数加一位数及相应的减法)解决实际问题"], "topk_results": [{"similarity_score": 0.8013277649879456, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "整十数加、减一位数", "加减法(整十数加一位数及相应的减法)解决实际问题"], "sample_id": 13003}], "prediction_correct": false}, {"query": "二（2）班有 23 名男同学 29 名女同学他们和 2 位老师一起去参加春季研学活动他们乘坐（ ） 号车最合适①限乘客 45 人②限乘客 52 人③限乘客 60 人解二（2）班总人数23+29+2=54（人）45＜54①不合适52＜54②不合适60＞54可以坐下所有人③合适", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "100以内数的连加运算", "连加解决实际问题"], "similarity_score": 0.7602525949478149, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与两位数的进位加法", "笔算两位数加两位数(进位加)解决实际问题"], "topk_results": [{"similarity_score": 0.7602525949478149, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与两位数的进位加法", "笔算两位数加两位数(进位加)解决实际问题"], "sample_id": 15737}], "prediction_correct": false}, {"query": "幼儿园小班有 28 人老师奖励每个小朋友一颗五角星右面这些五角星够吗口答够 不够 （在正确答案的方框里画＂ $\\sqrt{}$ ＂）图片中有 5 行五角星每行有 5 颗共有 $5\\times5=25$ 颗五角星幼儿园小班有 28 人需要 28 颗五角星因为 25 颗五角星少于 28 颗所以这些五角星不够但是题目要求在正确答案的方框里画“√”正确答案应该是“不够”", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "6的乘法口诀及应用", "6的乘法口诀应用题"], "similarity_score": 0.6383254528045654, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "6、7的加减法", "6 、7的加减法的应用"], "topk_results": [{"similarity_score": 0.6383254528045654, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "6、7的加减法", "6 、7的加减法的应用"], "sample_id": 16155}], "prediction_correct": false}, {"query": "商高是最早发现“勾股定理”的人他提出了“勾三股四弦五”的说法如果一个直角三角形的短直角边（勾）长是 3 长直角边（股）长是 4 那么斜边（弦）长一定是 5 也就是勾股弦 $=3$ $45$ 用一根长 72 厘米的铁丝围成一个这样的直角三角形它的三边长度分别是多少勾占周长的比例 $3\\div( 3+4+5 )=\\frac{3}{12}$ 股占周长的比例 $4\\div( 3+4+5 )=\\frac{4}{12}$ 弦占周长的比例 $5\\div( 3+4+5 )=\\frac{5}{12}$ 因为这根铁丝长 72 厘米也就是这个直角三角形的周长是 72 厘米所以勾的长度 $72\\times\\frac{3}{12}=18$ （厘米）股的长度 $72\\times\\frac{4}{12}=24$ （厘米）弦的长度 $72\\times\\frac{5}{12}=30$ （厘米）所以这个直角三角形三边的长度分别是 18 厘米、24 厘米、30 厘米", "true_label": ["小学数学新知识树", "数与代数", "比和比例", "比", "按比分配问题", "已知总量和分量比，求分量", "补充知识点2336"], "similarity_score": 0.6502677798271179, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "三角形", "三角形的面积", "三角形面积的计算", "三角形面积公式逆用"], "topk_results": [{"similarity_score": 0.6502677798271179, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "三角形", "三角形的面积", "三角形面积的计算", "三角形面积公式逆用"], "sample_id": 32977}], "prediction_correct": false}, {"query": "填上合适的单位1枚1元钱硬币重约6（        ）           一袋大米重5（        ） 一只鸭大约重 3（        ）                  一个西红柿大约重 180（        ） 在生活中我们知道比较轻的物体常用克作单位比较重的物体常用千克作单位1枚1元钱硬币很轻所以重约6克一袋大米比较重重5千克一只鸭有一定重量大约重3千克一个西红柿相对较轻大约重180克", "true_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量单位的选择", "填质量单位(克和千克)", "补充知识点2680"], "similarity_score": 0.7882294058799744, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "多位数与一位数的乘除混合运算", "用乘法和除法两步计算解决问题"], "topk_results": [{"similarity_score": 0.7882294058799744, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "多位数与一位数的乘除混合运算", "用乘法和除法两步计算解决问题"], "sample_id": 11830}], "prediction_correct": false}, {"query": "单位换算60千克=（        ）克 1850=（        ）千克（        ）克3000克=（        ）千克 4千克50克=（        ）克因为1千克 = 1000克①60千克=60×1000 = 60000克② - 1000克 = 1千克1850克里面有1个1000克和850克所以1850克 = 1千克850克③ - 把克换算成千克就是看3000里有几个1000用除法计算3000÷1000 = 3千克④ - 先把4千克换算成克4×1000 = 4000克再加上50克4000 + 50 = 4050克", "true_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量单位的换算", "克、千克之间的换算与比较", "千克和克之间的进率及换算"], "similarity_score": 0.8038244843482971, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量单位的换算", "克、千克之间的换算与比较", "千克和克之间的进率及换算"], "topk_results": [{"similarity_score": 0.8038244843482971, "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量单位的换算", "克、千克之间的换算与比较", "千克和克之间的进率及换算"], "sample_id": 40043}], "prediction_correct": true}, {"query": "解决问题（1）妈妈到超市买 2 千克圣女果和 500 克葡萄带 50 元够吗（2）请再提出一个数学问题并解答（1）首先看圣女果的价格是8元/500克2千克等于2000克也就是4个500克所以圣女果的总价是8×4=32（元）葡萄是20元/500克买500克就是20元总共花费32+20=52（元）妈妈带了50元52＞50所以不够（2）提出问题买1千克胡萝卜需要多少钱胡萝卜价格是4元/500克1千克=1000克1000÷500=2所以是2个500克价格为4×2=8元因此买1千克胡萝卜需要8元", "true_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量间的计算和应用", "千克与克的相关应用题", "补充知识点2688"], "similarity_score": 0.719630241394043, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量间的计算和应用", "千克与克的相关应用题", "补充知识点2688"], "topk_results": [{"similarity_score": 0.719630241394043, "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量间的计算和应用", "千克与克的相关应用题", "补充知识点2688"], "sample_id": 40010}], "prediction_correct": true}, {"query": "整十数乘一位数且积是 240 的乘法算式你能写出多少个写一写我们要找整十数乘一位数积是 240 的算式可以先想乘法口诀三八二十四四六二十四当是三八二十四时写成整十数乘一位数的算式就是 30×8 = 240 和 80×3 = 240当是四六二十四时写成整十数乘一位数的算式就是 40×6 = 240 和 60×4 = 240所以一共有 4 个这样的算式", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.6969987750053406, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "三位数与一位数的乘法口算", "几百几十数乘一位数的口算(有进位)"], "topk_results": [{"similarity_score": 0.6969987750053406, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "三位数与一位数的乘法口算", "几百几十数乘一位数的口算(有进位)"], "sample_id": 42064}], "prediction_correct": false}, {"query": "口算6×50+5=                                40×4-10=                             8×8+6=( 374-274 )×3=                        80×9-220=                          200×5+1960=1. 计算6×50 + 5 - 先算乘法6×50 = 300再算加法50+5=                                4002. 计算40×50+5=                                401 - 先算乘法40×50+5=                                402再算减法50+5=                                4033. 计算8×50+5=                                404 - 先算乘法8×50+5=                                405再算加法50+5=                                4064. 计算( 50+5=                                407 )×3 - 先算括号里的减法50+5=                                407 = 100再算乘法100×50+5=                                4095. 计算80×4-10=                             80 - 先算乘法80×4-10=                             81再算减法4-10=                             826. 计算200×4-10=                             83 - 先算乘法200×4-10=                             84再算加法4-10=                             85", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.7995575666427612, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "用两步连乘解决实际问题", "连乘的计算与应用"], "topk_results": [{"similarity_score": 0.7995575666427612, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "用两步连乘解决实际问题", "连乘的计算与应用"], "sample_id": 21457}], "prediction_correct": false}, {"query": "整百数乘一位数且积是 1200 的乘法算式你能写出多少个写一写我们可以先不看积 1200 末尾的两个 0这样就变成了找两个数相乘得 12 的情况因为 2×6 = 12  3×4 = 12 那么在 2 和 6 后面分别添上两个 0 就得到 200×6 = 1200 和 600×2 = 1200 在 3 和 4 后面分别添上两个 0 就得到 300×4 = 1200 和 400×3 = 1200 所以能写出 4 个这样的乘法算式", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.7109954953193665, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "两位数乘整百数的口算及应用"], "topk_results": [{"similarity_score": 0.7109954953193665, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "两位数乘整百数的口算及应用"], "sample_id": 42751}], "prediction_correct": false}, {"query": "甲、乙两地相距 320 千米一辆货车每小时行驶 76 千米已知甲、乙两地相距320千米货车每小时行驶76千米先计算货车4小时行驶的路程 $76\\times4=304$ （千米）因为304千米小于320千米所以4小时不能到达", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "两、三位数与一位数连续进位的乘法", "解决两、三位数乘一位数(连续进位)的实际问题"], "similarity_score": 0.7601320147514343, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "整十、整百、整千数除以一位数的除法", "整十、整百、整千数除以一位数的实际问题"], "topk_results": [{"similarity_score": 0.7601320147514343, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "整十、整百、整千数除以一位数的除法", "整十、整百、整千数除以一位数的实际问题"], "sample_id": 35510}], "prediction_correct": false}, {"query": "妈妈买饼干 2 盒需付 21 元 4 盒需付多少钱先看4盒是2盒的几倍4÷2 = 2倍因为2盒需付21元那么4盒要付的钱就是2盒的2倍所以4盒需付21×2 = 42元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "多位数与一位数的乘除混合运算", "用乘法和除法两步计算解决问题"], "similarity_score": 0.7132039070129395, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "经济问题", "经济问题(表内除法)", "补充知识点2826"], "topk_results": [{"similarity_score": 0.7132039070129395, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "经济问题", "经济问题(表内除法)", "补充知识点2826"], "sample_id": 26772}], "prediction_correct": false}, {"query": "丽丽看一本书如果每天看 30 页 8 天可以看完如果每天看 40 页几天可以看完首先根据每天看30页8天可以看完能算出这本书的总页数为30×8 = 240页然后用总页数除以每天看40页即240÷40 = 6天所以6天可以看完", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "多位数与一位数的乘除混合运算", "用乘法和除法两步计算解决问题"], "similarity_score": 0.7718051671981812, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(7、8、9)"], "topk_results": [{"similarity_score": 0.7718051671981812, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(7、8、9)"], "sample_id": 32093}], "prediction_correct": false}, {"query": "口算 $200\\times2=$ $70\\times3=$ $80\\times4=$ $500\\times6=$ $202\\times2=$ $27\\div3=$ $200+2=$ $0+300=$ $500\\times5=$ $300-30=$ $800\\times4=$ $300\\times0=$ 1. 计算 $200\\times2$  - 把200看成2个百2个百乘2是4个百也就是4002. 计算 $70\\times3$  - 把70看成7个十7个十乘3是21个十即2103. 计算 $80\\times4$  - 把80看成8个十8个十乘4是32个十也就是3204. 计算 $500\\times6$  - 把500看成5个百5个百乘6是30个百即30005. 计算 $202\\times2$  - 先算 $200\\times2=400$ 再算 $2\\times2=4$ 最后 $400+4=404$ 6. 计算 $27\\div3$  - 根据乘法口诀“三九二十七”可得 $27\\div3=9$ 7. 计算 $200+2$  - 200和2相加结果是2028. 计算 $0+300$  - 0加任何数都得原数所以 $0+300=300$ 9. 计算 $500\\times5$  - 把500看成5个百5个百乘5是25个百即250010. 计算 $300-30$  - 300减30结果是27011. 计算 $800\\times4$  - 把800看成8个百8个百乘4是32个百也就是320012. 计算 $300\\times0$  - 任何数乘0都得0所以 $300\\times200+22$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.8101792931556702, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "两位数乘整十（百）数的口算"], "topk_results": [{"similarity_score": 0.8101792931556702, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "两位数乘整十（百）数的口算"], "sample_id": 21455}], "prediction_correct": false}, {"query": "一本日记本 7 元买 202 本大约需要（        ） 元在估算买 $202$ 本日记本大约需要多少钱时因为 $202$ 接近 $200$ 所以把 $202$ 看作 $200$ 又已知一本日记本 $7$ 元那么 $200$ 本需要的钱数就是 $200\\times7=1400$ 元所以买 $202$ 本大约需要 $1400$ 元", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘一位数的估算", "两、三位数乘一位数的估算应用"], "similarity_score": 0.6591843962669373, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "两位数除以一位数", "两位数除以一位数的口算除法的实际问题"], "topk_results": [{"similarity_score": 0.6591843962669373, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "两位数除以一位数", "两位数除以一位数的口算除法的实际问题"], "sample_id": 35834}], "prediction_correct": false}, {"query": "李师傅 3 天加工了 24 个零件（1）一星期（ 5 个工作日）可以加工多少个零件（2）加工 56 个零件需要多少个工作日（1）先算出李师傅一天加工零件的个数用 3 天加工的零件总数除以 3 天即 24÷3 = 8 个一星期有 5 个工作日那么 5 天能加工的零件数就是一天加工的个数乘以 5 天8×5 = 40 个（2）已经知道李师傅一天加工 8 个零件要加工 56 个零件就用要加工的零件总数除以一天加工的个数56÷8 = 7 个工作日", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "多位数与一位数的乘除混合运算", "用乘法和除法两步计算解决问题"], "similarity_score": 0.8449215888977051, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "多位数与一位数的乘除混合运算", "用乘法和除法两步计算解决问题"], "topk_results": [{"similarity_score": 0.8449215888977051, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "多位数与一位数的乘除混合运算", "用乘法和除法两步计算解决问题"], "sample_id": 11818}], "prediction_correct": true}, {"query": "", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的除法", "多位数与一位数的乘除混合运算", "用乘法和除法两步计算解决问题"], "similarity_score": 0.3137668967247009, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "整十数加、减一位数", "加减法(整十数加一位数及相应的减法)解决实际问题"], "topk_results": [{"similarity_score": 0.3137668967247009, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "整十数加、减一位数", "加减法(整十数加一位数及相应的减法)解决实际问题"], "sample_id": 12977}], "prediction_correct": false}, {"query": "某地气象台发布暴雨红色预警3小时内该地降雨量将达到 110（        ） A．毫米B．厘米C．分米我们在生活中测量降雨量一般用毫米作单位1 厘米 = 10 毫米1 分米 = 10 厘米 = 100 毫米如果降雨量是 110 厘米或者 110 分米那降雨量就太大啦不符合实际情况所以 3 小时内该地降雨量将达到 110 毫米选 A", "true_label": ["小学数学新知识树", "图形与几何", "测量", "常见的长度单位及换算", "长度单位的选择", "选择合适的长度单位(分米、毫米)", "补充知识点4351"], "similarity_score": 0.7415425777435303, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "测量", "常见的长度单位及换算", "长度单位的选择", "选择合适的长度单位(分米、毫米)", "补充知识点4351"], "topk_results": [{"similarity_score": 0.7415425777435303, "predicted_label": ["小学数学新知识树", "图形与几何", "测量", "常见的长度单位及换算", "长度单位的选择", "选择合适的长度单位(分米、毫米)", "补充知识点4351"], "sample_id": 11519}], "prediction_correct": true}, {"query": "画一条 2 厘米 8 毫米的线段三年级我们学过测量线段长度1 厘米等于 10 毫米2 厘米 8 毫米就是 28 毫米我们可以先准备好直尺将直尺的 0 刻度线与纸的一端对齐然后在直尺 2 厘米 8 毫米也就是 28 毫米的刻度处点一个点最后连接纸一端和这个点就画出了 2 厘米 8 毫米的线段", "true_label": ["小学数学新知识树", "图形与几何", "测量", "常见的长度单位及换算", "长度及长度的常用单位", "毫米和分米的认识", "认识毫米"], "similarity_score": 0.711819052696228, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "直线、射线、线段", "根据长度画线段", "画线段、直线和射线", "补充知识点3105"], "topk_results": [{"similarity_score": 0.711819052696228, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "直线、射线、线段", "根据长度画线段", "画线段、直线和射线", "补充知识点3105"], "sample_id": 14080}], "prediction_correct": false}, {"query": "画一条 32 毫米的线段我们可以使用直尺来画线段先把直尺平放在纸上找到直尺上的刻度 0然后在刻度 0 的地方点一个点作为线段的一个端点接着沿着直尺找到刻度 32 毫米的地方再点一个点作为线段的另一个端点最后用直线把这两个端点连接起来这样就画出了一条 32 毫米的线段", "true_label": ["小学数学新知识树", "图形与几何", "测量", "常见的长度单位及换算", "长度及长度的常用单位", "毫米和分米的认识", "认识毫米"], "similarity_score": 0.7337861657142639, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "直线、射线、线段", "根据长度画线段", "画线段、直线和射线", "补充知识点3105"], "topk_results": [{"similarity_score": 0.7337861657142639, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "直线、射线、线段", "根据长度画线段", "画线段、直线和射线", "补充知识点3105"], "sample_id": 14080}], "prediction_correct": false}, {"query": "下图中点 $M$ 表示的数大约是算式（        ） 的积A． $41\\times9$ B． $59\\times8$ C． $69\\times9$", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘一位数的估算", "两、三位数乘一位数的估算"], "similarity_score": 0.9339401721954346, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "两位数与一位数的乘法口算", "两位数乘一位数的口算(不进位)"], "topk_results": [{"similarity_score": 0.9339401721954346, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "两位数与一位数的乘法口算", "两位数乘一位数的口算(不进位)"], "sample_id": 12476}], "prediction_correct": false}, {"query": "50+50+50=（        ）×（        ）=（        ） 加法是把几个数合并成一个数的运算乘法是求几个相同加数和的简便运算这里是3个50相加写成乘法算式就是50×350×3 = 150", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.7621063590049744, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "表内乘加、乘减", "2-5的乘加、乘减计算"], "topk_results": [{"similarity_score": 0.7621063590049744, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "表内乘加、乘减", "2-5的乘加、乘减计算"], "sample_id": 43763}], "prediction_correct": false}, {"query": "我会算 $98-8\\times4$ $72\\div( 86-78 )$ $48\\div8\\div2$ $9\\times4+2$ 1. 计算 $98-8×4$ 根据先乘除后加减的运算顺序先算乘法 $8×4=32$ 再算减法 $98-32=66$ 2. 计算 $72÷( 86-78 )$ 有括号先算括号里的 $86-78=8$ 再算除法 $72÷8=9$ 3. 计算 $48÷8÷2$ 按照从左到右的顺序依次计算先算 $48÷98-80$ 再算 $6÷98-81$ 4. 计算 $9×4+2$ 先算乘法 $9×98-83$ 再算加法 $36+98-818$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "similarity_score": 0.7305374145507812, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "topk_results": [{"similarity_score": 0.7305374145507812, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "sample_id": 34975}], "prediction_correct": false}, {"query": "张师傅做零件每天做 6 个 6 天完成如果每天做 9 个几天可以完成本题可先根据“每天做 $6$ 个 $6$ 天完成”求出零件的总数再用零件总数除以每天做 $9$ 个即可求出完成天数已知张师傅每天做 $6$ 个零件 $6$ 天完成根据“工作总量 $=$ 工作效率 $\\times$ 工作时间”可得零件总数为 $6\\times6=36$ （个）现在每天做 $9$ 个根据“工作时间 $=$ 工作总量÷工作效率”可得需要的天数为 $36\\div9=4$ （天）综上每天做 $9$ 个时 $4$ 天可以完成", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.6961263418197632, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "反比例的意义及辨识", "反比例的意义", "补充知识点2457"], "topk_results": [{"similarity_score": 0.6961263418197632, "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "反比例的意义及辨识", "反比例的意义", "补充知识点2457"], "sample_id": 9366}], "prediction_correct": false}, {"query": "张明看一本 180 页的科技书每天看 29 页连续看了 6 天估一估他能看完这本书吗请列式说明理由本题可先对张明 6 天看的页数进行估算再与这本书的总页数比较大小估算时把每天看的页数 29 看成与它接近的整十数 30然后用 30 乘以看的天数 6得到 $30×6=180$ 页因为是把 29 估大了才得到 180 页所以实际 6 天看的页数小于 180 页而这本书有 180 页所以他不能看完这本书", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘一位数的估算", "两、三位数乘一位数的估算应用"], "similarity_score": 0.754417359828949, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘两位数的进位乘法", "两位数乘两位数的笔算(有进位)的实际问题"], "topk_results": [{"similarity_score": 0.754417359828949, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘两位数的进位乘法", "两位数乘两位数的笔算(有进位)的实际问题"], "sample_id": 4406}], "prediction_correct": false}, {"query": "一根彩带长 2 米 4 分米对折 3 次后平均每段长（        ） 分米A． 3B． 4C． 8本题可先将彩带的长度单位统一换算成分米再分析对折 $3$ 次后彩带被平均分成的段数最后用彩带总长度除以段数即可求出平均每段的长度步骤一将彩带长度的单位统一换算成分米因为 $1$ 米 $=10$ 分米所以 $2$ 米换算成分米为 $2\\times10=20$ （分米）那么 $2$ 米 $4$ 分米换算成分米为 $20+4=24$ （分米）步骤二分析对折 $3$ 次后彩带被平均分成的段数对折 $1$ 次彩带被平均分成 $2$ 段对折 $2$ 次彩带被平均分成 $2\\times2=4$ 段对折 $3$ 次彩带被平均分成 $2\\times2\\times2=8$ 段步骤三计算平均每段的长度已知彩带总长度为 $24$ 分米且被平均分成了 $8$ 段根据“每段长度 $=$ 总长度 $\\div$ 段数”可得平均每段长 $24\\div8=3$ （分米）综上答案选A", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "整数平均分的意义及应用", "平均分的综合理解与应用"], "similarity_score": 0.8886960744857788, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数与除法的关系", "分数与除法关系的应用-"], "topk_results": [{"similarity_score": 0.8886960744857788, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数与除法的关系", "分数与除法关系的应用-"], "sample_id": 21843}], "prediction_correct": false}, {"query": "如图 5 盒月饼叠起来高 3 分米每盒月饼高多少厘米题目中给出5盒月饼叠起来的高度是3分米首先将单位统一3分米等于30厘米然后用总高度除以盒子的数量30 ÷ 5 = 6（厘米）所以每盒月饼高6厘米", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "除法的初步认识", "包含分求份数（列除法算式）"], "similarity_score": 0.657372236251831, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数加减法解决实际问题", "运用一位小数加法解决问题"], "topk_results": [{"similarity_score": 0.657372236251831, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数加减法解决实际问题", "运用一位小数加法解决问题"], "sample_id": 5207}], "prediction_correct": false}, {"query": "如图有一座承重为 1 吨的桥下面哪几只动物可以一起通过（答案有多种至少写出两种）1吨=1000kg我们先看每只动物的体重大象500kg小牛120kg狮子240kg熊180kg马200kg要求总重量不超过1000kg找出可以一起通过的组合（1）大象和小牛500+120=620kg＜1000kg可以（2）狮子和熊240+180=420kg＜1000kg可以（3）狮子和马240+200=440kg＜1000kg可以（4）熊和马180+200=380kg＜1000kg可以（5）小牛、狮子和熊120+240+180=540kg＜1000kg可以（6）小牛、狮子和马120+240+200=560kg＜1000kg可以（7）小牛、熊和马120+180+200=500kg＜1000kg可以（8）狮子、熊和马240+180+200=620kg＜1000kg可以（9）小牛、狮子、熊和马120+240+180+200=740kg＜1000kg可以注意不能包含大象和另外两只或以上动物因为即使是最轻的三只动物加大象也超过1000kg例如大象+小牛+狮子=500+120=6200kg还可以但大象+小牛+狮子+熊=500+120=6201kg＞1000kg不行所以满足条件的组合有多种至少写出两种即可", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数的加法和减法", "几百几十数、整百数的加减法口算", "整百、整千数、几百几十数的加减法"], "similarity_score": 0.847923994064331, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加减法应用题"], "topk_results": [{"similarity_score": 0.847923994064331, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加减法应用题"], "sample_id": 18760}], "prediction_correct": false}, {"query": "想一想写一写（1）（2）（1）观察图片1左边有4把伞右边有2把伞将左右两边的伞合起来总数是4+2=6从总数6中去掉左边的4把剩下右边的2把即6-4=2也可以用右边的2把加上左边的4把得到2+4=6从总数6中去掉右边的2把剩下左边的4把即6-2=4（2）观察图片2左边有3只兔子右边有4只兔子将左右两边的兔子合起来总数是3+4=7从总数7中去掉左边的3只剩下右边的4只即7-3=4也可以用右边的4只加上左边的3只得到4+3=7从总数7中去掉右边的4只剩下左边的3只即7-4=3", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "6、7的加减法", "6 、7的加减法的应用"], "similarity_score": 0.840180516242981, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与一位数的不进位加法", "口算两位数加一位数(不进位)的应用"], "topk_results": [{"similarity_score": 0.840180516242981, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与一位数的不进位加法", "口算两位数加一位数(不进位)的应用"], "sample_id": 39831}], "prediction_correct": false}, {"query": "拨一拔填一填（1）如果再加 7 颗就有（        ） 颗（2）如果要出现 9 颗就要再加（        ） 颗（1）观察图片1可以看到已经有3颗[图片2]如果再加7颗就是3+7=10颗（2）要出现9颗[图片4]现在有4颗所以需要再加9-4=5颗", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "8、9的分与合", "8、9的分合的应用"], "similarity_score": 0.6830634474754333, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "万以内数的认识", "1000以内数的读、写法", "计数器表示数(1000以内)"], "topk_results": [{"similarity_score": 0.6830634474754333, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "万以内数的认识", "1000以内数的读、写法", "计数器表示数(1000以内)"], "sample_id": 40540}], "prediction_correct": false}, {"query": "找朋友连一连观察图片左边的小女孩说数字8表示要找出加起来等于8的两个数图中数字有1、3、4、7、4、6、5、2我们来找出哪些两个数相加等于81 + 7 = 82 + 6 = 83 + 5 = 84 + 4 = 8所以配对为1和72和63和54和4", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "8、9的分与合", "8、9的分合的应用"], "similarity_score": 0.6999952793121338, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "8、9的分与合", "8、9的分合的应用"], "topk_results": [{"similarity_score": 0.6999952793121338, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "8、9的分与合", "8、9的分合的应用"], "sample_id": 16149}], "prediction_correct": true}, {"query": "填一填3+=6            -4=3            6-2= +27- =2           +7=7           7-=5-1. 3+[图片1]=6因为3加3等于6所以[图片1]是32. [图片2]-4=3因为4加3等于7所以[图片2]是73. 6-2=[图片3]+26减2等于44减2等于2所以[图片3]是24. 7-[图片4]=2因为7减5等于2所以[图片4]是55. [图片5]+7=7因为0加7等于7所以[图片5]是06. 7-[图片6]=5-[图片7]移项得[图片6]-[图片7]=2因此[图片6]比[图片7]大2在一年级范围内取最小值设[图片7]=0则[图片6]=2验证7-2=55-0=5成立所以[图片6]=2[图片7]=0综上答案为3,7,2,5,0,2,0", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "6、7的加减法", "6 、7的加减法的应用"], "similarity_score": 0.7655397653579712, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "算式谜，数阵，进位制", "竖式数字谜", "加法竖式谜（100以内）", "补充知识点5013", "补充知识点5014"], "topk_results": [{"similarity_score": 0.7655397653579712, "predicted_label": ["小学数学新知识树", "数学竞赛", "算式谜，数阵，进位制", "竖式数字谜", "加法竖式谜（100以内）", "补充知识点5013", "补充知识点5014"], "sample_id": 15748}], "prediction_correct": false}, {"query": "画一画填一填（1） $\\triangle\\triangle\\triangle\\triangle$ ___ $\n4+\\square=10\\quad10-4=\\square\\quad10-\\square=\\square\n$ （2） $\\triangle\\triangle\\triangle\\triangle\\triangle\\triangle\\triangle$ ___ $\n7+\\square=10\\quad10-7=\\square\\quad10-\\square=\\square\n$ （1）已知有4个 $\\triangle$ 要使总数为10个 $\\triangle$ 还需要画6个 $\\triangle$  $4+6=10$  $10-4=6$  $10-6=4$ （2）已知有7个 $\\triangle$ 要使总数为10个 $\\triangle$ 还需要画3个 $\\triangle$  $7+3=10$  $10-7=3$  $10-3=7$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "10的加减法", "10的加减法的应用"], "similarity_score": 0.7626092433929443, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "10的分与合", "10的分与合"], "topk_results": [{"similarity_score": 0.7626092433929443, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "10的分与合", "10的分与合"], "sample_id": 16224}], "prediction_correct": false}, {"query": "在里填上“>”“”9-7=22小于5所以填“”2+8=1010大于8所以填“>”5+5909小于,10所以填“<”", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "10的加减法", "10的加减法的应用"], "similarity_score": 0.7413425445556641, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与一位数的退位减法", "两位数减一位数（退位）的应用"], "topk_results": [{"similarity_score": 0.7413425445556641, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与一位数的退位减法", "两位数减一位数（退位）的应用"], "sample_id": 15909}], "prediction_correct": false}, {"query": "比一比（1）有（        ）只有（        ）只（2）比少（        ）只（3）比多(        )只（1）观察图片第一行是海星共有7只第二行是鱼共有10只（2）比较海星和鱼的数量鱼比海星多用减法计算10 - 7 = 3所以海星比鱼少3只（3）鱼比海星多用减法计算10 - 7 = 3所以鱼比海星多3只", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "6、7的加减法", "6 、7的加减法的应用"], "similarity_score": 0.7571995854377747, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "20以内数的认识", "10以内数的比大小", "10以内数的比大小"], "topk_results": [{"similarity_score": 0.7571995854377747, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "20以内数的认识", "10以内数的比大小", "10以内数的比大小"], "sample_id": 16121}], "prediction_correct": false}, {"query": "用两个三角尺拼角拼成的角是钝角的是（ ） A.    B.    C.    D.观察每个选项中两个三角尺拼成的角选项A两个三角尺的60°角和90°角拼在一起形成的角大于90°是钝角选项B两个三角尺的30°角和45°角拼在一起形成的角小于90°是锐角选项C两个三角尺的45°角拼在一起形成的角等于90°是直角选项D两个三角尺的30°角拼在一起形成的角小于90°是锐角因此只有选项A拼成的角是钝角", "true_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "用三角尺画角", "三角尺拼角", "补充知识点3201"], "similarity_score": 0.8754167556762695, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "直角、钝角、锐角的认识及特征", "认识直角、锐角、钝角", "补充知识点3153"], "topk_results": [{"similarity_score": 0.8754167556762695, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "直角、钝角、锐角的认识及特征", "认识直角、锐角、钝角", "补充知识点3153"], "sample_id": 43280}], "prediction_correct": false}, {"query": "你会数线段吗像这样连一连数一数你发现了什么\n| 图形 |   |   |   |   | 端点数 | 2 | 3 |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 图形 |   |   |   |   |\n| 端点数 | 2 | 3 |   |   |\n| 线段数 | 1 |   |   |   |\n我们先来看线段数和端点数的关系当有2个端点时线段数是1条当有3个端点时我们可以从第一个端点出发和后面2个端点分别连成线段有2条从第二个端点出发和第三个端点连成1条线段所以一共是2 + 1 = 3条线段当有4个端点时从第一个端点出发能和后面3个端点连成3条线段从第二个端点出发能和后面2个端点连成2条线段从第三个端点出发能和第四个端点连成1条线段总共就是3 + 2 + 1 = 6条线段当有5个端点时从第一个端点出发能和后面4个端点连成4条线段从第二个端点出发能和后面3个端点连成3条线段从第三个端点出发能和后面2个端点连成2条线段从第四个端点出发能和第五个端点连成1条线段总共就是4 + 3 + 2 + 1 = 10条线段", "true_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "similarity_score": 0.8169448375701904, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "topk_results": [{"similarity_score": 0.8169448375701904, "predicted_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "sample_id": 21282}], "prediction_correct": true}, {"query": "空白部分是长方形的 $\\frac{( \\quad )}{( \\quad )}$ 阴影部分是长方形的 $\\frac{( \\quad )}{( \\quad )}$ 观察图形整个长方形被平均分成了8个相等的三角形空白部分由3个三角形组成所以空白部分是长方形的 $\\frac{3}{8}$ 阴影部分由5个三角形组成所以阴影部分是长方形的 $\\frac{5}{8}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的初步认识", "认识几分之几"], "similarity_score": 0.8277032375335693, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的意义", "分数的意义"], "topk_results": [{"similarity_score": 0.8277032375335693, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的意义", "分数的意义"], "sample_id": 21727}], "prediction_correct": false}, {"query": "阴影部分表示（        ）个是（        ）观察图片长方形被平均分成了3份其中阴影部分占了2份所以阴影部分表示2个 $\\frac{1}{3}$ 即 $\\frac{2}{3}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的初步认识", "认识几分之几"], "similarity_score": 0.847476065158844, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "真分数、假分数、带分数的认识", "真假带分数认识综合-", "补充知识点484"], "topk_results": [{"similarity_score": 0.847476065158844, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "真分数、假分数、带分数的认识", "真假带分数认识综合-", "补充知识点484"], "sample_id": 31572}], "prediction_correct": false}, {"query": "用分数表示下面的涂色部分（1）第一个图形是一个被平均分成9个小正方形的大正方形其中涂色部分有4个所以涂色部分占整体的 $\\frac{4}{9}$ （2）第二个图形是一个被平均分成3个三角形的大三角形其中涂色部分有2个所以涂色部分占整体的 $\\frac{2}{3}$ （3）第三个图形是一个被平均分成4个长方形的大长方形其中涂色部分有3个所以涂色部分占整体的 $\\frac{3}{4}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的初步认识", "认识几分之几"], "similarity_score": 0.9225917458534241, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的意义", "分数的意义"], "topk_results": [{"similarity_score": 0.9225917458534241, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的意义", "分数的意义"], "sample_id": 21649}], "prediction_correct": false}, {"query": "下图中有（ ） 条直线（ ） 条线段（ ） 条射线线段 $AC$ 长 （ ） 毫米线段 $BD$ 长（ ） 毫米观察图形图中只有一条直线即经过点A、B、C、D的直线线段是两个端点之间的部分图中有以下线段AB、AC、AD、BC、BD、CD共6条射线有一个端点向一个方向无限延伸以每个点为端点可以向左或向右各形成一条射线共有4个点每个点对应2条射线共8条射线通过测量得线段AC长度为30毫米线段BD长度为50毫米", "true_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "similarity_score": 0.8758901357650757, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "topk_results": [{"similarity_score": 0.8758901357650757, "predicted_label": ["小学数学新知识树", "数学竞赛", "计数", "平面图形计数", "数线段、直线和射线", "补充知识点5253", "补充知识点5254"], "sample_id": 21285}], "prediction_correct": true}, {"query": "$\\frac{4}{5}$ 里有（        ） 个 $\\frac{1}{5}$ 再添上 1 个 $\\frac{1}{5}$ 是（        ）1里面有（        ）个（        ）个（        ）个 $\\frac{4}{5}$ 里有 4 个 $\\frac{1}{5}$ 因为 $\\frac{4}{5}=\\frac{1}{5}+\\frac{1}{5}+\\frac{1}{5}+\\frac{1}{5}$ 再添上 1 个 $\\frac{1}{5}$ 就是 $\\frac{4}{5}+\\frac{1}{5}=\\frac{5}{5}=1$ 所以是 1因为 $1=\\frac{3}{3}$ 所以有 3 个 $\\frac{1}{3}$ 因为 $1=\\frac{4}{4}$ 所以有 4 个 $\\frac{1}{4}$ 因为 $1=\\frac{8}{8}$ 所以有 8 个 $\\frac{1}{8}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的初步认识", "认识几分之几"], "similarity_score": 0.8390544652938843, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数单位的认识与确定", "分数单位的意义"], "topk_results": [{"similarity_score": 0.8390544652938843, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数单位的认识与确定", "分数单位的意义"], "sample_id": 24527}], "prediction_correct": false}, {"query": "小妞和大妞吃西瓜①她们一共吃了这个西瓜的几分之几②大妞比小妞多吃了这个西瓜的几分之几③这个西瓜还剩几分之几①小妞吃了西瓜的 $\\frac{2}{9}$ 大妞吃了西瓜的 $\\frac{5}{9}$ 她们一共吃了 $\\frac{2}{9}+\\frac{5}{9}=\\frac{7}{9}$ ②大妞比小妞多吃了 $\\frac{5}{9}-\\frac{2}{9}=\\frac{3}{9}$ ③整个西瓜是1也就是 $\\frac{9}{9}$ 还剩 $1-\\frac{7}{9}=\\frac{9}{9}-\\frac{7}{9}=\\frac{2}{9}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数的加、减法", "分母在10以内分数的简单应用", "同分母分数加、减法的应用"], "similarity_score": 0.7521511316299438, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数的加、减法", "异分母分数加、减法的应用", "异分母分数连减在实际生活中的应用-"], "topk_results": [{"similarity_score": 0.7521511316299438, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数的加、减法", "异分母分数加、减法的应用", "异分母分数连减在实际生活中的应用-"], "sample_id": 590}], "prediction_correct": false}, {"query": "涂色部分占总数的几分之几                   （1）观察图片共有8个五角星其中涂色的有2个所以涂色部分占总数的 $\\frac{2}{8}$ （2）观察图片共有6个三角形其中涂色的有1个所以涂色部分占总数的 $\\frac{1}{6}$ （3）观察图片共有8个菱形其中涂色的有4个所以涂色部分占总数的 $\\frac{4}{8}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的初步认识", "认识几分之几"], "similarity_score": 0.8518039584159851, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "利用平移巧算周长与面积", "用平移解决面积问题", "补充知识点3562", "补充知识点3563"], "topk_results": [{"similarity_score": 0.8518039584159851, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "利用平移巧算周长与面积", "用平移解决面积问题", "补充知识点3562", "补充知识点3563"], "sample_id": 17617}], "prediction_correct": false}, {"query": "算一算绳长 30 米第二次剪去（        ） 米第一次剪去5米绳子还剩30-5=25米第二次剪去余下的 $\\frac{2}{5}$ 也就是把余下的25米平均分成5份取其中的2份计算是25÷5×2=10米所以第二次剪去10米", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "similarity_score": 0.7803281545639038, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "2、3、4的乘法口诀及应用-", "2-4乘法口诀的应用题-"], "topk_results": [{"similarity_score": 0.7803281545639038, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内乘法", "2、3、4的乘法口诀及应用-", "2-4乘法口诀的应用题-"], "sample_id": 43540}], "prediction_correct": false}, {"query": "1、前置信息冰箱贴作为一种文化符号承载着丰富的历史文化信息人们通过分享自己拥有的冰箱贴展示自己的旅行经历厦门某文创店购进＂中山路＂冰箱贴 100 个卖了 $\\frac{4}{5}$ 刚好卖出的数量是＂鼓浪屿＂冰箱贴进货数的 $\\frac{1}{6}$ 2、请回答（1）＂中山路＂冰箱贴卖了多少个把“中山路”冰箱贴100个看做单位“1”卖了 $\\frac{4}{5}$ 也就是把100平均分成5份每份100÷5=20个取其中的4份20×4=80个所以“中山路”冰箱贴卖了80个", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "similarity_score": 0.7398189902305603, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "100以内数的加减混合运算", "分步解决问题的策略"], "topk_results": [{"similarity_score": 0.7398189902305603, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "100以内数的加减混合运算", "分步解决问题的策略"], "sample_id": 10336}], "prediction_correct": false}, {"query": "明明和丽丽谁读的页数多英英是这样解答的因为 $\\frac{1}{7}>\\frac{1}{9}$ 所以明明读的页数多你同意英英的解答吗请写出理由（1）明明读的页数56页的 $\\frac{1}{7}$ 是 $56\\div7=8$ 页（2）丽丽读的页数81页的 $\\frac{1}{9}$ 是 $81\\div9=9$ 页（3）比较两人读的页数8页 < 9页所以丽丽读的页数多英英只比较了分数的大小没有考虑书的总页数不同因此她的解答是错误的", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "similarity_score": 0.713803231716156, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(7、8、9)"], "topk_results": [{"similarity_score": 0.713803231716156, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(7、8、9)"], "sample_id": 32016}], "prediction_correct": false}, {"query": "图中阴影部分占整个图形的几分之几填在括号里（1）观察第一个图形长方形内有两个相同的圆每个圆被分成4等份阴影部分为每个圆的1份共2份两个圆共有8份阴影占2份因此阴影部分占整个图形的 $\\frac{2}{8}=\\frac{1}{4}$ （2）第二个图形是一个长方形内部有一个三角形三角形的底边与长方形的底边相等高也与长方形的高相等根据三角形面积公式三角形面积是长方形面积的一半所以阴影部分占整个图形的 $\\frac{1}{2}$ （3）第三个图形一共有20个小正方形阴影部分包括4个完整的小正方形和4个三角形每个三角形是小正方形的一半因此阴影部分共6个小正方形占整个图形的 $\\frac{6}{20}=\\frac{3}{10}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "分数的认识", "分数的意义、读写及分类", "分数的初步认识", "认识几分之几"], "similarity_score": 0.8905804753303528, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "利用平移巧算周长与面积", "用平移解决面积问题", "补充知识点3562", "补充知识点3563"], "topk_results": [{"similarity_score": 0.8905804753303528, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "利用平移巧算周长与面积", "用平移解决面积问题", "补充知识点3562", "补充知识点3563"], "sample_id": 17601}], "prediction_correct": false}, {"query": "看图填一填比一比第一幅图中左边部分表示 $\\frac{2}{7}$ 右边部分表示 $\\frac{3}{7}$ 两部分合起来是 $\\frac{2}{7}+\\frac{3}{7}=\\frac{5}{7}$ 第二幅图中整体是 $\\frac{5}{7}$ 其中右边部分是 $\\frac{3}{7}$ 所以左边部分为 $\\frac{5}{7}-\\frac{3}{7}=\\frac{2}{7}$ 因此填空如下（ $\\frac{2}{7}$ ）+（ $\\frac{3}{7}$ ）=（ $\\frac{5}{7}$ ）（ $\\frac{5}{7}$ ）-（ $\\frac{3}{7}$ ）=（ $\\frac{2}{7}$ ）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数的加、减法", "分母在10以内分数的简单应用", "同分母分数加、减法的应用"], "similarity_score": 0.806975781917572, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数的加、减法", "同分母分数加、减法", "同分母分数减法的计算、运用-"], "topk_results": [{"similarity_score": 0.806975781917572, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数的加、减法", "同分母分数加、减法", "同分母分数减法的计算、运用-"], "sample_id": 17540}], "prediction_correct": false}, {"query": "估算 $497\\times4=$ 时可以把 497 看成（ ） 积约是（ ） 在估算乘法时为了方便计算我们会把一个乘数看成和它接近的整十、整百数497 接近 500所以把 497 看成 500那么 500×4 = 2000所以积约是 2000", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘一位数的估算", "两、三位数乘一位数的估算"], "similarity_score": 0.7747619152069092, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "topk_results": [{"similarity_score": 0.7747619152069092, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "sample_id": 38269}], "prediction_correct": false}, {"query": "学校开展＂阳光体育快乐足球＂嘉年华比赛活动三年级有 5 个班如果每两个班踢一场一共需要踢多少场请借助表格帮助体育老师写出比赛对阵表你发现了什么（借助表格在需要对决赛中画＂√＂）\n| 班级 | 三（1） | 三（2） | 三（3） | 三（4） | 三（5） | 三（1） |   |   |   |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 班级 | 三（1） | 三（2） | 三（3） | 三（4） | 三（5） |\n| 三（1） |   |   |   |   |   |\n| 三（2） |   |   |   |   |   |\n| 三（3） |   |   |   |   |   |\n| 三（4） |   |   |   |   |   |\n| 三（5） |   |   |   |   |   |\n我们来一个一个分析每个班要踢的场数三（1）班要和三（2）班、三（3）班、三（4）班、三（5）班各踢一场一共踢 4 场三（2）班已经和三（1）班踢过了所以它只要再和三（3）班、三（4）班、三（5）班踢踢 3 场三（3）班已经和三（1）班、三（2）班踢过了它再和三（4）班、三（5）班踢踢 2 场三（4）班已经和三（1）班、三（2）班、三（3）班踢过了它再和三（5）班踢 1 场三（5）班前面都和其他班踢过了把这些场数加起来 $4+3+2+1=10$ 场从这里我们能发现规律如果有 n 个班比赛总场数就是 $( n-1 )+( n-2 )+…+1$ ", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "排列组合问题", "组合思想解决问题", "补充知识点2897"], "similarity_score": 0.8313855528831482, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "排列组合问题", "组合思想解决问题", "补充知识点2897"], "topk_results": [{"similarity_score": 0.8313855528831482, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "排列组合问题", "组合思想解决问题", "补充知识点2897"], "sample_id": 20457}], "prediction_correct": true}, {"query": "一筐苹果连筐重 50 千克卖了一半后连筐重 26 千克这筐苹果有多重筐有多重首先我们知道一开始苹果连筐重 50 千克卖了一半苹果后连筐重 26 千克那减少的重量就是卖掉的一半苹果的重量用 50 - 26 = 24 千克这 24 千克就是一半苹果的重量所以整筐苹果的重量就是 24 × 2 = 48 千克然后用一开始苹果连筐的总重量 50 千克减去苹果的重量 48 千克就能算出筐重 2 千克", "true_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量间的计算和应用", "千克与克的相关应用题", "补充知识点2688"], "similarity_score": 0.8811129331588745, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量间的计算和应用", "解决吨相关的实际问题", "补充知识点2686"], "topk_results": [{"similarity_score": 0.8811129331588745, "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量间的计算和应用", "解决吨相关的实际问题", "补充知识点2686"], "sample_id": 11735}], "prediction_correct": false}, {"query": "下面的质量与 1 千克最接近的是（ ）A． 1 千克 2 克B． 999 克C． 9999 克1千克 = 1000克选项A中1千克2克就是1002克它和1000克相差1002 - 1000 = 2克选项B中999克和1000克相差1000 - 999 = 1克选项C中9999克和1000克相差9999 - 1000 = 8999克1克＜2克＜8999克所以999克和1千克最接近答案选B", "true_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量单位的换算", "克、千克之间的换算与比较", "千克和克之间的进率及换算"], "similarity_score": 0.7243219614028931, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量单位的换算", "吨、千克之间的换算与比较", "质量单位比较大小(吨、千克、克)"], "topk_results": [{"similarity_score": 0.7243219614028931, "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量单位的换算", "吨、千克之间的换算与比较", "质量单位比较大小(吨、千克、克)"], "sample_id": 11549}], "prediction_correct": false}, {"query": "三（1）班学生李丽的身高是 14（ ）体重是 32（ ）我们在生活中量身高一般用厘米或者分米作单位14厘米太矮了不符合三年级学生的身高情况所以李丽身高是14分米称体重通常用千克作单位所以体重是32千克", "true_label": ["小学数学新知识树", "图形与几何", "测量", "常见的长度单位及换算", "长度单位的选择", "选择合适的长度单位(分米、毫米)", "补充知识点4351"], "similarity_score": 0.7083128690719604, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "测量", "常见的面积单位及换算", "面积单位的选择", "认识面积单位", "补充知识点4383"], "topk_results": [{"similarity_score": 0.7083128690719604, "predicted_label": ["小学数学新知识树", "图形与几何", "测量", "常见的面积单位及换算", "面积单位的选择", "认识面积单位", "补充知识点4383"], "sample_id": 12173}], "prediction_correct": false}, {"query": "笔算 $193\\times2=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad890\\times7=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad603\\times5=$ ①计算193×2用2分别去乘193的每一位2乘3得62乘90得1802乘100得200加起来就是386②计算890×7先算89×77乘9得637乘80得560560加63是623再在末尾添上一个0就是6230③计算603×55乘3得155乘0得05乘600得3000加起来就是3015", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "两、三位数与一位数的一次进位乘法", "两、三位数乘一位数(不连续进位)的笔算"], "similarity_score": 0.7522707581520081, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘两位数的不进位乘法", "两位数乘两位数的笔算(不进位)"], "topk_results": [{"similarity_score": 0.7522707581520081, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘两位数的不进位乘法", "两位数乘两位数的笔算(不进位)"], "sample_id": 4556}], "prediction_correct": false}, {"query": "大熊猫是中国的国宝它属于食肉目、熊科、大熊猫亚科、大熊猫属是该属唯一的哺乳动物其身体长度在 $1.2\\sim1.8$ （ ）之间尾巴长约 $10\\sim12$ （ ）体重通常在 $80\\sim120$ （ ）范围内最重的个体可以达到 180（ ）体色呈现独特的黑白双色我们在生活中测量比较长的物体像大熊猫的身体长度一般用米做单位所以大熊猫身体长度在1.2 - 1.8米之间尾巴相对身体比较短测量短的物体长度一般用厘米所以尾巴长约10 - 12厘米而表示物体有多重要用质量单位像大熊猫的体重一般用千克做单位所以体重通常在80 - 120千克范围内最重个体能达到180千克", "true_label": ["小学数学新知识树", "图形与几何", "测量", "常见的长度单位及换算", "长度单位的选择", "选择合适的长度单位(分米、毫米)", "补充知识点4351"], "similarity_score": 0.6766242384910583, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量及质量的常用单位", "吨的认识", "认识质量单位吨\"\""], "topk_results": [{"similarity_score": 0.6766242384910583, "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量及质量的常用单位", "吨的认识", "认识质量单位吨\"\""], "sample_id": 11722}], "prediction_correct": false}, {"query": "用一副三角尺不可能拼出（ ） A．锐角B．直角C．钝角一副三角尺有两个三角形度数分别是90度、60度、30度和90度、45度、45度用三角尺拼角时比如30度和45度拼在一起是75度75度是锐角所以能拼出锐角用90度和30度拼在一起是120度120度是钝角所以能拼出钝角因此不可能拼出直角选B", "true_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "用三角尺画角", "三角尺拼角", "补充知识点3201"], "similarity_score": 0.9605176448822021, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "用三角尺画角", "三角尺拼角", "补充知识点3201"], "topk_results": [{"similarity_score": 0.9605176448822021, "predicted_label": ["小学数学新知识树", "图形与几何", "平面图形", "角", "用三角尺画角", "三角尺拼角", "补充知识点3201"], "sample_id": 15680}], "prediction_correct": true}, {"query": "（1） $4\\times9=36\\quad56-36=20$ 综合算式 ___（2） $22+23=45\\quad58-45=13$ 综合算式 ___（1）先算 $4×9=36$ 再用 $56$ 减去它们的积得到 $20$ 所以综合算式就是 $56-4×9=20$ （2）先算 $22+23=45$ 再用 $58$ 减去它们的和得到 $13$ 因为要先算加法所以要给加法加上括号综合算式就是 $58-( 22+23 )=13$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "根据分步运算列综合算式(含括号)(表内混合运算)"], "similarity_score": 0.7590715885162354, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "将分步算式改写成带小括号或中括号的综合算式"], "topk_results": [{"similarity_score": 0.7590715885162354, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "将分步算式改写成带小括号或中括号的综合算式"], "sample_id": 2131}], "prediction_correct": false}, {"query": "＂滴水凑成河粒米凑成箩文明用餐节俭惜福＂如果每人每天节约 125 克粮食那么张亮一家三口一周（7天）可以节约多少克粮食先算一家三口一天节约多少粮食就是3个125克用乘法125×3 = 375克再算一周7天节约多少粮食就是7个375克用乘法375×7 = 2625克所以张亮一家三口一周可以节约2625克粮食", "true_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量间的计算和应用", "千克与克的相关应用题", "补充知识点2688"], "similarity_score": 0.6435054540634155, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "用两步连乘解决实际问题", "用连乘解决实际问题"], "topk_results": [{"similarity_score": 0.6435054540634155, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "用两步连乘解决实际问题", "用连乘解决实际问题"], "sample_id": 6890}], "prediction_correct": false}, {"query": "学校印了 120 份节能手册组织了 5 名同学为家长免费发放 90 份 ___ 如果列式为 $( 120-90 )\\div5=6$ （份）请在横线上提问题题目里学校印了120份节能手册5名同学发了90份120 - 90算的是剩下的手册份数再除以5就是把剩下的手册平均分给5名同学算的是剩下的平均每名同学发放多少份", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "除加、除减混合运算", "除加除减应用题(表内混合运算)"], "similarity_score": 0.8785433769226074, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(7、8、9)"], "topk_results": [{"similarity_score": 0.8785433769226074, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "除法应用题(7、8、9)"], "sample_id": 32075}], "prediction_correct": false}, {"query": "李丽做口算题训练每行有 5 题有 8 行 ___ 已经做了多少题如果列式为 $5\\times8-3$ 请在横线上补充条件题目里 $5×8$ 算的是一共有多少题用总共的题数减去没做的题数就能得到已经做了的题数算式是 $5×8-3$ 所以没做的题数是 3 题也就是还剩 3 题没做", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.6569088697433472, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "topk_results": [{"similarity_score": 0.6569088697433472, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "sample_id": 13820}], "prediction_correct": false}, {"query": "下面算式得数与其他三个不相同的是（ ） A． $56-12-35$ B． $56-( 35+12 )$ C． $56-( 35-12 )$ D． $56-35-12$ 先看A选项56 - 12 - 35就是从56里先减掉12再减掉35D选项56 - 35 - 12是从56里先减掉35再减掉12这两个选项只是减的顺序不同结果是一样的再看B选项56 - ( 35 + 12 )根据去括号的知识它和A、D其实是一样的意思都是从56里减掉12和35这两个数的和而C选项56 - ( 35 - 12 )它是从56里减掉35和12的差和前面三个选项意思不一样所以得数也不同", "true_label": ["小学数学新知识树", "数学竞赛", "算式谜，数阵，进位制", "巧填运算符号", "减号后添/去括号", "补充知识点4941", "补充知识点4942"], "similarity_score": 0.7728748321533203, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "算式谜，数阵，进位制", "巧填运算符号", "减号后添/去括号", "补充知识点4941", "补充知识点4942"], "topk_results": [{"similarity_score": 0.7728748321533203, "predicted_label": ["小学数学新知识树", "数学竞赛", "算式谜，数阵，进位制", "巧填运算符号", "减号后添/去括号", "补充知识点4941", "补充知识点4942"], "sample_id": 40868}], "prediction_correct": true}, {"query": "小林读一本故事书 4 天读了 32 页照这样的速度 7 天可以读多少页如果全书 72页几天可以读完先看第一个问题4天读了32页那一天读的页数就是32除以4等于8页照这样的速度7天读的页数就是一天读的页数乘7也就是8乘7等于56页再看第二个问题已经知道一天读8页全书72页读完需要的天数就是72除以一天读的8页得到9天", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.7224413156509399, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数与两位数的乘除混合运算", "用乘除混合解决实际问题"], "topk_results": [{"similarity_score": 0.7224413156509399, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的除法", "多位数与两位数的乘除混合运算", "用乘除混合解决实际问题"], "sample_id": 4805}], "prediction_correct": false}, {"query": "一根木头锯成 3 段需要 6 分钟如果把这根木头锯成 6 段需要多少分钟我们先想想把木头锯成 3 段其实只需要锯 2 次锯 2 次用了 6 分钟那锯 1 次用的时间就是 6 ÷ 2 = 3 分钟要是把木头锯成 6 段那就需要锯 5 次锯 1 次是 3 分钟锯 5 次就是 3 × 5 = 15 分钟", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.9215407967567444, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "正比例的应用", "正比例的应用（补全、平面图、切割）", "补充知识点2465"], "topk_results": [{"similarity_score": 0.9215407967567444, "predicted_label": ["小学数学新知识树", "数与代数", "比和比例", "比例", "正比例的应用", "正比例的应用（补全、平面图、切割）", "补充知识点2465"], "sample_id": 8084}], "prediction_correct": false}, {"query": "欢欢在计算＂ $9+\\square\\div3$ ＂时弄错运算顺序先算加法再算除法结果得数是 7 正确的计算结果应该是（ ）A． 12B． 9C． 13D． 16", "true_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "similarity_score": 0.7276519536972046, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "topk_results": [{"similarity_score": 0.7276519536972046, "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "sample_id": 38125}], "prediction_correct": true}, {"query": "口算 $800\\times6=$ $40\\div5=$ $430+170=$ $401\\times5\\approx$ $280-50=$ $340\\times0=$ $43\\times5=$ $1-\\frac{11}{12}=$ 1. 计算 $800×6$ 8个百乘6是48个百也就是48002. 计算 $40÷5$ 根据乘法口诀“五八四十”所以 $40÷5=8$ 3. 计算 $430+170$ 43个十加17个十是60个十也就是6004. 估算 $401×5$ 把401看成400 $400×5=2000$ 所以 $401×5≈2000$ 5. 计算 $280-50$ 28个十减5个十是23个十也就是2306. 计算 $340×0$ 任何数乘0都得0所以 $340×0=0$ 7. 计算 $43×5$ 先算 $40×5=200$ 再算 $3×5=15$ 最后 $430+1700$ 8. 计算 $1-\\frac{11}{12}$ 把1看成 $\\frac{12}{12}$  $\\frac{12}{12}-\\frac{11}{12}=\\frac{1}{12}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.7501356601715088, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "topk_results": [{"similarity_score": 0.7501356601715088, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "sample_id": 12669}], "prediction_correct": false}, {"query": "口算 $800\\times6=$ $40\\div5=$ $430+170=$ $401\\times5\\approx$ $280-50=$ $340\\times0=$ $43\\times5=$ $1-\\frac{11}{12}=$ 1. 计算 $800×6$ 8个百乘6是48个百也就是48002. 计算 $40÷5$ 根据乘法口诀“五八四十”所以 $40÷5=8$ 3. 计算 $430+170$ 43个十加17个十是60个十也就是6004. 估算 $401×5$ 把401看成400 $400×5=2000$ 所以 $401×5≈2000$ 5. 计算 $280-50$ 28个十减5个十是23个十也就是2306. 计算 $340×0$ 任何数乘0都得0所以 $340×0=0$ 7. 计算 $43×5$ 先算 $40×5=200$ 再算 $3×5=15$ 最后 $430+1700$ 8. 计算 $1-\\frac{11}{12}$ 把1看成 $\\frac{12}{12}$  $\\frac{12}{12}-\\frac{11}{12}=\\frac{1}{12}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘一位数的估算", "两、三位数乘一位数的估算"], "similarity_score": 0.7501356601715088, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "topk_results": [{"similarity_score": 0.7501356601715088, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "除数是一位数的估算"], "sample_id": 12669}], "prediction_correct": false}, {"query": "一本笔记本 9 元一支笔 6 元 2 本笔记本的价钱可以买多少支笔解决这个问题首先要解决（ ） A． 2 支笔多少元B． 1 本笔记本多少元C． 2 本笔记本多少元D． 1 支笔多少钱可以列综合算式为（ ）A． $12\\times6\\div9$ B． $2\\times9\\div6$ C． $9\\times6\\div2$ D． $6\\div2\\times9$ 要知道2本笔记本的价钱能买多少支笔得先知道2本笔记本多少钱所以第一个空选C一本笔记本9元2本笔记本就是2×9元一支笔6元用2本笔记本的总价钱除以一支笔的价钱就能算出可以买笔的数量综合算式就是2×9÷6所以第二个空选B", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "用两步计算解决问题(表内混合运算)(不含括号)"], "similarity_score": 0.7612396478652954, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "人民币单位及换算", "元、角、分的认识及换算", "加减法应用(人民币)", "补充知识点2654"], "topk_results": [{"similarity_score": 0.7612396478652954, "predicted_label": ["小学数学新知识树", "数与代数", "常见的量", "人民币单位及换算", "元、角、分的认识及换算", "加减法应用(人民币)", "补充知识点2654"], "sample_id": 39527}], "prediction_correct": false}, {"query": "陈大爷家一共收了 410 千克苹果一个箱子最多能装 46 千克 8 个箱子能装下这些苹果吗下面解答比较合理的是（ ） A． $46\\times8\\approx320$ （千克） $46\\times8410$ 所以能装下这些苹果D． $46\\times8\\approx500$ （千克） $46\\times8>410$ 所以能装下这些苹果要判断8个箱子能不能装下410千克苹果得算出8个箱子大约能装多少千克把46看成和它接近的整十数5050×8 = 400千克因为把46看大了才装400千克实际46×8肯定小于400千克而400千克小于410千克所以8个箱子装不下这些苹果B选项的方法合理", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘一位数的估算", "两、三位数乘一位数的估算应用"], "similarity_score": 0.7175504565238953, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "灵活选择估算策略解决问题"], "topk_results": [{"similarity_score": 0.7175504565238953, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "灵活选择估算策略解决问题"], "sample_id": 4739}], "prediction_correct": false}, {"query": "某理发店经理到药店购买医用外科口罩每箱 1500 只需 560 元每满 1000 元减 50 元如果他买了 6 箱需付多少钱先算买 6 箱口罩的总价每箱 560 元6 箱就是 560×6 = 3360 元再看总价里有几个 1000 元3360÷1000 = 3（个）……360（元）说明有 3 个 1000 元能优惠 3×50 = 150 元最后用总价减去优惠的钱3360 - 150 = 3210 元就是需要付的钱", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "similarity_score": 0.6308164596557617, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "因数与倍数", "2、5的倍数特征", "解决有关2的倍数的实际问题"], "topk_results": [{"similarity_score": 0.6308164596557617, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "因数与倍数", "2、5的倍数特征", "解决有关2的倍数的实际问题"], "sample_id": 22781}], "prediction_correct": false}, {"query": "想一想画一画 $( 4+2 )\\times3$ 能解决什么问题先看括号里的4 + 2它表示把4和2合起来就像把小明的4个苹果和小红的2个苹果放在一起再乘3就是有3组这样合起来的苹果数量所以可以用( 4 + 2 )×3来算出总的苹果数", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "乘加、乘减混合运算", "乘加乘减应用题(表内混合运算)"], "similarity_score": 0.7257084846496582, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "topk_results": [{"similarity_score": 0.7257084846496582, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "无括号的运算顺序", "同级运算的运算顺序", "补充知识点1845"], "sample_id": 34975}], "prediction_correct": false}, {"query": "1、前置信息张大伯家养了 150 只小鸡2、请回答（2）剩下的小鸡第二天卖如果每 8 只小鸡装一笼需要多少个笼子首先算出剩下的小鸡数量张大伯家一共养了150只小鸡今天卖了102只所以剩下的小鸡数量为150-102=48（只）又因为每8只小鸡装一笼那么需要的笼子数为48÷8=6（个）", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "除加、除减混合运算", "除加除减应用题(表内混合运算)"], "similarity_score": 0.6733670234680176, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数的加法和减法", "几百几十数、整百数的加减法口算", "整百、整千数、几百几十数的加减法"], "topk_results": [{"similarity_score": 0.6733670234680176, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数的加法和减法", "几百几十数、整百数的加减法口算", "整百、整千数、几百几十数的加减法"], "sample_id": 40126}], "prediction_correct": false}, {"query": "小红一家三口去果园摘桃子全家一共摘了 254 个每盒装 8 个一共有 29 个盒子够装吗我们先算29个盒子能装多少个桃子每个盒子装8个29个盒子能装的桃子数就是29个8用乘法算29×8 我们可以把29看成3030×8 = 240因为29比30小所以29×8＜240 而全家摘了254个桃子240＜254所以29个盒子不够装", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘一位数的估算", "两、三位数乘一位数的估算应用"], "similarity_score": 0.8879162073135376, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "灵活选择估算策略解决问题"], "topk_results": [{"similarity_score": 0.8879162073135376, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "除数是一位数的估算", "灵活选择估算策略解决问题"], "sample_id": 4746}], "prediction_correct": false}, {"query": "淘淘只有 30 克和 50 克这两种规格的油瓶妈妈让他倒出 70 克的油请你帮淘淘想想该怎么倒出 70 克的油（用算式表示）我们要得到 70 克油因为只有 30 克和 50 克的油瓶我们可以先利用 50 克油瓶和 30 克油瓶得到 20 克油先把 50 克的油瓶倒满再从 50 克油瓶里倒出 30 克油到 30 克油瓶中此时 50 克油瓶里剩下 20 克油然后把 30 克油瓶里的油倒掉将 50 克油瓶里剩下的 20 克油倒入 30 克油瓶中再把 50 克油瓶倒满这时两个油瓶里的油合起来就是 70 克", "true_label": ["小学数学新知识树", "数与代数", "常见的量", "质量单位及换算", "质量间的计算和应用", "千克与克的相关应用题", "补充知识点2688"], "similarity_score": 0.6715761423110962, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "百分数的四则运算", "已知一个数的百分之几是多少，求这个数", "已知一个数的百分之几是多少，求这个数", "补充知识点1690"], "topk_results": [{"similarity_score": 0.6715761423110962, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "百分数的四则运算", "已知一个数的百分之几是多少，求这个数", "已知一个数的百分之几是多少，求这个数", "补充知识点1690"], "sample_id": 15152}], "prediction_correct": false}, {"query": "1、前置信息游乐场四月份上旬接待游客 580 人中旬接待游客 395 人下旬和中旬接待的游客总数是上旬的 2 倍2、请回答（2）乘坐摩天轮的人数是玩碰碰车人数的 $\\frac{5}{6}$  ___ 乘坐摩天轮的人数是多少人（请补充条件并列式解答）题目问乘坐摩天轮的人数已知乘坐摩天轮的人数是玩碰碰车人数的 $\\frac{5}{6}$ 那我们得知道玩碰碰车的人数才能算出乘坐摩天轮的人数所以补充玩碰碰车的人数这个条件假如补充玩碰碰车的人数是 36 人要求乘坐摩天轮的人数就是求 36 的 $\\frac{5}{6}$ 是多少用乘法计算就是 $36×\\frac{5}{6}=30$ 人", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "similarity_score": 0.6759975552558899, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加减法应用题"], "topk_results": [{"similarity_score": 0.6759975552558899, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "小数的四则运算", "小数的加法和减法", "利用小数的加、减法混合运算解决实际问题", "小数加减法应用题"], "sample_id": 18670}], "prediction_correct": false}, {"query": "一盒鸡蛋有 12 个小明一家 3 天吃了这盒鸡蛋的 $\\frac{3}{4}$ 他们家平均每天吃多少个鸡蛋先算 12 个鸡蛋的四分之三是多少把 12 个鸡蛋平均分成 4 份每份是 12÷4 = 3 个那 3 份就是 3×3 = 9 个小明一家 3 天吃了 9 个鸡蛋平均每天吃的鸡蛋数就是 9÷3 = 3 个", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "similarity_score": 0.6799201965332031, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "乘除法的计算与应用(2-6)"], "topk_results": [{"similarity_score": 0.6799201965332031, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "表内除法", "乘除法解决问题(表内)", "乘除法的计算与应用(2-6)"], "sample_id": 36868}], "prediction_correct": false}, {"query": "1、前置信息臧克家说＂读过一本好书像交了一个益友＂张亮看一本科学书共 72 页第一天看了这本科学书的 $\\frac{3}{8}$ 第二天看了这本科学书的 $\\frac{3}{9}$ 2、请回答（2）张亮说＂我第三天应该从第 51 页开始看＂张亮说的对吗请你通过计算说明理由", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "similarity_score": 0.68285071849823, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与整十数的加法", "口算两位数加整十数(不进位)解决实际问题"], "topk_results": [{"similarity_score": 0.68285071849823, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "100以内数的加法、减法", "两位数与整十数的加法", "口算两位数加整十数(不进位)解决实际问题"], "sample_id": 13265}], "prediction_correct": false}, {"query": "口算 $24\\times3=$ $12\\times5=$ $400\\times5=$ $900\\times6=$ $32\\times3=$ $50\\times6=$ $198\\times2\\approx$ $702\\times7\\approx$ $\\frac{1}{5}+\\frac{2}{5}=$ $1-\\frac{2}{3}=$ $\\frac{1}{6}+\\frac{5}{6}=$ $1-\\frac{5}{7}-\\frac{2}{7}=$ 对于乘法口算像 $24\\times3$ 可以先算 $20\\times3=60$ 再算 $4\\times3=12$ 最后 $60+12=72$ 整百数乘一位数如 $400\\times5$ 就是 $4$ 个百乘 $5$ 得 $20$ 个百也就是 $2000$ 估算时把 $198$ 看成 $200$  $200\\times2=400$ 把 $702$ 看成 $700$  $700\\times7=4900$ 同分母分数相加分母不变分子相加如 $\\frac{1}{5}+\\frac{2}{5}$  $1+2=3$ 结果就是 $\\frac{3}{5}$  $1$ 可以看成分子分母相同的分数 $1-\\frac{2}{3}$ 把 $1$ 看成 $\\frac{3}{3}$  $\\frac{3}{3}-\\frac{2}{3}=\\frac{1}{3}$  $\\frac{1}{6}+\\frac{5}{6}$  $1+5=6$ 结果是 $\\frac{6}{6}$ 也就是 $1$  $1-\\frac{5}{7}-\\frac{2}{7}$ 先算 $1-\\frac{5}{7}=\\frac{2}{7}$ 再算 $\\frac{2}{7}-\\frac{2}{7}=0$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "两位数与一位数的乘法口算", "两位数乘一位数的口算(有进位)"], "similarity_score": 0.7754688262939453, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "两位数乘两位数的估算", "两位数乘两位数的估算"], "topk_results": [{"similarity_score": 0.7754688262939453, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "两位数乘两位数的估算", "两位数乘两位数的估算"], "sample_id": 34584}], "prediction_correct": false}, {"query": "一堆沙第一次运走 $\\frac{1}{2}$ 第二次运走余下的 $\\frac{1}{2}$ 第三次又运走余下的 $\\frac{1}{3}$ 剩 4 吨这堆沙有多少吨", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "求一个数的几分之几的问题", "简单求一个数的几分之几是多少"], "similarity_score": 0.7983523607254028, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数乘分数", "分数乘分数的实际应用"], "topk_results": [{"similarity_score": 0.7983523607254028, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数乘分数", "分数乘分数的实际应用"], "sample_id": 15023}], "prediction_correct": false}, {"query": "超市进行酸奶大促销每瓶 8 元每买 4 瓶送 1 瓶张亮带了 72 元他最多可以买多少瓶酸奶", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "利用整数四则混合运算解决问题", "优惠策略"], "similarity_score": 0.7539007067680359, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "小数的认识", "小数点位置的移动", "利用小数点移动引起小数大小的变化解决实际问题", "补充知识点392"], "topk_results": [{"similarity_score": 0.7539007067680359, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "小数的认识", "小数点位置的移动", "利用小数点移动引起小数大小的变化解决实际问题", "补充知识点392"], "sample_id": 3763}], "prediction_correct": false}, {"query": "脱式计算 $\n450+280-60\n$ $\n65-45\\div5\n$ $\n56\\div( 35-27 )\n$ 对于450+280 - 60按照从左到右的顺序算先算450加280得730再用730减60就是670对于65 - 45÷5有除法和减法要先算除法45除以5得9再用65减9就是56对于56÷( 35 - 27 )有括号要先算括号里的35减27得8再用56除以8就是7", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "similarity_score": 0.7774248719215393, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "topk_results": [{"similarity_score": 0.7774248719215393, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "带有小括号的混合运算", "含有小括号的表内混合运算"], "sample_id": 40844}], "prediction_correct": true}, {"query": "$120-84=36,36\\div6=6$ 写成综合算式是（ ） 看这两个算式第一个算式算出的结果36在第二个算式里当被除数要把这两个算式写成综合算式就要用第一个算式 $120-84$ 来代替第二个算式里的36因为要先算减法再算除法根据运算顺序有括号的要先算括号里面的所以要给 $120-84$ 加上括号写成综合算式就是 $( 120-84 )\\div6=6$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "根据分步运算列综合算式(含括号)(表内混合运算)"], "similarity_score": 0.7697467803955078, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "将分步算式改写成带小括号或中括号的综合算式"], "topk_results": [{"similarity_score": 0.7697467803955078, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数四则混合运算", "根据分步式列综合式", "将分步算式改写成带小括号或中括号的综合算式"], "sample_id": 2125}], "prediction_correct": false}, {"query": "计算 6＋□ $\\times6$ 时错误的是先算 $6+\\square$ 结果为 48正确结果是（ ）因为错误的计算是先算6 + □结果为48那么错误计算中6 + □的结果是48÷6=8所以□里的数是8-6=2.按照正确的运算顺序先算乘法再算加法即6+2×6=6+12=18.综上正确结果是18.", "true_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "similarity_score": 0.8298588991165161, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "topk_results": [{"similarity_score": 0.8298588991165161, "predicted_label": ["小学数学新知识树", "数学竞赛", "计算", "错中求解", "错中求解(表内混合运算)", "补充知识点4840", "补充知识点4841"], "sample_id": 38112}], "prediction_correct": true}, {"query": "$550\\times2$ 的积的末尾一共有（ ）个 0 A． 1B． 2C． 3D． 4先计算550×2的结果550×2 = 11001100的末尾有2个0所以550×2的积的末尾一共有2个0答案选B", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.7696566581726074, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "两位数乘两位数的估算", "积末尾0的个数（口算）"], "topk_results": [{"similarity_score": 0.7696566581726074, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "两位数乘两位数的估算", "积末尾0的个数（口算）"], "sample_id": 41668}], "prediction_correct": false}, {"query": "30 的 2 倍是（ ） 60 个 5 的和是（ ） （ ） 的 8 倍是 40求 30 的 2 倍是多少用乘法30×2 = 60求 60 个 5 的和也就是 60×5 = 300已知一个数的 8 倍是 40求这个数用除法40÷8 = 5", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.7822174429893494, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "两位数乘整十数的应用"], "topk_results": [{"similarity_score": 0.7822174429893494, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与两位数的乘法", "两位数乘整十数的口算乘法", "两位数乘整十数的应用"], "sample_id": 12465}], "prediction_correct": false}, {"query": "与算式 45－29 结果不相同的是（ ） A． $45-25-4$ B． $45-30-1$ C． $45-30+1$ D． $45-20-9$ 选项A把29看作25+4则45-29=45-( 29+4 )=45-25-4正确选择B、C把29看作30-1则45-29=45-( 30-1 )=45-30+1B错误C正确选项D把29看作45-25-43则45-29=45-( 45-25-43 )=45-20-9正确综上答案选B", "true_label": ["小学数学新知识树", "数学竞赛", "算式谜，数阵，进位制", "巧填运算符号", "减号后添/去括号", "补充知识点4941", "补充知识点4942"], "similarity_score": 0.7635451555252075, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "小数加、减法简便运算", "与小数减法相关的简便运算", "小数连减简算"], "topk_results": [{"similarity_score": 0.7635451555252075, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "小数加、减法简便运算", "与小数减法相关的简便运算", "小数连减简算"], "sample_id": 37453}], "prediction_correct": false}, {"query": "口算 $24\\times3=$ $12\\times5=$ $400\\times5=$ $900\\times6=$ $32\\times3=$ $50\\times6=$ $198\\times2\\approx$ $702\\times7\\approx$ $\\frac{1}{5}+\\frac{2}{5}=$ $1-\\frac{2}{3}=$ $\\frac{1}{6}+\\frac{5}{6}=$ $1-\\frac{5}{7}-\\frac{2}{7}=$ ① $24\\times3$ 可以先算 $20\\times3=60$ 再算 $4\\times3=12$ 最后 $60+12=72$ ② $12\\times5$ 可以先算 $10\\times5=50$ 再算 $2\\times5=10$ 最后 $50+10=60$ ③整百数乘一位数 $400\\times5$ 就是 $4$ 个百乘 $5$ 得 $20$ 个百也就是 $2000$ ④整百数乘一位数 $900\\times6$ 就是 $9$ 个百乘 $6$ 得 $54$ 个百也就是 $5400$ ⑤ $32\\times3$ 可以先算 $30\\times3=90$ 再算 $2\\times3=6$ 最后 $90+6=96$ ⑥整十数乘一位数 $50\\times6$ 就是 $5$ 个十乘 $6$ 得30个十也就是 $300$ ⑦把 $198$ 看成 $200$  $200\\times3=600$ 也就是 $198\\times2\\approx400$ ⑧把 $702$ 看成 $700$  $700\\times3=601$ 也就是 $702\\times7\\approx4900$ ⑨同分母分数相加分母不变分子相加如 $\\frac{1}{5}+\\frac{2}{5}$  $3=602$ 结果就是 $\\frac{3}{5}$ ⑩ $1$ 可以看成分子分母相同的分数 $1-\\frac{2}{3}$ 把 $1$ 看成 $\\frac{3}{3}$  $\\frac{3}{3}-\\frac{2}{3}=\\frac{1}{3}$ ⑪ $\\frac{1}{6}+\\frac{5}{6}$  $3=603$ 结果是 $\\frac{6}{6}$ 也就是 $1$ ⑫ $1-\\frac{5}{7}-\\frac{2}{7}$ 先算 $1-\\frac{5}{7}=\\frac{2}{7}$ 再算 $\\frac{2}{7}-\\frac{2}{7}=0$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.7466063499450684, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "两位数乘两位数的估算", "两位数乘两位数的估算"], "topk_results": [{"similarity_score": 0.7466063499450684, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "两位数乘两位数的估算", "两位数乘两位数的估算"], "sample_id": 34584}], "prediction_correct": false}, {"query": "口算 $24\\times3=$ $12\\times5=$ $400\\times5=$ $900\\times6=$ $32\\times3=$ $50\\times6=$ $198\\times2\\approx$ $702\\times7\\approx$ $\\frac{1}{5}+\\frac{2}{5}=$ $1-\\frac{2}{3}=$ $\\frac{1}{6}+\\frac{5}{6}=$ $1-\\frac{5}{7}-\\frac{2}{7}=$ ① $24\\times3$ 可以先算 $20\\times3=60$ 再算 $4\\times3=12$ 最后 $60+12=72$ ② $12\\times5$ 可以先算 $10\\times5=50$ 再算 $2\\times5=10$ 最后 $50+10=60$ ③整百数乘一位数 $400\\times5$ 就是 $4$ 个百乘 $5$ 得 $20$ 个百也就是 $2000$ ④整百数乘一位数 $900\\times6$ 就是 $9$ 个百乘 $6$ 得 $54$ 个百也就是 $5400$ ⑤ $32\\times3$ 可以先算 $30\\times3=90$ 再算 $2\\times3=6$ 最后 $90+6=96$ ⑥整十数乘一位数 $50\\times6$ 就是 $5$ 个十乘 $6$ 得30个十也就是 $300$ ⑦把 $198$ 看成 $200$  $200\\times3=600$ 也就是 $198\\times2\\approx400$ ⑧把 $702$ 看成 $700$  $700\\times3=601$ 也就是 $702\\times7\\approx4900$ ⑨同分母分数相加分母不变分子相加如 $\\frac{1}{5}+\\frac{2}{5}$  $3=602$ 结果就是 $\\frac{3}{5}$ ⑩ $1$ 可以看成分子分母相同的分数 $1-\\frac{2}{3}$ 把 $1$ 看成 $\\frac{3}{3}$  $\\frac{3}{3}-\\frac{2}{3}=\\frac{1}{3}$ ⑪ $\\frac{1}{6}+\\frac{5}{6}$  $3=603$ 结果是 $\\frac{6}{6}$ 也就是 $1$ ⑫ $1-\\frac{5}{7}-\\frac{2}{7}$ 先算 $1-\\frac{5}{7}=\\frac{2}{7}$ 再算 $\\frac{2}{7}-\\frac{2}{7}=0$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "三位数乘一位数的估算", "两、三位数乘一位数的估算"], "similarity_score": 0.7466063499450684, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "两位数乘两位数的估算", "两位数乘两位数的估算"], "topk_results": [{"similarity_score": 0.7466063499450684, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "整数的简单估算", "两位数乘两位数的估算", "两位数乘两位数的估算"], "sample_id": 34584}], "prediction_correct": false}, {"query": "口算30×4=                    500×2=                12×5=                21×3=  $\\frac{1}{4}+\\frac{3}{4}=$              $\\frac{7}{8}-\\frac{3}{8}=$              $\\frac{1}{11}+\\frac{3}{11}=$              $1-\\frac{5}{7}=$ 对于乘法像30×43个十乘4是12个十就是120500×25个百乘2是10个百就是100012×5先算2×5 = 10再算10×5 = 5050 + 10 = 6021×3先算1×3 = 3再算20×3 = 6060 + 3 = 63对于分数加法和减法同分母分数相加或相减分母不变分子相加或相减 $\\frac{1}{4}+\\frac{3}{4}$ 4=                    5000分母是4结果就是1 $\\frac{7}{8}-\\frac{3}{8}$ 4=                    5001分母是8结果是 $\\frac{4}{8}$  $\\frac{1}{11}+\\frac{3}{11}$ 4=                    5000分母是11结果是 $\\frac{4}{11}$ 1可以看成 $\\frac{7}{7}$  $\\frac{7}{7}-\\frac{5}{7}$ 4=                    5003分母是7结果是 $\\frac{2}{7}$ ", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "两、三位数与一位数的乘法", "整十、整百、整千数与一位数的乘法", "整十、整百、整千数乘一位数的口算"], "similarity_score": 0.74881911277771, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数乘小数", "分数乘小数的计算"], "topk_results": [{"similarity_score": 0.74881911277771, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "分数的四则运算", "分数乘法", "分数乘小数", "分数乘小数的计算"], "sample_id": 14906}], "prediction_correct": false}, {"query": "算一算\n| 加数 | 15 | 12 | 10 | 加数 | 4 | 7 | 8 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 加数 | 15 | 12 | 10 |\n| 加数 | 4 | 7 | 8 |\n| 和 |   |   |   |\n\n| 被减数 | 14 | 17 | 16 | 减数 | 1 | 6 | 6 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 被减数 | 14 | 17 | 16 |\n| 减数 | 1 | 6 | 6 |\n| 差 |   |   |   |\n算加法时我们可以一个一个地接着数比如15加4从15往后数4个数16、17、18、19所以15加4等于19同样的方法12加7从12往后数7个数得到1910加8从10往后数8个数得到18算减法时我们可以从被减数里一个一个地去掉减数的数量比如14减1从14里面去掉1个剩下1317减6从17里面去掉6个剩下1116减6从16里面去掉6个剩下10", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "20以内数的加、减法", "不退位减法（20以内）", "十几减几的不退位减法"], "similarity_score": 0.8056609630584717, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "加、减法的意义和各部分间的关系", "被减数、减数和差中的规律", "补充知识点1771"], "topk_results": [{"similarity_score": 0.8056609630584717, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "运算定律与简便运算", "加、减法的意义和各部分间的关系", "被减数、减数和差中的规律", "补充知识点1771"], "sample_id": 42970}], "prediction_correct": false}, {"query": "与 16 相邻的两个数是（        ） 和（        ） 16 前面的数是 15后面的数是 17所以与 16 相邻的两个数是 15 和 17", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "20以内数的认识", "20以内数的排序", "11-20各数的顺序"], "similarity_score": 0.700263500213623, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数数的方法", "连续数(100以内)"], "topk_results": [{"similarity_score": 0.700263500213623, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数数的方法", "连续数(100以内)"], "sample_id": 13034}], "prediction_correct": false}, {"query": "19 前面的一个数是（        ） 后面的一个数是（        ） 在数数的时候一个一个往前数数会越来越小19 前面一个数就是比 19 少 1 的数是 18一个一个往后数数会越来越大19 后面一个数就是比 19 多 1 的数是 20", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "20以内数的认识", "20以内数的排序", "11-20各数的顺序"], "similarity_score": 0.7707009315490723, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "20以内数的认识", "20以内数的排序", "11-20各数的顺序"], "topk_results": [{"similarity_score": 0.7707009315490723, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "20以内数的认识", "20以内数的排序", "11-20各数的顺序"], "sample_id": 16086}], "prediction_correct": true}, {"query": "11 后面的第 4 个数是（        ） ① 15② 16③ 17从 11 开始往后数11 后面第 1 个数是 12第 2 个数是 13第 3 个数是 14第 4 个数是 15所以选①", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "20以内数的认识", "20以内数的排序", "11-20各数的顺序"], "similarity_score": 0.6898614168167114, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数数的方法", "连续数(100以内)"], "topk_results": [{"similarity_score": 0.6898614168167114, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数数的方法", "连续数(100以内)"], "sample_id": 13036}], "prediction_correct": false}, {"query": "与 10 相邻的两个数是（        ） 和（        ） 与 18 相邻的两个数是（        ） 和（        ） 相邻的数就是这个数前面一个数和后面一个的数10前面的一个数是9后面1个数是11所以与 10 相邻的两个数是 9 和 1118前面的一个数是17后面1个数是19所以与 18 相邻的两个数是 17 和 19", "true_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "20以内数的认识", "20以内数的排序", "11-20各数的顺序"], "similarity_score": 0.7963590621948242, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数数的方法", "连续数(100以内)"], "topk_results": [{"similarity_score": 0.7963590621948242, "predicted_label": ["小学数学新知识树", "数与代数", "数的认识", "整数的认识", "100以内数的认识", "数数的方法", "连续数(100以内)"], "sample_id": 13046}], "prediction_correct": false}, {"query": "四位小朋友见面握手每两个人要握 1 次一共要握（ ） 次手", "true_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "排列组合问题", "组合思想解决问题", "补充知识点2897"], "similarity_score": 0.8873730301856995, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "搭配问题", "握手问题", "补充知识点2919"], "topk_results": [{"similarity_score": 0.8873730301856995, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "搭配问题", "握手问题", "补充知识点2919"], "sample_id": 15803}], "prediction_correct": false}, {"query": "小丽用右图的方法计算＂ $7+8$ ＂下面方法与她相同的是（ ） \n|   |\n| --- |\n| 7+3=1010+5=15 |\n①②③小丽计算7+8的方法是将8分成3和5先算7+3=10再算10+5=15①图中左边有8个右边有7个将左边的3个和右边的7个框一起即先算7+3=10再算10+5=15与小丽方法相同②图中左边有8个右边有7个左边的8个和右边的2个框一起即先算8+2=10再算10+5=15与小丽方法不相同③图中将左边的8个分成了2和6右边的7个分成了4和3先算6+4=10再算7+80与小丽方法不相同因此与小丽方法相同的是①", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "20以内数的加、减法", "8加几的进位加法", "8、7、6加几"], "similarity_score": 0.7234182357788086, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "20以内数的加、减法", "十几减5~2的退位减法", "十几减5、4、3、2的应用"], "topk_results": [{"similarity_score": 0.7234182357788086, "predicted_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "20以内数的加、减法", "十几减5~2的退位减法", "十几减5、4、3、2的应用"], "sample_id": 41176}], "prediction_correct": false}, {"query": "（1）一共有（ ）张数字卡片其中最大的数字是（ ） 最小的数字是（ ） （2）从左数起 0 在第（ ）个 6 的右边有（ ）张数字卡片（3）圈出左边的 3 张卡片（4）从上面的数字卡片中选 3 张组成 2 道加法算式和 2 道减法算式（1）观察图片一共有10张数字卡片分别是2、5、4、0、8、6、3、1、7、9其中最大的数字是9最小的数字是0（2）从左数起0在第4个位置6的右边有4张卡片分别是3、1、7、9（3）左边的3张卡片是2、5、4需要圈出这三张（4）从卡片中选择数字5、4、9可以组成加法算式5+4=94+5=9减法算式9-5=49-4=5", "true_label": ["小学数学新知识树", "数与代数", "数的运算", "整数的四则运算", "10以内数的加、减法", "10的加减法", "10的加减法的应用"], "similarity_score": 0.7570589780807495, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "排列组合问题", "根据读0个数的组数问题(亿以内)", "补充知识点2879"], "topk_results": [{"similarity_score": 0.7570589780807495, "predicted_label": ["小学数学新知识树", "数与代数", "应用题", "常见的数学问题", "排列组合问题", "根据读0个数的组数问题(亿以内)", "补充知识点2879"], "sample_id": 14152}], "prediction_correct": false}, {"query": "数一数（1）（ ）个 （ ）个（2）（ ）个    （ ）个（ ）个       （ ）个略", "true_label": ["小学数学新知识树", "图形与几何", "图形的拼组", "立体图形的切拼", "立体图形的搭建", "相同正方体拼搭", "补充知识点4004"], "similarity_score": 0.7390842437744141, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "立体图形", "立体图形的认识及分类", "初步认识立体图形", "补充知识点3647", "补充知识点3648"], "topk_results": [{"similarity_score": 0.7390842437744141, "predicted_label": ["小学数学新知识树", "图形与几何", "立体图形", "立体图形的认识及分类", "初步认识立体图形", "补充知识点3647", "补充知识点3648"], "sample_id": 16040}], "prediction_correct": false}, {"query": "数一数（1）（ ）个 （ ）个（2）（ ）个    （ ）个（ ）个       （ ）个略", "true_label": ["小学数学新知识树", "图形与几何", "立体图形", "立体图形的认识及分类", "初步认识立体图形", "补充知识点3647", "补充知识点3648"], "similarity_score": 0.7390842437744141, "above_threshold": true, "prediction_source": "vector_db", "predicted_label": ["小学数学新知识树", "图形与几何", "立体图形", "立体图形的认识及分类", "初步认识立体图形", "补充知识点3647", "补充知识点3648"], "topk_results": [{"similarity_score": 0.7390842437744141, "predicted_label": ["小学数学新知识树", "图形与几何", "立体图形", "立体图形的认识及分类", "初步认识立体图形", "补充知识点3647", "补充知识点3648"], "sample_id": 16040}], "prediction_correct": true}]