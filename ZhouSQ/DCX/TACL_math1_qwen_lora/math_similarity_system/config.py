"""
配置文件 - 数学题目相似度匹配系统
"""
import os

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(BASE_DIR)

# 数据路径
TRAIN_DATA_PATH = os.path.join(PROJECT_ROOT, "math1_level7_data", "wos_train.json")
# TEST_DATA_PATH = os.path.join(PROJECT_ROOT, "math1_level7_data", "wos_test.json")
TEST_DATA_PATH = os.path.join(PROJECT_ROOT, "math1_level7_data", "num_beyond_5.json")

# 模型路径
EMBEDDING_MODEL_PATH = os.path.join(PROJECT_ROOT, "Qwen3-Embedding-4B")
BASE_MODEL_PATH = os.path.join(PROJECT_ROOT, "Qwen3-4B")
LORA_MODEL_PATH = os.path.join(PROJECT_ROOT, "LLaMA-Factory", "saves", "Qwen3-4B-Instruct-2507", "lora", "train_2025-09-09-10-49-52")

# 向量数据库配置
VECTOR_DB_PATH = os.path.join(BASE_DIR, "data", "vector_db")
VECTOR_INDEX_PATH = os.path.join(VECTOR_DB_PATH, "Qwen3-Embedding-4B-level7.bin")
METADATA_PATH = os.path.join(VECTOR_DB_PATH, "Qwen3-Embedding-4B-metadata-level7.json")

# 相似度匹配配置
SIMILARITY_THRESHOLD = 0.00001  # 可调整的相似度阈值
TOP_K = 3  # 返回最相似的K个结果
VECTOR_SEARCH_TOP_K = 1000  # 向量数据库搜索返回的结果数量

# 模型配置
EMBEDDING_BATCH_SIZE = 4  # 每个GPU的批量大小，四卡并行时可以适当增大
MAX_LENGTH = 512

# 多GPU并行配置
import torch
import os

# 设置CUDA内存分配策略 - 更激进的内存管理
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:128'
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 同步CUDA操作，便于内存管理

if torch.cuda.is_available():
    # 强制使用4个GPU进行并行处理
    num_gpus = torch.cuda.device_count()
    if num_gpus >= 4:
        AVAILABLE_GPUS = [0, 1, 2, 3]  # 使用前4个GPU
    else:
        AVAILABLE_GPUS = list(range(num_gpus))  # 使用所有可用GPU

    PRIMARY_DEVICE = f"cuda:{AVAILABLE_GPUS[0]}"
    USE_MULTI_GPU = True  # 强制启用多GPU模式

    print(f"🚀 GPU配置信息:")
    print(f"  - 检测到 {num_gpus} 个GPU")
    print(f"  - 使用 {len(AVAILABLE_GPUS)} 个GPU进行并行处理")
    print(f"  - Available GPUs: {AVAILABLE_GPUS}")
    print(f"  - Primary GPU: {PRIMARY_DEVICE}")
    print(f"  - Multi-GPU enabled: {USE_MULTI_GPU}")
    print(f"  - 每个GPU批量大小: {EMBEDDING_BATCH_SIZE}")
else:
    AVAILABLE_GPUS = []
    PRIMARY_DEVICE = "cpu"
    USE_MULTI_GPU = False

# 注意：LLaMA-Factory相关配置已移除，请使用独立的analyze_llama_factory_results.py脚本

# 评估配置
RESULTS_DIR = os.path.join(BASE_DIR, "results")
LOGS_DIR = os.path.join(BASE_DIR, "logs")

# 输出文件名配置
SIMILARITY_RESULTS_FILENAME = f"similarity_results.json"
EVALUATION_REPORT_FILENAME = f"evaluation_report.json"

# 日志配置
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
ENABLE_DETAILED_LOGGING = True

# 性能配置
ENABLE_PROGRESS_BAR = True  # 是否显示进度条
BATCH_PROCESSING_ENABLED = True  # 是否启用批处理
SAVE_INTERMEDIATE_RESULTS = True  # 是否保存中间结果

# 评估配置选项
FOCUS_ON_LEAF_NODES_ONLY = True  # 是否只关注叶子节点评估
CALCULATE_PER_CLASS_METRICS = True  # 是否计算每个类别的详细指标
INCLUDE_CONFUSION_MATRIX = False  # 是否包含混淆矩阵（大类别数时可能很大）

# 确保目录存在
os.makedirs(VECTOR_DB_PATH, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)
os.makedirs(LOGS_DIR, exist_ok=True)

# 配置摘要打印
def print_config_summary():
    """打印当前配置摘要"""
    print("\n" + "="*60)
    print("📋 当前配置摘要")
    print("="*60)
    print(f"数据配置:")
    print(f"  训练数据: {os.path.basename(TRAIN_DATA_PATH)}")
    print(f"  测试数据: {os.path.basename(TEST_DATA_PATH)}")
    print(f"模型配置:")
    print(f"  嵌入模型: {os.path.basename(EMBEDDING_MODEL_PATH)}")
    print(f"  向量数据库: {os.path.basename(VECTOR_INDEX_PATH)}")
    print(f"匹配配置:")
    print(f"  相似度阈值: {SIMILARITY_THRESHOLD}")
    print(f"  Top-K: {TOP_K}")
    print(f"  批量大小: {EMBEDDING_BATCH_SIZE}")
    print(f"GPU配置:")
    print(f"  可用GPU: {AVAILABLE_GPUS if 'AVAILABLE_GPUS' in globals() else 'CPU'}")
    print(f"  多GPU模式: {USE_MULTI_GPU if 'USE_MULTI_GPU' in globals() else False}")
    print("="*60)

# 配置切换函数

def switch_top_k(new_top_k):
    """切换Top-K设置"""
    global TOP_K, VECTOR_SEARCH_TOP_K, SIMILARITY_RESULTS_FILENAME, EVALUATION_REPORT_FILENAME

    TOP_K = new_top_k
    VECTOR_SEARCH_TOP_K = new_top_k
    SIMILARITY_RESULTS_FILENAME = f"similarity_results_{SIMILARITY_THRESHOLD}_top{TOP_K}_4b.json"
    EVALUATION_REPORT_FILENAME = f"leaf_node_evaluation_report_4b_top{TOP_K}.json"
    print(f"✅ 已切换Top-K设置为: {new_top_k}")
    print(f"   相似度结果文件: {SIMILARITY_RESULTS_FILENAME}")
    print(f"   评估报告文件: {EVALUATION_REPORT_FILENAME}")

def switch_similarity_threshold(new_threshold):
    """切换相似度阈值"""
    global SIMILARITY_THRESHOLD, SIMILARITY_RESULTS_FILENAME

    SIMILARITY_THRESHOLD = new_threshold
    SIMILARITY_RESULTS_FILENAME = f"similarity_results_{SIMILARITY_THRESHOLD}_top{TOP_K}_4b.json"
    print(f"✅ 已切换相似度阈值为: {new_threshold}")
    print(f"   相似度结果文件: {SIMILARITY_RESULTS_FILENAME}")

# 快捷配置预设
def apply_preset_config(preset_name):
    """应用预设配置"""
    presets = {
        "top1_strict": {"top_k": 1, "threshold": 0.85},
        "top3_normal": {"top_k": 3, "threshold": 0.00001},
        "top5_loose": {"top_k": 5, "threshold": 0.00001}
    }

    if preset_name in presets:
        preset = presets[preset_name]
        if "top_k" in preset:
            switch_top_k(preset["top_k"])
        if "threshold" in preset:
            switch_similarity_threshold(preset["threshold"])
        print(f"✅ 已应用预设配置: {preset_name}")
    else:
        print(f"❌ 无效的预设配置: {preset_name}")
        print(f"   可用预设: {list(presets.keys())}")

if __name__ == "__main__":
    # 如果直接运行config.py，显示配置摘要
    print_config_summary()
    print(f"\n💡 使用示例:")
    print(f"   from config import switch_top_k, switch_similarity_threshold, apply_preset_config")
    print(f"   switch_top_k(3)  # 切换到Top-3")
    print(f"   switch_similarity_threshold(0.85)  # 切换相似度阈值")
    print(f"   apply_preset_config('top3_normal')  # 应用预设配置")
    print(f"\n📝 注意: LLaMA-Factory分析请使用独立脚本 analyze_llama_factory_results.py")
