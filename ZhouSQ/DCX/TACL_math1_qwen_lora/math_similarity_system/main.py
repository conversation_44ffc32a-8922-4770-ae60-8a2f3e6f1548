"""
数学题目相似度匹配系统 - 主程序
整合向量数据库构建、相似度匹配、模型推理和评估功能
"""
import os
import sys
import argparse
import logging
import time
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.vector_database import VectorDatabase
from src.similarity_matcher import SimilarityMatcher
from src.model_inference import FineTunedModelInference
from src.evaluation import EvaluationMetrics
from config import *

# 设置日志
# log_filename = os.path.join(LOGS_DIR, f"main_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler(log_filename, encoding='utf-8'),
#         logging.StreamHandler()
#     ]
# )
logger = logging.getLogger(__name__)

class MathSimilaritySystem:
    def __init__(self, threshold: float = None):
        self.threshold = threshold if threshold is not None else SIMILARITY_THRESHOLD
        self.vector_db = None
        self.similarity_matcher = None
        self.model_inference = None
        self.evaluator = None
        
    def step1_build_vector_database(self):
        """步骤1: 构建向量数据库"""
        logger.info("="*60)
        logger.info("步骤1: 构建向量数据库")
        logger.info("="*60)
        
        start_time = time.time()
        
        self.vector_db = VectorDatabase()
        self.vector_db.build_database()
        
        elapsed_time = time.time() - start_time
        logger.info(f"向量数据库构建完成，耗时: {elapsed_time:.2f}秒")
        
    def step2_similarity_matching(self):
        """步骤2: 相似度匹配"""
        logger.info("="*60)
        logger.info("步骤2: 相似度匹配")
        logger.info("="*60)
        
        start_time = time.time()
        
        self.similarity_matcher = SimilarityMatcher(threshold=self.threshold)
        similarity_results = self.similarity_matcher.process_all_test_data()
        results_path = self.similarity_matcher.save_similarity_results(similarity_results)
        
        elapsed_time = time.time() - start_time
        logger.info(f"相似度匹配完成，耗时: {elapsed_time:.2f}秒")
        
        return similarity_results, results_path
    
    def step3_model_inference(self, similarity_results):
        """步骤3: 微调模型推理"""
        logger.info("="*60)
        logger.info("步骤3: 微调模型推理")
        logger.info("="*60)
        
        start_time = time.time()
        
        self.model_inference = FineTunedModelInference()
        final_results = self.model_inference.process_similarity_results(similarity_results)
        results_path = self.model_inference.save_final_results(final_results)
        
        elapsed_time = time.time() - start_time
        logger.info(f"模型推理完成，耗时: {elapsed_time:.2f}秒")
        
        return final_results, results_path
    
    def step4_evaluation(self, final_results):
        """步骤4: 评估"""
        logger.info("="*60)
        logger.info("步骤4: 评估")
        logger.info("="*60)
        
        start_time = time.time()
        
        self.evaluator = EvaluationMetrics()
        report = self.evaluator.generate_detailed_report(final_results)
        report_path = self.evaluator.save_evaluation_report(report)
        
        # 打印评估摘要
        self.evaluator.print_summary(report)
        
        elapsed_time = time.time() - start_time
        logger.info(f"评估完成，耗时: {elapsed_time:.2f}秒")
        
        return report, report_path
    
    def run_complete_pipeline(self):
        """运行完整的流水线"""
        logger.info("开始运行数学题目相似度匹配系统")
        logger.info(f"相似度阈值: {self.threshold}")
        
        total_start_time = time.time()
        
        try:
            # 步骤1: 构建向量数据库
            self.step1_build_vector_database()
            
            # 步骤2: 相似度匹配
            similarity_results, similarity_path = self.step2_similarity_matching()
            
            # 步骤3: 模型推理
            final_results, final_path = self.step3_model_inference(similarity_results)
            
            # 步骤4: 评估
            report, report_path = self.step4_evaluation(final_results)
            
            total_elapsed_time = time.time() - total_start_time
            
            logger.info("="*60)
            logger.info("系统运行完成!")
            logger.info(f"总耗时: {total_elapsed_time:.2f}秒")
            logger.info(f"相似度匹配结果: {similarity_path}")
            logger.info(f"最终预测结果: {final_path}")
            logger.info(f"评估报告: {report_path}")
            logger.info("="*60)
            
            return {
                'similarity_results': similarity_results,
                'final_results': final_results,
                'evaluation_report': report,
                'paths': {
                    'similarity': similarity_path,
                    'final': final_path,
                    'evaluation': report_path
                }
            }
            
        except Exception as e:
            logger.error(f"系统运行出错: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="数学题目相似度匹配系统")
    parser.add_argument(
        '--threshold', 
        type=float, 
        default=SIMILARITY_THRESHOLD,
        help=f'相似度阈值 (默认: {SIMILARITY_THRESHOLD})'
    )
    parser.add_argument(
        '--step',
        type=str,
        choices=['all', 'vector_db', 'similarity', 'inference', 'evaluation'],
        default='all',
        help='运行特定步骤 (默认: all)'
    )
    parser.add_argument(
        '--parallel',
        action='store_true',
        help='强制启用四卡并行模式'
    )
    parser.add_argument(
        '--single-gpu',
        action='store_true',
        help='强制使用单GPU模式'
    )
    parser.add_argument(
        '--top-k',
        type=int,
        default=TOP_K,
        help=f'返回最相似的K个结果 (默认: {TOP_K})'
    )
    parser.add_argument(
        '--config-preset',
        type=str,
        choices=['top1_strict', 'top3_normal', 'top5_loose'],
        help='应用预设配置'
    )

    args = parser.parse_args()

    # 应用命令行配置
    if args.config_preset:
        from config import apply_preset_config
        apply_preset_config(args.config_preset)

    if args.top_k != TOP_K:
        from config import switch_top_k
        switch_top_k(args.top_k)

    # 处理并行模式设置
    if args.single_gpu:
        # 强制单GPU模式
        import config
        config.USE_MULTI_GPU = False
        config.AVAILABLE_GPUS = [config.AVAILABLE_GPUS[0]] if config.AVAILABLE_GPUS else []
        logger.info("强制使用单GPU模式")
    elif args.parallel:
        # 强制四卡并行模式
        import config
        config.USE_MULTI_GPU = True
        if len(config.AVAILABLE_GPUS) < 4:
            logger.warning(f"只有{len(config.AVAILABLE_GPUS)}个GPU可用，无法启用四卡并行")
        else:
            config.AVAILABLE_GPUS = config.AVAILABLE_GPUS[:4]
            logger.info("强制启用四卡并行模式")

    # 显示详细配置摘要
    print_config_summary()

    logger.info(f"运行参数:")
    logger.info(f"  - 执行步骤: {args.step}")
    logger.info(f"  - 相似度阈值: {args.threshold}")
    logger.info(f"  - 并行模式: {'四卡并行' if args.parallel else '单GPU' if args.single_gpu else '自动'}")

    # 创建系统实例
    system = MathSimilaritySystem(threshold=args.threshold)
    
    if args.step == 'all':
        # 运行完整流水线
        results = system.run_complete_pipeline()
    elif args.step == 'vector_db':
        # 只构建向量数据库
        system.step1_build_vector_database()
    elif args.step == 'similarity':
        # 只运行相似度匹配
        similarity_results, _ = system.step2_similarity_matching()
    elif args.step == 'inference':
        # 只运行模型推理（需要先有相似度结果）
        similarity_path = os.path.join(RESULTS_DIR, "similarity_results.json")
        if os.path.exists(similarity_path):
            import json
            with open(similarity_path, 'r', encoding='utf-8') as f:
                similarity_results = json.load(f)
            final_results, _ = system.step3_model_inference(similarity_results)
        else:
            logger.error("相似度结果文件不存在，请先运行相似度匹配")
    elif args.step == 'evaluation':
        # 只运行评估（需要先有最终结果）
        final_path = os.path.join(RESULTS_DIR, "final_results.json")
        similarity_path = os.path.join(RESULTS_DIR, "similarity_results.json")

        if os.path.exists(final_path):
            import json
            with open(final_path, 'r', encoding='utf-8') as f:
                final_results = json.load(f)
            report, _ = system.step4_evaluation(final_results)
        elif os.path.exists(similarity_path):
            # 如果只有相似度结果，先运行模型推理
            logger.info("未找到最终结果文件，先运行模型推理步骤...")
            import json
            with open(similarity_path, 'r', encoding='utf-8') as f:
                similarity_results = json.load(f)
            final_results, _ = system.step3_model_inference(similarity_results)
            report, _ = system.step4_evaluation(final_results)
        else:
            logger.error("结果文件不存在，请先运行前面的步骤")

if __name__ == "__main__":
    main()
