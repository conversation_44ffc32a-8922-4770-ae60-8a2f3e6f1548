# LLaMA-Factory结果分析工具

## 概述

`analyze_llama_factory_results.py` 是一个独立的分析工具，用于分析LLaMA-Factory生成的预测结果并输出详细的评估报告。

## 特性

- **独立运行**: 不依赖config.py中的LLaMA-Factory配置
- **灵活输入**: 支持自定义输入文件路径
- **详细分析**: 提供完整的分类性能指标
- **命令行友好**: 支持命令行参数配置

## 使用方法

### 基本用法

```bash
# 使用默认路径分析
python analyze_llama_factory_results.py

# 指定输入文件
python analyze_llama_factory_results.py --input /path/to/generated_predictions.jsonl

# 指定输出文件
python analyze_llama_factory_results.py --output /path/to/output_report.json

# 指定输出目录
python analyze_llama_factory_results.py --output-dir /path/to/output/directory
```

### 命令行参数

- `--input, -i`: 输入JSONL文件路径
  - 默认: `/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-18-16-54-11/generated_predictions.jsonl`
- `--output, -o`: 输出JSON文件路径
  - 默认: `results/llama_factory_evaluation_report.json`
- `--output-dir`: 输出目录
  - 默认: `./results`

### 输入文件格式

输入文件应为JSONL格式，每行包含一个JSON对象：

```json
{
  "prompt": "题目内容...",
  "label": "真实标签1\n真实标签2\n...",
  "predict": "预测标签1\n预测标签2\n..."
}
```

## 输出报告

分析工具会生成包含以下内容的详细报告：

### 1. 基本信息
- 模型类型和路径
- 评估样本数
- 标签统计信息

### 2. 分类性能指标
- **微平均指标**: 基于总体TP/FP/FN计算
- **宏平均指标**: 各类别指标的平均值
- **完全匹配准确率**: 预测完全正确的样本比例
- **汉明损失**: 多标签分类的损失指标

### 3. 类别分析
- 类别数量分布
- 最常见的叶子节点类别
- 每个类别的详细指标

### 4. 预测源分析
- 不同预测源的性能对比
- 相似度阈值分析

## 示例输出

```
============================================================
LLaMA-Factory模型评估结果摘要
============================================================
模型类型: LLaMA-Factory Fine-tuned Model
模型路径: Qwen3-8B-Thinking/lora
评估样本数: 6047
真实标签叶子节点数: 1262
预测标签路径数: 1333

📊 叶子节点分类性能:
  准确率: 0.6747
  叶子节点类别数: 1461

📈 微平均指标 (基于总体TP/FP/FN):
  微平均 Precision: 0.6747
  微平均 Recall:    0.6747
  微平均 F1:        0.6747

📊 宏平均指标 (各类别指标的平均):
  宏平均 Precision: 0.5346
  宏平均 Recall:    0.5055
  宏平均 F1:        0.4969

🎯 整体分类性能:
  完全匹配准确率: 0.6739
  汉明损失: 0.3253
```

## 注意事项

1. **文件路径**: 确保输入文件路径正确且文件存在
2. **权限**: 确保对输出目录有写入权限
3. **内存**: 大文件分析可能需要较多内存
4. **依赖**: 需要安装相关Python包（pandas, json等）

## 故障排除

### 常见问题

1. **文件不存在错误**
   ```
   ❌ 错误: 输入文件不存在: /path/to/file
   ```
   解决方案: 检查文件路径是否正确

2. **JSON解析错误**
   ```
   警告: 第X行JSON解析失败
   ```
   解决方案: 检查JSONL文件格式是否正确

3. **权限错误**
   ```
   Permission denied
   ```
   解决方案: 检查输出目录的写入权限

## 与主系统的关系

- **独立性**: 此工具完全独立于主相似度匹配系统
- **配置分离**: 不依赖config.py中的LLaMA-Factory配置
- **数据兼容**: 使用相同的评估指标体系，便于结果对比

## 更新日志

- **v1.0**: 初始版本，支持基本的LLaMA-Factory结果分析
- **v1.1**: 添加命令行参数支持，实现配置独立化
