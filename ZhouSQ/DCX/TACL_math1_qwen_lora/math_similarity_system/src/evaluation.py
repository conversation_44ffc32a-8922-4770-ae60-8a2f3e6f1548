"""
评估模块
计算各种评估指标：micro、macro、accuracy、recall等
"""
import json
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report
from sklearn.preprocessing import MultiLabelBinarizer
import pandas as pd
import logging
from typing import List, Dict, Tuple
from collections import defaultdict, Counter

from config import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EvaluationMetrics:
    def __init__(self):
        self.mlb = MultiLabelBinarizer()
        
    def load_results(self, results_path: str) -> List[Dict]:
        """加载预测结果"""
        with open(results_path, 'r', encoding='utf-8') as f:
            results = json.load(f)
        logger.info(f"Loaded {len(results)} prediction results")
        return results
    
    def extract_leaf_labels(self, label_list: List[str]) -> str:
        """提取标签列表中的最末端标签（叶子节点）"""
        if not label_list:
            return ""
        # 返回最后一个标签作为叶子节点
        return label_list[-1]

    def extract_labels(self, results: List[Dict]) -> Tuple[List[List[str]], List[List[str]]]:
        """提取真实标签和预测标签"""
        true_labels = []
        pred_labels = []

        for result in results:
            true_label = result.get('true_label', [])
            pred_label = result.get('predicted_label', [])

            # 处理None值
            if true_label is None:
                true_label = []
            if pred_label is None:
                pred_label = []

            # 确保标签是列表格式
            if isinstance(true_label, str):
                true_label = [true_label]
            if isinstance(pred_label, str):
                pred_label = [pred_label]

            # 确保是列表类型
            if not isinstance(true_label, list):
                true_label = []
            if not isinstance(pred_label, list):
                pred_label = []

            true_labels.append(true_label)
            pred_labels.append(pred_label)

        return true_labels, pred_labels

    def extract_leaf_labels_only(self, results: List[Dict]) -> Tuple[List[str], List[str]]:
        """提取所有结果的叶子节点标签"""
        true_leaf_labels = []
        pred_leaf_labels = []

        for result in results:
            true_label = result.get('true_label', [])
            pred_label = result.get('predicted_label', [])

            true_leaf = self.extract_leaf_labels(true_label)
            pred_leaf = self.extract_leaf_labels(pred_label)

            true_leaf_labels.append(true_leaf)
            pred_leaf_labels.append(pred_leaf)

        return true_leaf_labels, pred_leaf_labels

    def _get_leaf_classes_count(self, results: List[Dict]) -> int:
        """获取叶子节点类别数量"""
        true_leaf_labels, pred_leaf_labels = self.extract_leaf_labels_only(results)
        all_leaf_classes = set(true_leaf_labels + pred_leaf_labels)
        # 移除空字符串
        all_leaf_classes.discard("")
        return len(all_leaf_classes)
    
    def calculate_multilabel_metrics(self, true_labels: List[List[str]], pred_labels: List[List[str]], results: List[Dict] = None) -> Dict:
        """计算多标签分类指标 - 基于叶子节点的正确宏平均和微平均计算"""
        # 对于多标签分类，我们主要关注叶子节点的分类性能
        if results:
            # 如果有results，直接使用叶子节点指标
            leaf_metrics = self.calculate_leaf_node_metrics(results)

            # 计算完全匹配准确率
            exact_match_accuracy = np.mean([
                set(true) == set(pred) for true, pred in zip(true_labels, pred_labels)
            ])

            return {
                'micro_precision': leaf_metrics['leaf_node_micro_precision'],
                'micro_recall': leaf_metrics['leaf_node_micro_recall'],
                'micro_f1': leaf_metrics['leaf_node_micro_f1'],
                'macro_precision': leaf_metrics['leaf_node_macro_precision'],
                'macro_recall': leaf_metrics['leaf_node_macro_recall'],
                'macro_f1': leaf_metrics['leaf_node_macro_f1'],
                'weighted_precision': leaf_metrics['leaf_node_micro_precision'],  # 使用微平均作为加权平均
                'weighted_recall': leaf_metrics['leaf_node_micro_recall'],
                'weighted_f1': leaf_metrics['leaf_node_micro_f1'],
                'exact_match_accuracy': float(exact_match_accuracy),
                'hamming_loss': 1.0 - leaf_metrics['leaf_node_accuracy'],  # 基于叶子节点准确率计算
                'num_samples': len(true_labels),
                'num_classes': leaf_metrics['num_leaf_classes']
            }

        # 如果没有results，使用传统方法（向后兼容）
        # 过滤掉None和空标签，确保所有标签都是有效的列表
        filtered_true_labels = []
        filtered_pred_labels = []

        for true_label, pred_label in zip(true_labels, pred_labels):
            # 确保标签不是None且是列表
            if true_label is None:
                true_label = []
            if pred_label is None:
                pred_label = []
            if not isinstance(true_label, list):
                true_label = []
            if not isinstance(pred_label, list):
                pred_label = []

            filtered_true_labels.append(true_label)
            filtered_pred_labels.append(pred_label)

        # 使用MultiLabelBinarizer转换标签
        all_labels = filtered_true_labels + filtered_pred_labels

        # 过滤掉空的标签列表
        non_empty_labels = [labels for labels in all_labels if labels]

        if not non_empty_labels:
            # 如果没有有效标签，返回默认指标
            logger.warning("No valid labels found, returning default metrics")
            return {
                'micro_precision': 0.0,
                'micro_recall': 0.0,
                'micro_f1': 0.0,
                'macro_precision': 0.0,
                'macro_recall': 0.0,
                'macro_f1': 0.0,
                'weighted_precision': 0.0,
                'weighted_recall': 0.0,
                'weighted_f1': 0.0,
                'hamming_loss': 0.0,
                'exact_match_accuracy': 0.0,
                'num_samples': len(true_labels),
                'num_classes': 0
            }

        self.mlb.fit(all_labels)

        y_true = self.mlb.transform(filtered_true_labels)
        y_pred = self.mlb.transform(filtered_pred_labels)

        # 计算各种指标
        precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
            y_true, y_pred, average='micro', zero_division=0
        )

        precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
            y_true, y_pred, average='macro', zero_division=0
        )

        precision_weighted, recall_weighted, f1_weighted, _ = precision_recall_fscore_support(
            y_true, y_pred, average='weighted', zero_division=0
        )

        # 计算准确率（完全匹配）
        exact_match_accuracy = np.mean([
            set(true) == set(pred) for true, pred in zip(true_labels, pred_labels)
        ])

        # 计算Hamming Loss
        hamming_loss = np.mean(y_true != y_pred)

        metrics = {
            'micro_precision': float(precision_micro),
            'micro_recall': float(recall_micro),
            'micro_f1': float(f1_micro),
            'macro_precision': float(precision_macro),
            'macro_recall': float(recall_macro),
            'macro_f1': float(f1_macro),
            'weighted_precision': float(precision_weighted),
            'weighted_recall': float(recall_weighted),
            'weighted_f1': float(f1_weighted),
            'exact_match_accuracy': float(exact_match_accuracy),
            'hamming_loss': float(hamming_loss),
            'num_samples': len(true_labels),
            'num_classes': len(self.mlb.classes_)
        }

        return metrics

    def calculate_leaf_node_metrics(self, results: List[Dict]) -> Dict:
        """计算叶子节点（最末端类别）的分类指标"""
        true_leaf_labels, pred_leaf_labels = self.extract_leaf_labels_only(results)

        # 过滤掉空标签
        filtered_pairs = [(true, pred) for true, pred in zip(true_leaf_labels, pred_leaf_labels)
                         if true and pred]

        if not filtered_pairs:
            return {
                'leaf_node_accuracy': 0.0,
                'leaf_node_precision': 0.0,
                'leaf_node_recall': 0.0,
                'leaf_node_f1': 0.0,
                'leaf_node_macro_precision': 0.0,
                'leaf_node_macro_recall': 0.0,
                'leaf_node_macro_f1': 0.0,
                'leaf_node_micro_precision': 0.0,
                'leaf_node_micro_recall': 0.0,
                'leaf_node_micro_f1': 0.0,
                'num_leaf_classes': 0,
                'leaf_class_distribution': {}
            }

        filtered_true, filtered_pred = zip(*filtered_pairs)

        # 计算叶子节点准确率
        leaf_accuracy = sum(1 for true, pred in zip(filtered_true, filtered_pred) if true == pred) / len(filtered_pairs)

        # 获取所有唯一的叶子节点类别
        all_leaf_classes = sorted(set(filtered_true + filtered_pred))

        # 手动计算每个类别的指标
        class_metrics = {}
        total_tp = 0  # 总的真正例
        total_fp = 0  # 总的假正例
        total_fn = 0  # 总的假负例

        for cls in all_leaf_classes:
            # 计算该类别的TP, FP, FN
            tp = sum(1 for true, pred in zip(filtered_true, filtered_pred) if true == cls and pred == cls)
            fp = sum(1 for true, pred in zip(filtered_true, filtered_pred) if true != cls and pred == cls)
            fn = sum(1 for true, pred in zip(filtered_true, filtered_pred) if true == cls and pred != cls)

            # 计算该类别的precision, recall, f1
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
            support = sum(1 for true in filtered_true if true == cls)

            class_metrics[cls] = {
                'precision': precision,
                'recall': recall,
                'f1-score': f1,
                'support': support
            }

            # 累加用于微平均计算
            total_tp += tp
            total_fp += fp
            total_fn += fn

        # 计算宏平均（各类别指标的简单平均）
        macro_precision = sum(metrics['precision'] for metrics in class_metrics.values()) / len(class_metrics) if class_metrics else 0.0
        macro_recall = sum(metrics['recall'] for metrics in class_metrics.values()) / len(class_metrics) if class_metrics else 0.0
        macro_f1 = sum(metrics['f1-score'] for metrics in class_metrics.values()) / len(class_metrics) if class_metrics else 0.0

        # 计算微平均（基于总的TP, FP, FN）
        micro_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
        micro_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
        micro_f1 = 2 * micro_precision * micro_recall / (micro_precision + micro_recall) if (micro_precision + micro_recall) > 0 else 0.0

        # 统计叶子类别分布
        from collections import Counter
        true_distribution = Counter(filtered_true)
        pred_distribution = Counter(filtered_pred)

        class_distribution = {}
        for cls in all_leaf_classes:
            class_distribution[cls] = {
                'true_count': true_distribution.get(cls, 0),
                'pred_count': pred_distribution.get(cls, 0),
                'support': true_distribution.get(cls, 0)
            }

        return {
            'leaf_node_accuracy': float(leaf_accuracy),
            'leaf_node_precision': float(micro_precision),  # 保持向后兼容，使用微平均
            'leaf_node_recall': float(micro_recall),
            'leaf_node_f1': float(micro_f1),
            'leaf_node_macro_precision': float(macro_precision),
            'leaf_node_macro_recall': float(macro_recall),
            'leaf_node_macro_f1': float(macro_f1),
            'leaf_node_micro_precision': float(micro_precision),
            'leaf_node_micro_recall': float(micro_recall),
            'leaf_node_micro_f1': float(micro_f1),
            'num_leaf_classes': len(all_leaf_classes),
            'leaf_class_distribution': class_distribution,
            'leaf_class_report': class_metrics  # 使用我们手动计算的指标
        }
    
    def calculate_prediction_source_metrics(self, results: List[Dict]) -> Dict:
        """计算不同预测源的统计信息"""
        source_stats = defaultdict(int)
        source_accuracy = defaultdict(list)
        
        for result in results:
            source = result.get('prediction_source', 'unknown')
            source_stats[source] += 1
            
            # 计算该样本的准确性
            true_label = set(result.get('true_label', []))
            pred_label = set(result.get('predicted_label', []))
            accuracy = 1.0 if true_label == pred_label else 0.0
            source_accuracy[source].append(accuracy)
        
        # 计算每个源的平均准确率
        source_metrics = {}
        for source in source_stats:
            source_metrics[source] = {
                'count': source_stats[source],
                'percentage': source_stats[source] / len(results) * 100,
                'accuracy': np.mean(source_accuracy[source]) if source_accuracy[source] else 0.0
            }
        
        return source_metrics
    
    def calculate_similarity_threshold_analysis(self, results: List[Dict]) -> Dict:
        """分析相似度阈值的效果"""
        above_threshold = [r for r in results if r.get('above_threshold', False)]
        below_threshold = [r for r in results if not r.get('above_threshold', False)]
        
        analysis = {
            'above_threshold_count': len(above_threshold),
            'below_threshold_count': len(below_threshold),
            'threshold_value': SIMILARITY_THRESHOLD
        }
        
        if above_threshold:
            above_scores = [r.get('similarity_score', 0) for r in above_threshold]
            analysis['above_threshold_avg_similarity'] = np.mean(above_scores)
            analysis['above_threshold_min_similarity'] = np.min(above_scores)
            analysis['above_threshold_max_similarity'] = np.max(above_scores)
        
        if below_threshold:
            below_scores = [r.get('similarity_score', 0) for r in below_threshold]
            analysis['below_threshold_avg_similarity'] = np.mean(below_scores)
            analysis['below_threshold_min_similarity'] = np.min(below_scores)
            analysis['below_threshold_max_similarity'] = np.max(below_scores)
        
        return analysis

    def calculate_prediction_accuracy(self, results: List[Dict]) -> Dict:
        """计算预测成功率统计（包括topk匹配）"""
        total_samples = len(results)
        correct_predictions = sum(1 for r in results if r.get('prediction_correct', False))

        # 按预测源分组统计
        vector_db_correct = sum(1 for r in results
                               if r.get('prediction_source') == 'vector_db' and r.get('prediction_correct', False))
        vector_db_total = sum(1 for r in results if r.get('prediction_source') == 'vector_db')

        model_correct = sum(1 for r in results
                           if r.get('prediction_source') == 'fine_tuned_model' and r.get('prediction_correct', False))
        model_total = sum(1 for r in results if r.get('prediction_source') == 'fine_tuned_model')

        # 计算传统的top1匹配（仅使用最相似的结果）
        top1_correct = 0
        for r in results:
            if r.get('prediction_source') == 'vector_db':
                # 对于向量库结果，检查最相似的是否匹配
                true_label = r.get('true_label', [])
                predicted_label = r.get('predicted_label', [])
                if set(true_label or []) == set(predicted_label or []):
                    top1_correct += 1
            elif r.get('prediction_correct', False):
                # 对于微调模型结果，直接使用prediction_correct
                top1_correct += 1

        return {
            'overall_accuracy_topk': correct_predictions / total_samples if total_samples > 0 else 0.0,
            'overall_accuracy_top1': top1_correct / total_samples if total_samples > 0 else 0.0,
            'total_correct_topk': correct_predictions,
            'total_correct_top1': top1_correct,
            'total_samples': total_samples,
            'vector_db_accuracy_topk': vector_db_correct / vector_db_total if vector_db_total > 0 else 0.0,
            'vector_db_correct': vector_db_correct,
            'vector_db_total': vector_db_total,
            'model_accuracy': model_correct / model_total if model_total > 0 else 0.0,
            'model_correct': model_correct,
            'model_total': model_total,
            'improvement_topk_vs_top1': (correct_predictions - top1_correct) / total_samples if total_samples > 0 else 0.0
        }

    def generate_detailed_report(self, results: List[Dict]) -> Dict:
        """生成详细的评估报告"""
        logger.info("Generating detailed evaluation report...")
        
        # 提取标签
        true_labels, pred_labels = self.extract_labels(results)
        
        # 计算多标签指标
        multilabel_metrics = self.calculate_multilabel_metrics(true_labels, pred_labels, results)
        
        # 计算预测源统计
        source_metrics = self.calculate_prediction_source_metrics(results)
        
        # 相似度阈值分析
        threshold_analysis = self.calculate_similarity_threshold_analysis(results)

        # 预测成功率统计
        prediction_accuracy = self.calculate_prediction_accuracy(results)

        # 计算叶子节点指标
        leaf_node_metrics = self.calculate_leaf_node_metrics(results)

        # 注意：现在主要关注叶子节点的分类指标，不再生成完整的多标签分类报告

        report = {
            'overall_metrics': multilabel_metrics,
            'prediction_source_metrics': source_metrics,
            'similarity_threshold_analysis': threshold_analysis,
            'prediction_accuracy': prediction_accuracy,
            'leaf_node_metrics': leaf_node_metrics,  # 新增叶子节点指标
            'per_class_metrics': leaf_node_metrics.get('leaf_class_report', {}),  # 叶子节点的详细分类报告
            'evaluation_summary': {
                'total_samples': len(results),
                'unique_true_labels': len(set([tuple(sorted(labels)) for labels in true_labels])),
                'unique_pred_labels': len(set([tuple(sorted(labels)) for labels in pred_labels])),
                'avg_similarity_score': np.mean([r.get('similarity_score', 0) for r in results]),
                'num_classes': leaf_node_metrics.get('num_leaf_classes', 0)  # 只统计叶子节点类别数
            }
        }
        
        return report
    
    def save_evaluation_report(self, report: Dict, filename: str = None):
        """保存评估报告"""
        if filename is None:
            filename = EVALUATION_REPORT_FILENAME  # 使用配置文件中的默认文件名
        output_path = os.path.join(RESULTS_DIR, filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Evaluation report saved to {output_path}")
        return output_path
    
    def print_summary(self, report: Dict):
        """打印评估摘要"""
        print("\n" + "="*60)
        print("数学题目相似度匹配系统 - 评估报告")
        print("="*60)

        overall = report['overall_metrics']
        print(f"\n整体性能指标:")
        print(f"  Micro F1-Score:     {overall['micro_f1']:.4f}")
        print(f"  Macro F1-Score:     {overall['macro_f1']:.4f}")
        print(f"  Weighted F1-Score:  {overall['weighted_f1']:.4f}")
        print(f"  完全匹配准确率:      {overall['exact_match_accuracy']:.4f}")
        print(f"  Hamming Loss:       {overall['hamming_loss']:.4f}")

        # 显示叶子节点的详细指标
        leaf_metrics = report.get('leaf_node_metrics', {})
        if leaf_metrics:
            print(f"\n叶子节点分类指标:")
            print(f"  叶子节点准确率:      {leaf_metrics.get('leaf_node_accuracy', 0):.4f}")
            print(f"  微平均 Precision:   {leaf_metrics.get('leaf_node_micro_precision', 0):.4f}")
            print(f"  微平均 Recall:      {leaf_metrics.get('leaf_node_micro_recall', 0):.4f}")
            print(f"  微平均 F1-Score:    {leaf_metrics.get('leaf_node_micro_f1', 0):.4f}")
            print(f"  宏平均 Precision:   {leaf_metrics.get('leaf_node_macro_precision', 0):.4f}")
            print(f"  宏平均 Recall:      {leaf_metrics.get('leaf_node_macro_recall', 0):.4f}")
            print(f"  宏平均 F1-Score:    {leaf_metrics.get('leaf_node_macro_f1', 0):.4f}")
            print(f"  叶子节点类别数:      {leaf_metrics.get('num_leaf_classes', 0)}")

        source_metrics = report['prediction_source_metrics']
        print(f"\n预测源统计:")
        for source, metrics in source_metrics.items():
            print(f"  {source}:")
            print(f"    样本数量: {metrics['count']} ({metrics['percentage']:.1f}%)")
            print(f"    准确率:   {metrics['accuracy']:.4f}")

        threshold = report['similarity_threshold_analysis']
        print(f"\n相似度阈值分析 (阈值: {threshold['threshold_value']}):")
        print(f"  高于阈值: {threshold['above_threshold_count']} 样本")
        print(f"  低于阈值: {threshold['below_threshold_count']} 样本")

        if 'above_threshold_avg_similarity' in threshold:
            print(f"  高于阈值平均相似度: {threshold['above_threshold_avg_similarity']:.4f}")
        if 'below_threshold_avg_similarity' in threshold:
            print(f"  低于阈值平均相似度: {threshold['below_threshold_avg_similarity']:.4f}")

        print("="*60)

def test_macro_micro_calculation():
    """测试宏平均和微平均计算的正确性"""
    print("\n" + "="*60)
    print("测试宏平均和微平均计算")
    print("="*60)

    # 创建测试数据：100个题目，A类99个（99个正确），B类1个（0个正确）
    test_results = []

    # A类：99个题目，99个正确
    for _ in range(99):
        test_results.append({
            'true_label': ['A'],
            'predicted_label': ['A'],
            'prediction_source': 'test',
            'similarity_score': 1.0,
            'above_threshold': True,
            'prediction_correct': True
        })

    # B类：1个题目，0个正确
    test_results.append({
        'true_label': ['B'],
        'predicted_label': ['A'],  # 预测错误
        'prediction_source': 'test',
        'similarity_score': 1.0,
        'above_threshold': True,
        'prediction_correct': False
    })

    evaluator = EvaluationMetrics()
    leaf_metrics = evaluator.calculate_leaf_node_metrics(test_results)

    print(f"测试数据：100个题目")
    print(f"  A类：99个真实，100个预测（99正确+1错误） → precision=99/100=0.99, recall=99/99=1.0")
    print(f"  B类：1个真实，0个预测 → precision=0/0=0.0, recall=0/1=0.0")
    print(f"\n计算结果：")
    print(f"  微平均 = 总正确数/总题目数 = 99/100 = {leaf_metrics['leaf_node_micro_precision']:.4f}")
    print(f"  宏平均 = (0.99 + 0.0)/2 = {leaf_metrics['leaf_node_macro_precision']:.4f}")
    print(f"\n验证：")
    print(f"  微平均是否正确: {abs(leaf_metrics['leaf_node_micro_precision'] - 0.99) < 0.001}")
    print(f"  宏平均是否正确: {abs(leaf_metrics['leaf_node_macro_precision'] - 0.495) < 0.001}")
    print("="*60)

if __name__ == "__main__":
    # 先运行测试
    test_macro_micro_calculation()

    # 运行评估
    evaluator = EvaluationMetrics()

    # 加载最终结果
    results_path = os.path.join(RESULTS_DIR, "similarity_results.json")
    if os.path.exists(results_path):
        results = evaluator.load_results(results_path)

        # 生成评估报告
        report = evaluator.generate_detailed_report(results)

        # 保存报告
        evaluator.save_evaluation_report(report)

        # 打印摘要
        evaluator.print_summary(report)
    else:
        logger.error("Final results not found. Please run the complete pipeline first.")
