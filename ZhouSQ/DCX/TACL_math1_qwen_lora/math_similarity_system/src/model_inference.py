"""
微调模型推理模块
实现LoRA模型加载和推理功能
"""
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import logging
from typing import List, Dict
import json
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm

from config import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FineTunedModelInference:
    def __init__(self):
        self.tokenizer = None
        self.model = None
        self.device = PRIMARY_DEVICE
        self.use_multi_gpu = USE_MULTI_GPU
        
    def load_model(self):
        """加载基础模型和LoRA适配器"""
        try:
            logger.info(f"Loading base model from {BASE_MODEL_PATH}")
            logger.info(f"Using device: {self.device}")

            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                BASE_MODEL_PATH,
                trust_remote_code=True,
                padding_side="left"
            )

            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # 加载基础模型
            if self.use_multi_gpu and len(AVAILABLE_GPUS) > 1:
                # 多GPU模式，使用device_map自动分配
                device_map = "auto"
                logger.info(f"Using multi-GPU setup with device_map=auto")
            else:
                # 单GPU模式
                device_map = None

            base_model = AutoModelForCausalLM.from_pretrained(
                BASE_MODEL_PATH,
                torch_dtype=torch.float16 if "cuda" in self.device else torch.float32,
                device_map=device_map,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            # 加载LoRA适配器
            logger.info(f"Loading LoRA adapter from {LORA_MODEL_PATH}")
            self.model = PeftModel.from_pretrained(
                base_model,
                LORA_MODEL_PATH,
                torch_dtype=torch.float16 if "cuda" in self.device else torch.float32
            )

            # 合并LoRA权重到基础模型
            logger.info("Merging LoRA weights...")
            self.model = self.model.merge_and_unload()

            # 如果不是多GPU模式，手动移动到指定设备
            if not self.use_multi_gpu and "cuda" in self.device:
                self.model = self.model.to(self.device)

            self.model.eval()
            logger.info("Model loaded and merged successfully")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def format_prompt(self, question: str) -> str:
        """格式化输入提示"""
        # 根据训练时使用的格式来构建提示
        prompt = f"""请根据给定的数学题目内容，从小学数学知识树中选择与该题目最相关的9个标签。标签应该按照从宏观到具体的层次结构排列，包括学科分类、知识领域、题目类型、具体知识点等。
        数学题目内容为：{question}"""
        return prompt
    
    def generate_response(self, question: str, max_length: int = 1024) -> str:
        """生成模型回答"""
        if self.model is None:
            self.load_model()
        
        prompt = self.format_prompt(question)
        
        # Tokenize输入
        inputs = self.tokenizer(
            prompt,
            return_tensors="pt",
            max_length=MAX_LENGTH,
            truncation=True,
            padding=True
        )
        
        if "cuda" in self.device:
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # 生成回答
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length - inputs['input_ids'].shape[1],
                do_sample=True,
                temperature=0.01,
                top_p=0.9,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # 解码输出
        response = self.tokenizer.decode(
            outputs[0][inputs['input_ids'].shape[1]:],
            skip_special_tokens=True
        )
        
        return response.strip()
    
    def batch_inference(self, questions: List[str], batch_size: int = 16) -> List[str]:
        """批量推理"""
        if self.model is None:
            self.load_model()
        
        responses = []
        
        for i in range(0, len(questions), batch_size):
            batch_questions = questions[i:i + batch_size]
            batch_responses = []
            
            for question in batch_questions:
                try:
                    response = self.generate_response(question)
                    batch_responses.append(response)
                except Exception as e:
                    logger.error(f"Error generating response for question: {e}")
                    batch_responses.append(f"生成回答时出错: {str(e)}")
            
            responses.extend(batch_responses)
            logger.info(f"Processed {min(i + batch_size, len(questions))}/{len(questions)} questions")
        
        return responses
    
    def process_similarity_results(self, similarity_results: List[Dict]) -> List[Dict]:
        """处理相似度匹配结果，为需要模型推理的项目生成答案"""
        # 找出需要模型推理的项目
        need_inference = [
            result for result in similarity_results 
            if result.get('needs_model_inference', False)
        ]
        
        if not need_inference:
            logger.info("No items need model inference")
            return similarity_results
        
        logger.info(f"Processing {len(need_inference)} items with fine-tuned model")
        
        # 提取问题
        questions = [item['query'] for item in need_inference]
        
        # 批量推理
        responses = self.batch_inference(questions)
        
        # 更新结果
        inference_idx = 0
        for result in similarity_results:
            if result.get('needs_model_inference', False):
                result['predicted_label'] = self.extract_label_from_response(responses[inference_idx])
                del result['needs_model_inference']  # 清理标记
                inference_idx += 1

                # 更新预测成功判断
                from similarity_matcher import SimilarityMatcher
                matcher = SimilarityMatcher()
                result['prediction_correct'] = matcher.is_prediction_correct(
                    result['true_label'],
                    result['predicted_label']
                )
        
        return similarity_results
    
    def extract_label_from_response(self, response: str) -> List[str]:
        """从模型回答中提取标签（简单实现）"""
        # 这里可以实现更复杂的标签提取逻辑
        # 目前返回一个通用标签
        return ["模型生成答案"]
    
    def save_final_results(self, results: List[Dict], filename: str = "final_results.json"):
        """保存最终结果"""
        output_path = os.path.join(RESULTS_DIR, filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Final results saved to {output_path}")
        return output_path

if __name__ == "__main__":
    # 测试模型推理
    inference = FineTunedModelInference()
    
    # 加载相似度结果
    similarity_results_path = os.path.join(RESULTS_DIR, "similarity_results.json")
    if os.path.exists(similarity_results_path):
        with open(similarity_results_path, 'r', encoding='utf-8') as f:
            similarity_results = json.load(f)
        
        # 处理需要模型推理的项目
        final_results = inference.process_similarity_results(similarity_results)
        
        # 保存最终结果
        inference.save_final_results(final_results)
    else:
        logger.error("Similarity results not found. Please run similarity matching first.")
