"""
向量数据库构建模块
使用Qwen3-Embedding-4B模型构建数学题目的向量数据库
"""
import json
import os
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
from tqdm import tqdm
import pickle
import logging
import torch
import gc
from typing import List, Dict, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

from config import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def aggressive_memory_cleanup():
    """激进的内存清理"""
    if torch.cuda.is_available():
        # 多次清理GPU内存
        for _ in range(5):
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

        # 清理Python内存
        gc.collect()

        # 显示内存状态
        # for i in range(torch.cuda.device_count()):
        #     memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
        #     memory_reserved = torch.cuda.memory_reserved(i) / 1024**3
        #     logger.info(f"GPU {i} memory: {memory_allocated:.2f}GB allocated, {memory_reserved:.2f}GB reserved")

class VectorDatabase:
    def __init__(self):
        self.models = {}  # 存储多个GPU上的模型
        self.index = None
        self.metadata = []
        self.lock = threading.Lock()  # 线程锁

    def load_embedding_model(self):
        """加载Qwen3-Embedding模型到多个GPU"""
        try:
            logger.info(f"Loading embedding model from {EMBEDDING_MODEL_PATH}")
            logger.info(f"Available GPUs: {AVAILABLE_GPUS}")

            # 强制多GPU模式：为每个GPU加载一个模型实例
            successful_gpus = []
            for gpu_id in AVAILABLE_GPUS:
                device = f"cuda:{gpu_id}"
                try:
                    logger.info(f"Loading model on {device}")
                    # 先清理GPU内存
                    torch.cuda.empty_cache()
                    if gpu_id in [0, 1, 2, 3]:  # 确保GPU存在
                        torch.cuda.set_device(gpu_id)
                        torch.cuda.empty_cache()

                    # 使用更节省内存的方式加载模型
                    model = SentenceTransformer(EMBEDDING_MODEL_PATH, device='cpu')
                    model = model.to(device)
                    model.eval()  # 设置为评估模式以节省内存

                    self.models[gpu_id] = model
                    successful_gpus.append(gpu_id)
                    logger.info(f"Successfully loaded model on {device}")
                except Exception as e:
                    logger.warning(f"Failed to load model on {device}: {e}")
                    continue

            if not successful_gpus:
                raise RuntimeError("Failed to load model on any GPU")

            logger.info(f"Successfully loaded models on GPUs: {successful_gpus}")
            logger.info(f"Embedding model loaded successfully on {len(self.models)} GPUs")
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            raise

    def process_batch_on_gpu(self, gpu_id: int, batch_texts: List[str], batch_id: int) -> Tuple[int, np.ndarray]:
        """在指定GPU上处理一个批次"""
        try:
            model = self.models[gpu_id]
            embeddings = model.encode(
                batch_texts,
                batch_size=len(batch_texts),
                show_progress_bar=False,
                convert_to_numpy=True
            )

            # GPU内存清理
            torch.cuda.empty_cache()

            return batch_id, embeddings

        except Exception as e:
            logger.error(f"Error processing batch {batch_id} on GPU {gpu_id}: {e}")
            raise
    
    def load_train_data(self) -> List[Dict]:
        """加载训练数据"""
        logger.info(f"Loading training data from {TRAIN_DATA_PATH}")
        with open(TRAIN_DATA_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"Loaded {len(data)} training samples")
        return data
    
    def create_embeddings(self, texts: List[str]) -> np.ndarray:
        """使用四卡并行创建文本嵌入向量"""
        logger.info(f"Creating embeddings for {len(texts)} texts using {len(AVAILABLE_GPUS)} GPUs")

        if not USE_MULTI_GPU or len(AVAILABLE_GPUS) <= 1:
            # 单GPU模式
            return self._create_embeddings_single_gpu(texts)

        # 四卡并行模式
        return self._create_embeddings_multi_gpu(texts)

    def _create_embeddings_single_gpu(self, texts: List[str]) -> np.ndarray:
        """单GPU创建嵌入向量（备用方案）"""
        logger.info("Using single GPU mode")
        model = self.models[AVAILABLE_GPUS[0]]

        effective_batch_size = min(EMBEDDING_BATCH_SIZE, 8)
        embeddings = []

        for i in tqdm(range(0, len(texts), effective_batch_size), desc="Creating embeddings"):
            batch_texts = texts[i:i + effective_batch_size]
            try:
                batch_embeddings = model.encode(
                    batch_texts,
                    batch_size=effective_batch_size,
                    show_progress_bar=False,
                    convert_to_numpy=True
                )
                embeddings.append(batch_embeddings)
                aggressive_memory_cleanup()
            except Exception as e:
                logger.error(f"Error in single GPU processing: {e}")
                raise

        return np.vstack(embeddings) if embeddings else np.array([])

    def _create_embeddings_multi_gpu(self, texts: List[str]) -> np.ndarray:
        """四卡并行创建嵌入向量"""
        logger.info(f"Using multi-GPU mode with {len(AVAILABLE_GPUS)} GPUs")

        # 计算每个GPU的批次大小
        num_gpus = len(AVAILABLE_GPUS)
        gpu_batch_size = max(1, EMBEDDING_BATCH_SIZE)  # 每个GPU的批次大小
        total_batch_size = gpu_batch_size * num_gpus  # 总批次大小

        logger.info(f"GPU batch size: {gpu_batch_size}, Total batch size: {total_batch_size}")

        # 准备批次数据
        batches = []
        for i in range(0, len(texts), total_batch_size):
            # 将大批次分割为多个GPU批次
            batch_texts = texts[i:i + total_batch_size]

            # 为每个GPU分配子批次
            for gpu_idx, gpu_id in enumerate(AVAILABLE_GPUS):
                start_idx = gpu_idx * gpu_batch_size
                end_idx = min(start_idx + gpu_batch_size, len(batch_texts))

                if start_idx < len(batch_texts):
                    gpu_batch = batch_texts[start_idx:end_idx]
                    batch_id = len(batches)
                    batches.append((gpu_id, gpu_batch, batch_id))

        logger.info(f"Created {len(batches)} batches for parallel processing")

        # 并行处理所有批次
        embeddings_dict = {}

        with ThreadPoolExecutor(max_workers=num_gpus) as executor:
            # 提交所有任务
            future_to_batch = {
                executor.submit(self.process_batch_on_gpu, gpu_id, batch_texts, batch_id): batch_id
                for gpu_id, batch_texts, batch_id in batches
            }

            # 收集结果
            with tqdm(total=len(batches), desc="Processing batches on multiple GPUs") as pbar:
                for future in as_completed(future_to_batch):
                    try:
                        batch_id, batch_embeddings = future.result()
                        embeddings_dict[batch_id] = batch_embeddings
                        pbar.update(1)
                    except Exception as e:
                        batch_id = future_to_batch[future]
                        logger.error(f"Batch {batch_id} failed: {e}")
                        raise

        # 按顺序合并嵌入向量
        embeddings_list = []
        for batch_id in sorted(embeddings_dict.keys()):
            embeddings_list.append(embeddings_dict[batch_id])

        if embeddings_list:
            all_embeddings = np.vstack(embeddings_list)
            logger.info(f"Created embeddings with shape: {all_embeddings.shape}")

            # 最终内存清理
            aggressive_memory_cleanup()

            return all_embeddings
        else:
            logger.error("No embeddings were created")
            raise ValueError("Failed to create any embeddings")
    
    def build_faiss_index(self, embeddings: np.ndarray):
        """构建FAISS索引"""
        logger.info(f"Building FAISS index for {embeddings.shape[0]} vectors")
        dimension = embeddings.shape[1]
        
        # 使用IndexFlatIP进行余弦相似度搜索
        self.index = faiss.IndexFlatIP(dimension)
        
        # 归一化向量以使用余弦相似度
        faiss.normalize_L2(embeddings)
        self.index.add(embeddings.astype(np.float32))
        
        logger.info(f"FAISS index built with {self.index.ntotal} vectors")
    
    def save_database(self):
        """保存向量数据库和元数据"""
        logger.info("Saving vector database...")
        
        # 保存FAISS索引
        faiss.write_index(self.index, VECTOR_INDEX_PATH)
        
        # 保存元数据
        with open(METADATA_PATH, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        
        logger.info("Vector database saved successfully")
    
    def load_database(self):
        """加载已保存的向量数据库"""
        if os.path.exists(VECTOR_INDEX_PATH) and os.path.exists(METADATA_PATH):
            logger.info("Loading existing vector database...")
            self.index = faiss.read_index(VECTOR_INDEX_PATH)
            
            with open(METADATA_PATH, 'r', encoding='utf-8') as f:
                self.metadata = json.load(f)
            
            logger.info(f"Loaded vector database with {len(self.metadata)} entries")
            return True
        return False
    
    def build_database(self):
        """构建完整的向量数据库"""
        # 检查是否已存在数据库
        if self.load_database():
            logger.info("Vector database already exists, skipping build")
            return

        # 预清理内存
        logger.info("Pre-cleaning memory before building database...")
        aggressive_memory_cleanup()

        # 加载模型和数据
        self.load_embedding_model()
        train_data = self.load_train_data()
        
        # 准备文本和元数据
        texts = []
        self.metadata = []
        
        for i, item in enumerate(train_data):
            text = item.get('doc_token', '')
            label = item.get('doc_label', [])
            
            texts.append(text)
            self.metadata.append({
                'id': i,
                'text': text,
                'label': label,
                'original_data': item
            })
        
        # 创建嵌入向量
        embeddings = self.create_embeddings(texts)
        
        # 构建FAISS索引
        self.build_faiss_index(embeddings)
        
        # 保存数据库
        self.save_database()
        
        logger.info("Vector database build completed")
    
    def search_similar(self, query_text: str, top_k: int = None, gpu_id: int = None) -> List[Tuple[float, Dict]]:
        """搜索相似的题目"""
        if top_k is None:
            top_k = VECTOR_SEARCH_TOP_K  # 使用配置文件中的默认值
        if not self.models:
            self.load_embedding_model()

        if self.index is None:
            self.load_database()

        # 选择使用哪个GPU的模型
        if gpu_id is not None and gpu_id in self.models:
            model = self.models[gpu_id]
        else:
            # 默认使用第一个可用GPU的模型
            model = self.models[AVAILABLE_GPUS[0]]

        # 创建查询向量
        query_embedding = model.encode([query_text], convert_to_numpy=True)
        faiss.normalize_L2(query_embedding)

        # 搜索相似向量
        scores, indices = self.index.search(query_embedding.astype(np.float32), top_k)

        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx < len(self.metadata):
                results.append((float(score), self.metadata[idx]))

        return results

if __name__ == "__main__":
    # 构建向量数据库
    db = VectorDatabase()
    db.build_database()
