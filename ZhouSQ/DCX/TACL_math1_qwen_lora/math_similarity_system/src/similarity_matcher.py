"""
相似度匹配模块
实现测试集题目与向量库的相似度计算和阈值判断
"""
import json
import logging
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
try:
    from .vector_database import VectorDatabase
except ImportError:
    from vector_database import VectorDatabase
from config import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimilarityMatcher:
    def __init__(self, threshold: float = SIMILARITY_THRESHOLD):
        self.threshold = threshold
        self.vector_db = VectorDatabase()
        # 尝试加载数据库，如果不存在则构建
        if not self.vector_db.load_database():
            logger.info("Vector database not found, building new one...")
            self.vector_db.build_database()
        
    def load_test_data(self) -> List[Dict]:
        """加载测试数据"""
        logger.info(f"Loading test data from {TEST_DATA_PATH}")
        with open(TEST_DATA_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"Loaded {len(data)} test samples")
        return data
    
    def find_most_similar(self, query_text: str, top_k: int = None, gpu_id: int = None) -> List[Tuple[float, Dict]]:
        """找到最相似的题目（返回top_k个结果）"""
        if top_k is None:
            top_k = TOP_K  # 使用配置文件中的默认值
        results = self.vector_db.search_similar(query_text, top_k=top_k, gpu_id=gpu_id)
        return results if results else []
    
    def is_above_threshold(self, similarity_score: float) -> bool:
        """判断相似度是否超过阈值"""
        return similarity_score >= self.threshold
    
    def is_prediction_correct(self, true_label: List[str], predicted_label: List[str]) -> bool:
        """判断预测是否完全正确（标签完全一致）"""
        if true_label is None:
            true_label = []
        if predicted_label is None:
            predicted_label = []

        # 转换为集合进行比较，忽略顺序
        return set(true_label) == set(predicted_label)

    def is_prediction_correct_topk(self, true_label: List[str], topk_results: List[Tuple[float, Dict]]) -> bool:
        """判断topk结果中是否有与真实标签完全一致的"""
        if true_label is None:
            true_label = []

        true_label_set = set(true_label)

        for score, metadata in topk_results:
            predicted_label = metadata.get('label', [])
            if predicted_label is None:
                predicted_label = []

            predicted_label_set = set(predicted_label)
            if true_label_set == predicted_label_set:
                return True

        return False
    
    def process_test_item(self, test_item: Dict, gpu_id: int = None) -> Dict:
        """处理单个测试项目"""
        query_text = test_item.get('doc_token', '')
        true_label = test_item.get('doc_label', [])

        # 查找topk最相似的题目
        topk_results = self.find_most_similar(query_text, top_k=TOP_K, gpu_id=gpu_id)

        # 获取最相似的结果（用于阈值判断）
        if topk_results:
            similarity_score = topk_results[0][0]  # 最高相似度分数
            most_similar_metadata = topk_results[0][1]  # 最相似的元数据
        else:
            similarity_score = 0.0
            most_similar_metadata = None

        result = {
            'query': query_text,
            'true_label': true_label,
            'similarity_score': similarity_score,
            'above_threshold': self.is_above_threshold(similarity_score),
            'prediction_source': None,
            'predicted_label': None,
            'topk_results': topk_results  # 保存topk结果用于分析
        }

        if self.is_above_threshold(similarity_score) and most_similar_metadata:
            # 相似度超过阈值，使用向量库中的结果（仍然使用最相似的作为预测）
            result['prediction_source'] = 'vector_db'
            result['predicted_label'] = most_similar_metadata.get('label', [])

            # 使用topk匹配判断预测成功
            result['prediction_correct'] = self.is_prediction_correct_topk(
                result['true_label'],
                topk_results
            )
        else:
            # 相似度不够，需要使用微调模型
            result['prediction_source'] = 'fine_tuned_model'
            result['needs_model_inference'] = True

            # 对于微调模型的结果，仍使用精确匹配
            result['prediction_correct'] = self.is_prediction_correct(
                result['true_label'],
                result['predicted_label']
            )

        return result
    
    def process_batch(self, batch_data: List[Tuple[int, Dict]], gpu_id: int = None) -> List[Tuple[int, Dict]]:
        """处理一个批次的测试数据"""
        batch_results = []
        for idx, test_item in batch_data:
            result = self.process_test_item(test_item, gpu_id=gpu_id)
            batch_results.append((idx, result))
        return batch_results

    def process_all_test_data(self) -> List[Dict]:
        """使用四卡并行处理所有测试数据"""
        test_data = self.load_test_data()

        logger.info(f"Processing {len(test_data)} test items with threshold {self.threshold}")
        logger.info(f"Using {len(AVAILABLE_GPUS) if USE_MULTI_GPU else 1} workers for parallel processing")

        # 预先加载模型到所有GPU以避免多线程竞争
        if not self.vector_db.models:
            self.vector_db.load_embedding_model()

        # 强制四卡并行模式
        return self._process_all_test_data_multi_thread(test_data)

    def _process_all_test_data_single_thread(self, test_data: List[Dict]) -> List[Dict]:
        """单线程处理所有测试数据"""
        results = []
        vector_db_count = 0
        model_inference_count = 0

        for i, test_item in enumerate(tqdm(test_data, desc="Processing test items")):
            result = self.process_test_item(test_item, gpu_id=AVAILABLE_GPUS[0])
            results.append(result)

            if result['prediction_source'] == 'vector_db':
                vector_db_count += 1
            else:
                model_inference_count += 1

        logger.info(f"Processing completed:")
        logger.info(f"  - Vector DB predictions: {vector_db_count}")
        logger.info(f"  - Model inference needed: {model_inference_count}")

        return results

    def _process_all_test_data_multi_thread(self, test_data: List[Dict]) -> List[Dict]:
        """四卡并行处理所有测试数据"""
        # 使用实际可用的GPU数量
        available_model_gpus = list(self.vector_db.models.keys())
        num_workers = len(available_model_gpus)
        batch_size = max(1, len(test_data) // (num_workers * 4))  # 每个worker处理多个小批次

        logger.info(f"Using {num_workers} workers with batch size {batch_size}")
        logger.info(f"Available model GPUs: {available_model_gpus}")

        # 准备批次数据
        batches = []
        for i in range(0, len(test_data), batch_size):
            batch_data = [(i + j, test_data[i + j]) for j in range(min(batch_size, len(test_data) - i))]
            batches.append(batch_data)

        logger.info(f"Created {len(batches)} batches for parallel processing")

        # 并行处理
        results_dict = {}
        vector_db_count = 0
        model_inference_count = 0

        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            # 提交所有批次，为每个批次分配GPU
            future_to_batch = {}
            for batch_id, batch in enumerate(batches):
                gpu_id = available_model_gpus[batch_id % len(available_model_gpus)]  # 轮询分配GPU
                future = executor.submit(self.process_batch, batch, gpu_id)
                future_to_batch[future] = batch_id

            # 收集结果
            with tqdm(total=len(batches), desc="Processing batches in parallel") as pbar:
                for future in as_completed(future_to_batch):
                    try:
                        batch_results = future.result()
                        for idx, result in batch_results:
                            results_dict[idx] = result

                            if result['prediction_source'] == 'vector_db':
                                vector_db_count += 1
                            else:
                                model_inference_count += 1

                        pbar.update(1)
                    except Exception as e:
                        batch_id = future_to_batch[future]
                        logger.error(f"Batch {batch_id} failed: {e}")
                        raise

        # 按顺序重组结果
        results = [results_dict[i] for i in range(len(test_data))]

        logger.info(f"Processing completed:")
        logger.info(f"  - Vector DB predictions: {vector_db_count}")
        logger.info(f"  - Model inference needed: {model_inference_count}")

        return results
    
    def save_similarity_results(self, results: List[Dict], filename: str = None):
        """保存相似度匹配结果"""
        if filename is None:
            filename = SIMILARITY_RESULTS_FILENAME  # 使用配置文件中的默认文件名
        output_path = os.path.join(RESULTS_DIR, filename)

        # 清理结果以便JSON序列化（简化topk结果）
        cleaned_results = []
        for result in results:
            cleaned_result = result.copy()

            # 简化topk结果，只保留必要信息
            if 'topk_results' in cleaned_result:
                topk_simplified = []
                for score, metadata in cleaned_result['topk_results']:
                    topk_simplified.append({
                        'similarity_score': float(score),
                        'predicted_label': metadata.get('label', []),
                        'sample_id': metadata.get('id', -1)
                    })
                cleaned_result['topk_results'] = topk_simplified

            cleaned_results.append(cleaned_result)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_results, f, ensure_ascii=False, indent=2)

        logger.info(f"Similarity results saved to {output_path}")
        return output_path

if __name__ == "__main__":
    # 测试相似度匹配
    matcher = SimilarityMatcher()
    results = matcher.process_all_test_data()
    matcher.save_similarity_results(results)
