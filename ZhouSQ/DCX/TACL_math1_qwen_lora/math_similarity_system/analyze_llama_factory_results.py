#!/usr/bin/env python3
"""
分析LLaMA-Factory生成的预测结果并输出评估报告
独立脚本，不依赖config.py中的LLaMA-Factory配置
"""
import json
import sys
import os
import argparse
from typing import List, Dict, Tuple
import re
from collections import Counter, defaultdict
import pandas as pd

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from src.evaluation import EvaluationMetrics

# 默认配置
DEFAULT_INPUT_PATH = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_6047/generated_predictions.jsonl"
DEFAULT_OUTPUT_DIR = os.path.join(os.path.dirname(__file__), "results")
DEFAULT_OUTPUT_FILENAME = "llama_factory_evaluation_report.json"

def parse_label_string(label_str: str) -> List[str]:
    """解析标签字符串，提取标签列表"""
    if not label_str:
        return []
    
    # 移除<think>标签内容
    cleaned = re.sub(r'<think>.*?</think>', '', label_str, flags=re.DOTALL)
    
    # 按行分割并清理
    lines = [line.strip() for line in cleaned.strip().split('\n') if line.strip()]
    
    return lines

def load_llama_factory_results(jsonl_path: str) -> List[Dict]:
    """加载LLaMA-Factory的JSONL结果文件"""
    results = []
    
    print(f"正在加载文件: {jsonl_path}")
    
    with open(jsonl_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                
                # 解析预测和真实标签
                predict_labels = parse_label_string(data.get('predict', ''))
                true_labels = parse_label_string(data.get('label', ''))
                
                # 转换为相似度匹配系统的格式
                result = {
                    'query': data.get('prompt', ''),
                    'true_label': true_labels,
                    'predicted_label': predict_labels,
                    'prediction_source': 'fine_tuned_model',  # LLaMA-Factory使用的是微调模型
                    'similarity_score': 1.0,  # 微调模型没有相似度概念，设为1.0
                    'above_threshold': False,  # 微调模型不基于阈值
                    'prediction_correct': set(true_labels) == set(predict_labels) if true_labels and predict_labels else False,
                    # 新增：记录预测和真实的类别数量
                    'predicted_class_count': len(predict_labels),
                    'true_class_count': len(true_labels)
                }
                
                results.append(result)
                
            except json.JSONDecodeError as e:
                print(f"警告: 第{line_num}行JSON解析失败: {e}")
                continue
            except Exception as e:
                print(f"警告: 第{line_num}行处理失败: {e}")
                continue
    
    print(f"成功加载 {len(results)} 个结果")
    return results

def generate_evaluation_report(results: List[Dict], output_path: str):
    """生成评估报告"""
    print("正在生成评估报告...")

    # 使用现有的评估系统
    evaluator = EvaluationMetrics()
    report = evaluator.generate_detailed_report(results)

    # 计算类别数量统计
    class_stats = analyze_class_counts(results)

    # 统计真实标签的叶子节点和预测标签的完整路径
    unique_true_leaf_nodes = set()  # 真实标签的叶子节点
    unique_predicted_paths = set()  # 预测标签的完整路径

    for result in results:
        true_labels = result.get('true_label', [])
        predicted_labels = result.get('predicted_label', [])

        # 对于真实标签，只取最后一个元素（叶子节点）
        if true_labels:
            leaf_node = true_labels[-1]  # 最后一个元素是叶子节点
            unique_true_leaf_nodes.add(leaf_node)

        # 对于预测标签，取完整路径（用" -> "连接）
        if predicted_labels:
            full_path = " -> ".join(predicted_labels)
            unique_predicted_paths.add(full_path)

    # 添加LLaMA-Factory特有的信息
    report['model_info'] = {
        'model_type': 'LLaMA-Factory Fine-tuned Model',
        'model_path': 'Qwen3-8B-Thinking/lora',
        'evaluation_date': '2025-09-15-09-13-54',
        'total_samples': len(results),
        'num_true_leaf_nodes': len(unique_true_leaf_nodes),
        'num_predicted_paths': len(unique_predicted_paths),
        'num_total_unique_items': len(unique_true_leaf_nodes) + len(unique_predicted_paths)
    }

    # 添加类别数量分布统计
    report['class_count_statistics'] = class_stats

    # 保存报告
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"评估报告已保存到: {output_path}")
    return report

def analyze_class_counts(results: List[Dict]) -> Dict:
    """分析预测类别数和真实类别数的统计信息"""
    predicted_counts = [r.get('predicted_class_count', 0) for r in results]
    true_counts = [r.get('true_class_count', 0) for r in results]

    predicted_counter = Counter(predicted_counts)
    true_counter = Counter(true_counts)

    return {
        'predicted_class_distribution': dict(predicted_counter),
        'true_class_distribution': dict(true_counter),
        'avg_predicted_classes': sum(predicted_counts) / len(predicted_counts) if predicted_counts else 0,
        'avg_true_classes': sum(true_counts) / len(true_counts) if true_counts else 0,
        'max_predicted_classes': max(predicted_counts) if predicted_counts else 0,
        'max_true_classes': max(true_counts) if true_counts else 0,
        'min_predicted_classes': min(predicted_counts) if predicted_counts else 0,
        'min_true_classes': min(true_counts) if true_counts else 0
    }

def print_summary(report: Dict, results: List[Dict]):
    """打印评估摘要"""
    print("\n" + "="*60)
    print("LLaMA-Factory模型评估结果摘要")
    print("="*60)

    # 基本信息
    model_info = report.get('model_info', {})
    print(f"模型类型: {model_info.get('model_type', 'Unknown')}")
    print(f"模型路径: {model_info.get('model_path', 'Unknown')}")
    print(f"评估样本数: {model_info.get('total_samples', 0)}")
    print(f"真实标签叶子节点数: {model_info.get('num_true_leaf_nodes', 0)}")
    print(f"预测标签路径数: {model_info.get('num_predicted_paths', 0)}")
    print(f"总计: {model_info.get('num_total_unique_items', 0)}")

    # 类别数量统计
    class_stats = analyze_class_counts(results)
    print(f"\n📊 类别数量统计:")
    print(f"  平均预测类别数: {class_stats['avg_predicted_classes']:.2f}")
    print(f"  平均真实类别数: {class_stats['avg_true_classes']:.2f}")
    print(f"  最大预测类别数: {class_stats['max_predicted_classes']}")
    print(f"  最大真实类别数: {class_stats['max_true_classes']}")
    print(f"  最小预测类别数: {class_stats['min_predicted_classes']}")
    print(f"  最小真实类别数: {class_stats['min_true_classes']}")

    # 预测类别数分布
    pred_dist = class_stats['predicted_class_distribution']
    print(f"\n📈 预测类别数分布:")
    for count in sorted(pred_dist.keys()):
        print(f"  {count}个类别: {pred_dist[count]}个样本 ({pred_dist[count]/len(results)*100:.1f}%)")

    # 真实类别数分布
    true_dist = class_stats['true_class_distribution']
    print(f"\n📈 真实类别数分布:")
    for count in sorted(true_dist.keys()):
        print(f"  {count}个类别: {true_dist[count]}个样本 ({true_dist[count]/len(results)*100:.1f}%)")

    # 叶子节点指标
    leaf_metrics = report.get('leaf_node_metrics', {})
    print(f"\n📊 叶子节点分类性能:")
    print(f"  准确率: {leaf_metrics.get('leaf_node_accuracy', 0):.4f}")
    print(f"  叶子节点类别数: {leaf_metrics.get('num_leaf_classes', 0)}")

    print(f"\n📈 微平均指标 (基于总体TP/FP/FN):")
    print(f"  微平均 Precision: {leaf_metrics.get('leaf_node_micro_precision', 0):.4f}")
    print(f"  微平均 Recall:    {leaf_metrics.get('leaf_node_micro_recall', 0):.4f}")
    print(f"  微平均 F1:        {leaf_metrics.get('leaf_node_micro_f1', 0):.4f}")

    print(f"\n📊 宏平均指标 (各类别指标的平均):")
    print(f"  宏平均 Precision: {leaf_metrics.get('leaf_node_macro_precision', 0):.4f}")
    print(f"  宏平均 Recall:    {leaf_metrics.get('leaf_node_macro_recall', 0):.4f}")
    print(f"  宏平均 F1:        {leaf_metrics.get('leaf_node_macro_f1', 0):.4f}")

    # 整体指标
    overall_metrics = report.get('overall_metrics', {})
    print(f"\n🎯 整体分类性能:")
    print(f"  完全匹配准确率: {overall_metrics.get('exact_match_accuracy', 0):.4f}")
    print(f"  汉明损失: {overall_metrics.get('hamming_loss', 0):.4f}")

    # 预测成功率
    pred_accuracy = report.get('prediction_accuracy', {})
    print(f"\n🎯 预测成功率:")
    print(f"  总体准确率: {pred_accuracy.get('overall_accuracy_topk', 0):.4f}")
    print(f"  正确预测数: {pred_accuracy.get('total_correct_topk', 0)}")
    print(f"  总样本数: {pred_accuracy.get('total_samples', 0)}")

    # 类别分布（显示前10个最常见的叶子节点）
    leaf_dist = leaf_metrics.get('leaf_class_distribution', {})
    if leaf_dist:
        print(f"\n🏷️  最常见的叶子节点类别 (前10个):")
        sorted_classes = sorted(leaf_dist.items(), key=lambda x: x[1].get('true_count', 0), reverse=True)
        for i, (cls, info) in enumerate(sorted_classes[:10], 1):
            true_count = info.get('true_count', 0)
            pred_count = info.get('pred_count', 0)
            print(f"  {i:2d}. {cls}: 真实{true_count}个, 预测{pred_count}个")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分析LLaMA-Factory生成的预测结果")
    parser.add_argument(
        '--input', '-i',
        type=str,
        default=DEFAULT_INPUT_PATH,
        help=f'输入JSONL文件路径 (默认: {DEFAULT_INPUT_PATH})'
    )
    parser.add_argument(
        '--output', '-o',
        type=str,
        help='输出JSON文件路径 (默认: results/llama_factory_evaluation_report.json)'
    )
    parser.add_argument(
        '--output-dir',
        type=str,
        default=DEFAULT_OUTPUT_DIR,
        help=f'输出目录 (默认: {DEFAULT_OUTPUT_DIR})'
    )

    args = parser.parse_args()

    # 设置输入输出路径
    input_path = args.input

    if args.output:
        output_path = args.output
    else:
        # 确保输出目录存在
        os.makedirs(args.output_dir, exist_ok=True)
        output_path = os.path.join(args.output_dir, DEFAULT_OUTPUT_FILENAME)

    # 打印当前配置
    print(f"📋 LLaMA-Factory结果分析配置:")
    print(f"  输入文件: {input_path}")
    print(f"  输出文件: {output_path}")
    print()

    # 检查输入文件是否存在
    if not os.path.exists(input_path):
        print(f"❌ 错误: 输入文件不存在: {input_path}")
        print(f"💡 请检查文件路径是否正确，或使用 --input 参数指定正确的路径")
        return False

    try:
        # 加载LLaMA-Factory结果
        results = load_llama_factory_results(input_path)

        if not results:
            print("❌ 错误: 没有成功加载任何结果")
            return False

        # 生成评估报告
        report = generate_evaluation_report(results, output_path)

        # 打印摘要
        print_summary(report, results)

        print(f"\n✅ 分析完成！详细报告已保存到: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 错误: 分析过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
