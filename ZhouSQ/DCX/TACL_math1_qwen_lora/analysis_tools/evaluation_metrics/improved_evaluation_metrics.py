#!/usr/bin/env python3
"""
改进的评估指标脚本
实现加权宏平均、置信区间计算和样本数筛选
"""

import json
import pandas as pd
import numpy as np
from collections import defaultdict
from datetime import datetime
import re
import scipy.stats as stats
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

def extract_knowledge_path(text: str) -> str:
    """从预测文本中提取知识点路径"""
    if not text:
        return ''
    
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    text = text.strip()
    
    if not text:
        return ''
    
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    knowledge_points = []
    
    for line in lines:
        if any(marker in line for marker in ['```', '===', '---', '<|', '|>', 'assistant', 'user']):
            continue
        knowledge_points.append(line)
    
    return ' -> '.join(knowledge_points) if knowledge_points else ''

def calculate_confidence_interval(values: List[float], confidence: float = 0.95) -> <PERSON><PERSON>[float, float]:
    """计算置信区间"""
    if len(values) < 2:
        return (0.0, 0.0)
    
    mean = np.mean(values)
    std_err = stats.sem(values)
    
    if std_err == 0:
        return (mean, mean)
    
    # 使用t分布计算置信区间
    t_value = stats.t.ppf((1 + confidence) / 2, len(values) - 1)
    margin_error = t_value * std_err
    
    return (mean - margin_error, mean + margin_error)

def bootstrap_confidence_interval(values: List[float], confidence: float = 0.95, n_bootstrap: int = 1000) -> Tuple[float, float]:
    """使用Bootstrap方法计算置信区间"""
    if len(values) < 2:
        return (0.0, 0.0)
    
    bootstrap_means = []
    for _ in range(n_bootstrap):
        bootstrap_sample = np.random.choice(values, size=len(values), replace=True)
        bootstrap_means.append(np.mean(bootstrap_sample))
    
    alpha = 1 - confidence
    lower_percentile = (alpha / 2) * 100
    upper_percentile = (1 - alpha / 2) * 100
    
    ci_lower = np.percentile(bootstrap_means, lower_percentile)
    ci_upper = np.percentile(bootstrap_means, upper_percentile)
    
    return (ci_lower, ci_upper)

def analyze_with_improved_metrics(predictions_file: str, test_file: str, dataset_name: str, min_samples: int = 10):
    """使用改进的评估指标分析预测结果"""
    print(f"\n=== 改进评估指标分析: {dataset_name} ===")
    print(f"最小样本数阈值: {min_samples}")
    
    # 加载测试集获取真实标签分布
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    true_label_counts = defaultdict(int)
    for item in test_data:
        if 'output' in item:
            output = item['output']
            lines = [line.strip() for line in output.split('\n') if line.strip()]
            if lines:
                true_path = ' -> '.join(lines)
                true_label_counts[true_path] += 1
    
    # 分析预测结果
    knowledge_point_stats = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})
    
    try:
        # 第一遍扫描：计算TP和FP
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    predicted_path = extract_knowledge_path(data.get('predict', ''))
                    true_path = extract_knowledge_path(data.get('label', ''))
                    
                    if predicted_path:
                        if predicted_path == true_path:
                            knowledge_point_stats[predicted_path]['tp'] += 1
                        else:
                            knowledge_point_stats[predicted_path]['fp'] += 1
                            
                except:
                    continue
        
        # 第二遍扫描：计算FN
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    predicted_path = extract_knowledge_path(data.get('predict', ''))
                    true_path = extract_knowledge_path(data.get('label', ''))
                    
                    if true_path and true_path in knowledge_point_stats:
                        if predicted_path != true_path:
                            knowledge_point_stats[true_path]['fn'] += 1
                            
                except:
                    continue
                    
    except Exception as e:
        print(f"读取预测文件时出错: {e}")
        return None
    
    # 计算每个知识点的指标
    results = []
    precision_values = []
    recall_values = []
    f1_values = []
    sample_weights = []
    
    for knowledge_point, stats in knowledge_point_stats.items():
        tp = stats['tp']
        fp = stats['fp'] 
        fn = stats['fn']
        test_samples = true_label_counts.get(knowledge_point, 0)
        
        # 只保留样本数>=min_samples的类别
        if test_samples < min_samples:
            continue
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        results.append({
            '知识点': knowledge_point,
            'TP': tp,
            'FP': fp,
            'FN': fn,
            'Precision': precision,
            'Recall': recall,
            'F1': f1,
            '测试集样本数': test_samples,
            '数据集': dataset_name
        })
        
        # 收集用于加权平均和置信区间的数据
        precision_values.append(precision)
        recall_values.append(recall)
        f1_values.append(f1)
        sample_weights.append(test_samples)
    
    if not results:
        print(f"❌ 没有满足最小样本数要求({min_samples})的类别")
        return None
    
    df = pd.DataFrame(results)
    
    # 计算传统宏平均
    macro_precision = np.mean(precision_values)
    macro_recall = np.mean(recall_values)
    macro_f1 = np.mean(f1_values)
    
    # 计算加权宏平均
    total_weight = sum(sample_weights)
    weighted_precision = sum(p * w for p, w in zip(precision_values, sample_weights)) / total_weight
    weighted_recall = sum(r * w for r, w in zip(recall_values, sample_weights)) / total_weight
    weighted_f1 = sum(f * w for f, w in zip(f1_values, sample_weights)) / total_weight
    
    # 计算置信区间
    precision_ci = bootstrap_confidence_interval(precision_values)
    recall_ci = bootstrap_confidence_interval(recall_values)
    f1_ci = bootstrap_confidence_interval(f1_values)
    
    # 计算加权置信区间（使用Bootstrap重采样）
    def weighted_bootstrap_ci(values, weights, n_bootstrap=1000, confidence=0.95):
        if len(values) < 2:
            return (0.0, 0.0)
        
        bootstrap_means = []
        for _ in range(n_bootstrap):
            # 按权重进行重采样
            indices = np.random.choice(len(values), size=len(values), replace=True, p=np.array(weights)/sum(weights))
            bootstrap_sample = [values[i] for i in indices]
            bootstrap_weights = [weights[i] for i in indices]
            
            weighted_mean = sum(v * w for v, w in zip(bootstrap_sample, bootstrap_weights)) / sum(bootstrap_weights)
            bootstrap_means.append(weighted_mean)
        
        alpha = 1 - confidence
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        ci_lower = np.percentile(bootstrap_means, lower_percentile)
        ci_upper = np.percentile(bootstrap_means, upper_percentile)
        
        return (ci_lower, ci_upper)
    
    weighted_precision_ci = weighted_bootstrap_ci(precision_values, sample_weights)
    weighted_recall_ci = weighted_bootstrap_ci(recall_values, sample_weights)
    weighted_f1_ci = weighted_bootstrap_ci(f1_values, sample_weights)
    
    # 输出结果
    print(f"\n📊 评估结果 (样本数>={min_samples}的类别)")
    print(f"符合条件的类别数: {len(df)}")
    print(f"总测试样本数: {sum(sample_weights)}")
    
    print(f"\n📈 传统宏平均指标:")
    print(f"  Precision: {macro_precision:.4f} [{precision_ci[0]:.4f}, {precision_ci[1]:.4f}]")
    print(f"  Recall:    {macro_recall:.4f} [{recall_ci[0]:.4f}, {recall_ci[1]:.4f}]")
    print(f"  F1:        {macro_f1:.4f} [{f1_ci[0]:.4f}, {f1_ci[1]:.4f}]")
    
    print(f"\n⚖️  加权宏平均指标 (按样本数加权):")
    print(f"  Precision: {weighted_precision:.4f} [{weighted_precision_ci[0]:.4f}, {weighted_precision_ci[1]:.4f}]")
    print(f"  Recall:    {weighted_recall:.4f} [{weighted_recall_ci[0]:.4f}, {weighted_recall_ci[1]:.4f}]")
    print(f"  F1:        {weighted_f1:.4f} [{weighted_f1_ci[0]:.4f}, {weighted_f1_ci[1]:.4f}]")
    
    # 计算改进效果
    improvement_precision = weighted_precision - macro_precision
    improvement_recall = weighted_recall - macro_recall
    improvement_f1 = weighted_f1 - macro_f1
    
    print(f"\n📊 加权vs传统宏平均的改进:")
    print(f"  Precision改进: {improvement_precision:+.4f}")
    print(f"  Recall改进:    {improvement_recall:+.4f}")
    print(f"  F1改进:        {improvement_f1:+.4f}")
    
    return {
        'dataset': dataset_name,
        'min_samples': min_samples,
        'qualified_classes': len(df),
        'total_samples': sum(sample_weights),
        'macro_precision': macro_precision,
        'macro_recall': macro_recall,
        'macro_f1': macro_f1,
        'weighted_precision': weighted_precision,
        'weighted_recall': weighted_recall,
        'weighted_f1': weighted_f1,
        'precision_ci': precision_ci,
        'recall_ci': recall_ci,
        'f1_ci': f1_ci,
        'weighted_precision_ci': weighted_precision_ci,
        'weighted_recall_ci': weighted_recall_ci,
        'weighted_f1_ci': weighted_f1_ci,
        'improvement_precision': improvement_precision,
        'improvement_recall': improvement_recall,
        'improvement_f1': improvement_f1,
        'detailed_results': df
    }

def main():
    """主函数"""
    print("=== 改进评估指标实验 ===")
    
    # 定义数据集配置
    datasets = [
        {
            'name': '6000题数据集',
            'predictions': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-09-13-54_6047/generated_predictions.jsonl',
            'test': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/data/math1_test.json'
        },
        {
            'name': '8月数据集(num10)',
            'predictions': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-17-22-14_num10/generated_predictions.jsonl',
            'test': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/data/math1_test_num10.json'
        }
    ]
    
    # 测试不同的最小样本数阈值
    min_sample_thresholds = [5, 10, 15, 20]
    
    all_results = []
    
    for dataset in datasets:
        if os.path.exists(dataset['predictions']) and os.path.exists(dataset['test']):
            print(f"\n{'='*80}")
            print(f"分析数据集: {dataset['name']}")
            print('='*80)
            
            for min_samples in min_sample_thresholds:
                result = analyze_with_improved_metrics(
                    dataset['predictions'], 
                    dataset['test'], 
                    dataset['name'],
                    min_samples
                )
                if result:
                    all_results.append(result)
        else:
            print(f"\n❌ {dataset['name']} 文件不存在")
    
    # 生成对比报告
    if all_results:
        print(f"\n{'='*80}")
        print("改进效果总结")
        print('='*80)
        
        # 按数据集分组显示结果
        datasets_results = {}
        for result in all_results:
            dataset_name = result['dataset']
            if dataset_name not in datasets_results:
                datasets_results[dataset_name] = []
            datasets_results[dataset_name].append(result)
        
        for dataset_name, results in datasets_results.items():
            print(f"\n📊 {dataset_name} - 不同样本数阈值的效果:")
            print("样本数阈值 | 符合类别数 | 传统宏F1 | 加权宏F1 | F1改进 | 95%置信区间")
            print("-" * 75)
            
            for result in results:
                ci_str = f"[{result['weighted_f1_ci'][0]:.3f}, {result['weighted_f1_ci'][1]:.3f}]"
                print(f"{result['min_samples']:>8} | {result['qualified_classes']:>8} | "
                      f"{result['macro_f1']:>7.4f} | {result['weighted_f1']:>7.4f} | "
                      f"{result['improvement_f1']:>+6.4f} | {ci_str}")
        
        # 保存详细结果
        # 确保results目录存在
        import os
        results_dir = '../../results/evaluation_metrics'
        os.makedirs(results_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(results_dir, f"improved_evaluation_results_{timestamp}.xlsx")

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 保存汇总结果
            summary_data = []
            for result in all_results:
                summary_data.append({
                    '数据集': result['dataset'],
                    '最小样本数': result['min_samples'],
                    '符合条件类别数': result['qualified_classes'],
                    '总样本数': result['total_samples'],
                    '传统宏平均F1': result['macro_f1'],
                    '加权宏平均F1': result['weighted_f1'],
                    'F1改进': result['improvement_f1'],
                    '加权F1置信区间下界': result['weighted_f1_ci'][0],
                    '加权F1置信区间上界': result['weighted_f1_ci'][1]
                })
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='改进效果汇总', index=False)
            
            # 保存每个实验的详细结果
            for i, result in enumerate(all_results):
                sheet_name = f"{result['dataset'][:10]}_min{result['min_samples']}"
                result['detailed_results'].to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"\n✅ 详细结果已保存到: {output_file}")

if __name__ == "__main__":
    import os
    main()
