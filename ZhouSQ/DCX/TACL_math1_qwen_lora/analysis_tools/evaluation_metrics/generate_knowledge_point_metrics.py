#!/usr/bin/env python3
"""
基于预测结果生成知识点分类指标Excel表格
计算每个知识点的TP、FP、FN、Precision、Recall、F1以及在训练集和测试集中的样本数量
"""

import json
import sys
import re
from pathlib import Path
from collections import defaultdict, Counter
import pandas as pd
from typing import Dict, List, Set, Tuple
from datetime import datetime
import os

def extract_knowledge_path(text: str) -> str:
    """
    从文本中提取完整的知识点路径

    Args:
        text (str): 包含知识点路径的文本

    Returns:
        str: 完整的知识点路径，用" -> "连接
    """
    if not text:
        return ""

    # 移除<think>标签内容
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    text = text.strip()

    if not text:
        return ""

    # 按行分割并过滤空行
    lines = [line.strip() for line in text.split('\n') if line.strip()]

    # 过滤掉非知识点行（如包含特殊字符的行）
    knowledge_points = []
    for line in lines:
        # 跳过包含特殊标记的行
        if any(marker in line for marker in ['```', '===', '---', '<|', '|>', 'assistant', 'user']):
            continue
        knowledge_points.append(line)

    # 返回完整路径，用" -> "连接
    return " -> ".join(knowledge_points) if knowledge_points else ""

def extract_knowledge_path_without_supplement(text: str) -> str:
    """
    从文本中提取知识点路径，但忽略"补充知识点xxx"层级

    Args:
        text (str): 包含知识点路径的文本

    Returns:
        str: 去除补充知识点后的知识点路径，用" -> "连接
    """
    if not text:
        return ""

    # 移除<think>标签内容
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    text = text.strip()

    if not text:
        return ""

    # 按行分割并过滤空行
    lines = [line.strip() for line in text.split('\n') if line.strip()]

    # 过滤掉非知识点行和补充知识点行
    knowledge_points = []
    for line in lines:
        # 跳过包含特殊标记的行
        if any(marker in line for marker in ['```', '===', '---', '<|', '|>', 'assistant', 'user']):
            continue
        # 跳过补充知识点
        if line.startswith('补充知识点'):
            continue
        knowledge_points.append(line)

    # 返回完整路径，用" -> "连接
    return " -> ".join(knowledge_points) if knowledge_points else ""

def load_dataset_knowledge_points(file_path: str) -> Dict[str, int]:
    """
    加载数据集并统计每个知识点路径的样本数量（忽略补充知识点）

    Args:
        file_path (str): 数据集文件路径

    Returns:
        Dict[str, int]: 知识点路径到样本数量的映射
    """
    knowledge_point_counts = Counter()

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        for item in data:
            output = item.get('output', '')
            # 提取去除补充知识点的路径
            path = extract_knowledge_path_without_supplement(output)
            if path:
                knowledge_point_counts[path] += 1

    except Exception as e:
        print(f"加载数据集 {file_path} 时出错: {e}")

    return dict(knowledge_point_counts)

def calculate_metrics(tp: int, fp: int, fn: int) -> Tuple[float, float, float]:
    """
    计算Precision、Recall、F1指标
    
    Args:
        tp (int): True Positive
        fp (int): False Positive  
        fn (int): False Negative
        
    Returns:
        Tuple[float, float, float]: (Precision, Recall, F1)
    """
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    
    return precision, recall, f1

def analyze_predictions(predictions_file: str, train_file: str, test_file: str) -> pd.DataFrame:
    """
    分析预测结果并生成指标表格
    
    Args:
        predictions_file (str): 预测结果文件路径
        train_file (str): 训练集文件路径
        test_file (str): 测试集文件路径
        
    Returns:
        pd.DataFrame: 包含各项指标的数据框
    """
    print("正在加载训练集和测试集...")
    train_counts = load_dataset_knowledge_points(train_file)
    test_counts = load_dataset_knowledge_points(test_file)
    
    print("正在分析预测结果...")
    
    # 只统计预测的知识点的TP、FP、FN
    knowledge_point_stats = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})

    try:
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    predict_raw = data.get('predict', '')
                    label_raw = data.get('label', '')

                    # 提取完整知识点路径
                    predicted_path = extract_knowledge_path(predict_raw)
                    true_path = extract_knowledge_path(label_raw)

                    # 只处理预测的知识点
                    if predicted_path:
                        if predicted_path == true_path:
                            # True Positive: 预测正确
                            knowledge_point_stats[predicted_path]['tp'] += 1
                        else:
                            # False Positive: 预测错误
                            knowledge_point_stats[predicted_path]['fp'] += 1
                            
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行 JSON 解析错误: {e}")
                    continue
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    continue
                    
    except Exception as e:
        print(f"读取预测文件时出错: {e}")
        return pd.DataFrame()

    # 第二遍扫描：为预测的知识点计算FN
    # FN = 真实标签是该知识点，但预测不是该知识点的次数
    try:
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    predict_raw = data.get('predict', '')
                    label_raw = data.get('label', '')

                    predicted_path = extract_knowledge_path(predict_raw)
                    true_path = extract_knowledge_path(label_raw)

                    # 对于每个预测过的知识点，检查是否有遗漏的真实标签
                    if true_path and true_path in knowledge_point_stats:
                        if predicted_path != true_path:
                            # 真实标签是这个知识点，但预测不是
                            knowledge_point_stats[true_path]['fn'] += 1

                except json.JSONDecodeError:
                    continue
                except Exception:
                    continue

    except Exception as e:
        print(f"第二遍扫描时出错: {e}")

    print("正在计算指标...")
    
    # 生成结果数据
    results = []
    for knowledge_point, stats in knowledge_point_stats.items():
        tp = stats['tp']
        fp = stats['fp'] 
        fn = stats['fn']
        
        precision, recall, f1 = calculate_metrics(tp, fp, fn)
        
        # 对于训练集和测试集的匹配，使用去除补充知识点的路径进行匹配
        knowledge_point_without_supplement = extract_knowledge_path_without_supplement(knowledge_point)
        train_samples = train_counts.get(knowledge_point_without_supplement, 0)
        test_samples = test_counts.get(knowledge_point_without_supplement, 0)
        
        results.append({
            '知识点': knowledge_point,
            'TP': tp,
            'FP': fp,
            'FN': fn,
            'Precision': round(precision, 4),
            'Recall': round(recall, 4),
            'F1': round(f1, 4),
            '训练集样本数': train_samples,
            '测试集样本数': test_samples
        })
    
    # 按F1分数降序排列
    results.sort(key=lambda x: x['F1'], reverse=True)
    
    return pd.DataFrame(results)

def main():
    """主函数"""
    # 默认文件路径
    default_predictions = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-17-22-14_num10/generated_predictions.jsonl"
    default_train = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/data/math1.json"
    default_test = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/data/math1_test.json"
    
    # 检查命令行参数
    predictions_file = sys.argv[1] if len(sys.argv) > 1 else default_predictions
    train_file = sys.argv[2] if len(sys.argv) > 2 else default_train
    test_file = sys.argv[3] if len(sys.argv) > 3 else default_test
    
    print(f"预测文件: {predictions_file}")
    print(f"训练集文件: {train_file}")
    print(f"测试集文件: {test_file}")
    
    # 检查文件是否存在
    for file_path in [predictions_file, train_file, test_file]:
        if not Path(file_path).exists():
            print(f"错误: 文件不存在 - {file_path}")
            sys.exit(1)
    
    # 分析数据
    df = analyze_predictions(predictions_file, train_file, test_file)
    
    if df.empty:
        print("❌ 没有生成有效数据")
        return
    
    # 计算统计信息
    avg_precision = df['Precision'].mean()
    avg_recall = df['Recall'].mean()
    avg_f1 = df['F1'].mean()
    total_tp = df['TP'].sum()
    total_fp = df['FP'].sum()
    total_fn = df['FN'].sum()

    # 创建统计信息行
    stats_row = {
        '知识点': '📈 统计信息',
        'TP': f'总TP: {total_tp}',
        'FP': f'总FP: {total_fp}',
        'FN': f'总FN: {total_fn}',
        'Precision': f'平均: {avg_precision:.4f}',
        'Recall': f'平均: {avg_recall:.4f}',
        'F1': f'平均: {avg_f1:.4f}',
        '训练集样本数': '',
        '测试集样本数': ''
    }

    # 添加统计信息行到数据框
    df_with_stats = pd.concat([df, pd.DataFrame([stats_row])], ignore_index=True)

    # 过滤测试集样本数>=3的数据用于Sheet2
    df_filtered = df[df['测试集样本数'] >= 3].copy()

    # 生成带时间戳的sheet名称
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    sheet_name_all = f'所有数据_{timestamp}'
    sheet_name_filtered = f'测试集样本数>=3_{timestamp}'

    # 保存Excel文件，追加新的sheet
    # 确保results目录存在
    results_dir = '../../results/evaluation_metrics'
    os.makedirs(results_dir, exist_ok=True)

    output_file = os.path.join(results_dir, "knowledge_point_metrics.xlsx")

    # 检查文件是否存在，如果存在则追加，否则创建新文件
    if os.path.exists(output_file):
        # 读取现有文件
        with pd.ExcelWriter(output_file, engine='openpyxl', mode='a', if_sheet_exists='new') as writer:
            # 添加新的sheet
            df_with_stats.to_excel(writer, sheet_name=sheet_name_all, index=False)

            if not df_filtered.empty:
                # 重新计算过滤后数据的统计信息
                filtered_avg_precision = df_filtered['Precision'].mean()
                filtered_avg_recall = df_filtered['Recall'].mean()
                filtered_avg_f1 = df_filtered['F1'].mean()
                filtered_total_tp = df_filtered['TP'].sum()
                filtered_total_fp = df_filtered['FP'].sum()
                filtered_total_fn = df_filtered['FN'].sum()

                filtered_stats_row = {
                    '知识点': '📈 统计信息 (测试集样本数>=3)',
                    'TP': f'总TP: {filtered_total_tp}',
                    'FP': f'总FP: {filtered_total_fp}',
                    'FN': f'总FN: {filtered_total_fn}',
                    'Precision': f'平均: {filtered_avg_precision:.4f}',
                    'Recall': f'平均: {filtered_avg_recall:.4f}',
                    'F1': f'平均: {filtered_avg_f1:.4f}',
                    '训练集样本数': '',
                    '测试集样本数': ''
                }

                df_filtered_with_stats = pd.concat([df_filtered, pd.DataFrame([filtered_stats_row])], ignore_index=True)
                df_filtered_with_stats.to_excel(writer, sheet_name=sheet_name_filtered, index=False)
    else:
        # 创建新文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # Sheet1: 所有数据 + 统计信息
            df_with_stats.to_excel(writer, sheet_name=sheet_name_all, index=False)

            # Sheet2: 测试集样本数>=3的数据 + 统计信息
            if not df_filtered.empty:
                # 重新计算过滤后数据的统计信息
                filtered_avg_precision = df_filtered['Precision'].mean()
                filtered_avg_recall = df_filtered['Recall'].mean()
                filtered_avg_f1 = df_filtered['F1'].mean()
                filtered_total_tp = df_filtered['TP'].sum()
                filtered_total_fp = df_filtered['FP'].sum()
                filtered_total_fn = df_filtered['FN'].sum()

                filtered_stats_row = {
                    '知识点': '📈 统计信息 (测试集样本数>=3)',
                    'TP': f'总TP: {filtered_total_tp}',
                    'FP': f'总FP: {filtered_total_fp}',
                    'FN': f'总FN: {filtered_total_fn}',
                    'Precision': f'平均: {filtered_avg_precision:.4f}',
                    'Recall': f'平均: {filtered_avg_recall:.4f}',
                    'F1': f'平均: {filtered_avg_f1:.4f}',
                    '训练集样本数': '',
                    '测试集样本数': ''
                }

                df_filtered_with_stats = pd.concat([df_filtered, pd.DataFrame([filtered_stats_row])], ignore_index=True)
                df_filtered_with_stats.to_excel(writer, sheet_name=sheet_name_filtered, index=False)

    print(f"\n✅ 成功生成Excel文件: {output_file}")
    print(f"📊 新增Sheet: {sheet_name_all} - 总共分析了 {len(df)} 个知识点")
    if not df_filtered.empty:
        print(f"📊 新增Sheet: {sheet_name_filtered} - 测试集样本数>=3的知识点: {len(df_filtered)} 个")

    # 显示前10行预览
    print("\n📋 前10行数据预览:")
    print("=" * 120)
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 50)
    print(df.head(10).to_string(index=False))

    # 显示统计信息
    print(f"\n📈 所有数据统计信息:")
    print(f"平均Precision: {avg_precision:.4f}")
    print(f"平均Recall: {avg_recall:.4f}")
    print(f"平均F1: {avg_f1:.4f}")
    print(f"总TP: {total_tp}")
    print(f"总FP: {total_fp}")
    print(f"总FN: {total_fn}")

    if not df_filtered.empty:
        print(f"\n📈 测试集样本数>=3的数据统计信息:")
        print(f"平均Precision: {filtered_avg_precision:.4f}")
        print(f"平均Recall: {filtered_avg_recall:.4f}")
        print(f"平均F1: {filtered_avg_f1:.4f}")
        print(f"总TP: {filtered_total_tp}")
        print(f"总FP: {filtered_total_fp}")
        print(f"总FN: {filtered_total_fn}")

if __name__ == "__main__":
    main()
