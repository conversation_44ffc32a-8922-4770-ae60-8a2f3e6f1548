#!/usr/bin/env python3
"""
三模型Badcase分析脚本
分析LLaMA、Embedding和TACL三个模型的预测失败案例
生成详细的Excel分析报告
"""

import json
import pandas as pd
from collections import defaultdict, Counter
import numpy as np
from typing import List, Dict, Tuple, Set
from difflib import SequenceMatcher
import re
import os

def extract_knowledge_path_without_supplement(path_str: str) -> str:
    """提取知识点路径，忽略补充知识点和思考标记"""
    if not path_str:
        return ""
    
    # 分割路径
    parts = [part.strip() for part in path_str.split('\n') if part.strip()]
    
    # 过滤掉补充知识点和思考标记
    filtered_parts = []
    for part in parts:
        if (not part.startswith('补充知识点') and 
            part not in ['<think>', '</think>', '<think> -> </think>']):
            filtered_parts.append(part)
    
    # 如果是用 -> 分隔的格式，也需要处理
    if len(filtered_parts) == 1 and ' -> ' in filtered_parts[0]:
        path_parts = [p.strip() for p in filtered_parts[0].split(' -> ')]
        filtered_parts = []
        for part in path_parts:
            if (not part.startswith('补充知识点') and 
                part not in ['<think>', '</think>', '']):
                filtered_parts.append(part)
    
    return ' -> '.join(filtered_parts) if filtered_parts else ""

def clean_query_text(text: str) -> str:
    """清理查询文本，移除多余的空格和特殊字符"""
    if not text:
        return ""
    text = re.sub(r'\s+', ' ', text.strip())
    text = text.replace('$', '').replace('\\', '')
    return text

def extract_query_from_llama_prompt(prompt: str) -> str:
    """从LLaMA的prompt中提取查询内容"""
    if not prompt:
        return ""

    if '<|im_start|>user\n' in prompt and '<|im_end|>' in prompt:
        user_content = prompt.split('<|im_start|>user\n')[1].split('<|im_end|>')[0]

        instruction_patterns = [
            '确保标签序列严格遵循从宏观到具体的顺序。\n',
            '确保标签序列严格遵循从宏观到具体的顺序。',
            '请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。\n',
            '请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。'
        ]

        for pattern in instruction_patterns:
            if pattern in user_content:
                query = user_content.split(pattern, 1)[-1].strip()
                if query:
                    return clean_query_text(query)

        return clean_query_text(user_content)

    return ""

def load_llama_predictions(file_path: str) -> List[Dict]:
    """加载LLaMA预测结果"""
    predictions = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for idx, line in enumerate(f):
            data = json.loads(line.strip())
            
            pred_path = extract_knowledge_path_without_supplement(data['predict'])
            true_path = extract_knowledge_path_without_supplement(data['label'])
            query = extract_query_from_llama_prompt(data['prompt'])
            
            predictions.append({
                'index': idx,
                'query': query,
                'llama_prediction': pred_path,
                'true_label': true_path,
                'raw_prompt': data['prompt']
            })
    
    return predictions

def load_embedding_predictions(file_path: str) -> List[Dict]:
    """加载Embedding预测结果，包含Top-k信息"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    predictions = []
    for idx, item in enumerate(data):
        true_path = ' -> '.join(item['true_label'])
        true_path = extract_knowledge_path_without_supplement(true_path)
        
        pred_path = ' -> '.join(item['predicted_label'])
        pred_path = extract_knowledge_path_without_supplement(pred_path)
        
        # 提取Top-k结果
        topk_predictions = []
        if 'topk_results' in item and isinstance(item['topk_results'], list):
            for k, topk_item in enumerate(item['topk_results']):
                try:
                    if isinstance(topk_item, list) and len(topk_item) >= 2:
                        similarity_score = topk_item[0]
                        detail_dict = topk_item[1]
                        pred_label = detail_dict.get('label', [])
                    elif isinstance(topk_item, dict):
                        pred_label = topk_item.get('predicted_label', [])
                        similarity_score = topk_item.get('similarity_score', 0.0)
                    else:
                        continue
                    
                    if isinstance(pred_label, list):
                        topk_path = ' -> '.join(pred_label)
                    elif isinstance(pred_label, str):
                        topk_path = pred_label
                    else:
                        continue
                        
                    topk_path = extract_knowledge_path_without_supplement(topk_path)
                    topk_predictions.append({
                        'rank': k + 1,
                        'prediction': topk_path,
                        'similarity_score': similarity_score
                    })
                except Exception:
                    continue

        predictions.append({
            'index': idx,
            'query': clean_query_text(item['query']),
            'embedding_prediction': pred_path,
            'embedding_topk': topk_predictions,
            'true_label': true_path
        })

    return predictions

def load_tacl_predictions(file_path: str) -> List[Dict]:
    """加载TACL预测结果，包含Top-k信息"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    predictions = []
    for idx, item in enumerate(data):
        if item['true_lable_paths']:
            true_path = item['true_lable_paths'][0]
            true_path = extract_knowledge_path_without_supplement(true_path)
        else:
            true_path = ""

        pred_path = ""
        confidence = 0.0
        if item['predictions']:
            pred_path = item['predictions'][0]['full_path']
            pred_path = extract_knowledge_path_without_supplement(pred_path)
            confidence = item['predictions'][0]['confidence']

        # 提取Top-k结果
        topk_predictions = []
        if item['predictions']:
            for k, pred_item in enumerate(item['predictions']):
                topk_path = extract_knowledge_path_without_supplement(pred_item['full_path'])
                topk_predictions.append({
                    'rank': k + 1,
                    'prediction': topk_path,
                    'confidence': pred_item['confidence']
                })

        predictions.append({
            'index': idx,
            'query': clean_query_text(item['question']),
            'tacl_prediction': pred_path,
            'tacl_topk': topk_predictions,
            'true_label': true_path,
            'confidence': confidence
        })

    return predictions

def load_dataset_labels(file_path: str) -> Dict[str, int]:
    """加载数据集，统计每个标签的出现次数"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    label_counts = Counter()
    for item in data:
        # 检查不同的标签字段名
        label_field = None
        if 'doc_label' in item:
            label_field = item['doc_label']
        elif 'label' in item:
            label_field = item['label']

        if label_field:
            if isinstance(label_field, list):
                label_path = ' -> '.join(label_field)
            else:
                label_path = str(label_field)

            label_path = extract_knowledge_path_without_supplement(label_path)
            if label_path:
                label_counts[label_path] += 1

    return dict(label_counts)

def load_train_and_test_labels(train_file: str, test_file: str) -> Tuple[Dict[str, int], Dict[str, int]]:
    """加载训练集和测试集，统计每个标签的出现次数"""
    print("📊 统计训练集和测试集中的标签频次...")

    train_counts = load_dataset_labels(train_file)
    test_counts = load_dataset_labels(test_file)

    print(f"  训练集标签种类: {len(train_counts)}")
    print(f"  测试集标签种类: {len(test_counts)}")

    return train_counts, test_counts

def find_best_match(query: str, candidate_queries: List[str]) -> Tuple[int, float]:
    """找到最佳匹配的查询"""
    best_idx = -1
    best_similarity = 0.0

    for idx, candidate in enumerate(candidate_queries):
        similarity = SequenceMatcher(None, query, candidate).ratio()
        if similarity > best_similarity:
            best_similarity = similarity
            best_idx = idx

    return best_idx, best_similarity

def merge_predictions(llama_preds: List[Dict], embedding_preds: List[Dict], tacl_preds: List[Dict]) -> List[Dict]:
    """合并三个模型的预测结果"""
    merged_results = []
    used_embedding_indices = set()
    used_tacl_indices = set()

    print(f"🔄 开始匹配 {len(llama_preds)} 个样本...")

    for llama_pred in llama_preds:
        llama_query = llama_pred['query']
        llama_idx = llama_pred['index']

        matched_embedding_idx = -1
        matched_tacl_idx = -1

        # 策略1: 位置索引匹配（优先）
        if llama_idx < len(embedding_preds) and llama_idx not in used_embedding_indices:
            embedding_pred = embedding_preds[llama_idx]
            similarity = SequenceMatcher(None, llama_query, embedding_pred['query']).ratio()
            if similarity > 0.7:
                matched_embedding_idx = llama_idx

        if llama_idx < len(tacl_preds) and llama_idx not in used_tacl_indices:
            tacl_pred = tacl_preds[llama_idx]
            similarity = SequenceMatcher(None, llama_query, tacl_pred['query']).ratio()
            if similarity > 0.7:
                matched_tacl_idx = llama_idx

        # 策略2: 精确匹配
        if matched_embedding_idx == -1:
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices and embedding_pred['query'] == llama_query:
                    matched_embedding_idx = idx
                    break

        if matched_tacl_idx == -1:
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices and tacl_pred['query'] == llama_query:
                    matched_tacl_idx = idx
                    break

        # 策略3: 模糊匹配
        if matched_embedding_idx == -1:
            available_queries = []
            available_indices = []
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices:
                    available_queries.append(embedding_pred['query'])
                    available_indices.append(idx)

            if available_queries:
                best_idx, best_similarity = find_best_match(llama_query, available_queries)
                if best_similarity > 0.5:
                    matched_embedding_idx = available_indices[best_idx]

        if matched_tacl_idx == -1:
            available_queries = []
            available_indices = []
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices:
                    available_queries.append(tacl_pred['query'])
                    available_indices.append(idx)

            if available_queries:
                best_idx, best_similarity = find_best_match(llama_query, available_queries)
                if best_similarity > 0.5:
                    matched_tacl_idx = available_indices[best_idx]

        # 策略4: 强制匹配
        if matched_embedding_idx == -1:
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices:
                    matched_embedding_idx = idx
                    break

        if matched_tacl_idx == -1:
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices:
                    matched_tacl_idx = idx
                    break

        # 如果找到了匹配，添加到结果中
        if matched_embedding_idx != -1 and matched_tacl_idx != -1:
            embedding_pred = embedding_preds[matched_embedding_idx]
            tacl_pred = tacl_preds[matched_tacl_idx]
            used_embedding_indices.add(matched_embedding_idx)
            used_tacl_indices.add(matched_tacl_idx)

            merged_results.append({
                'llama_index': llama_idx,
                'embedding_index': matched_embedding_idx,
                'tacl_index': matched_tacl_idx,
                'query': llama_query,
                'true_label': llama_pred['true_label'],
                'llama_prediction': llama_pred['llama_prediction'],
                'embedding_prediction': embedding_pred['embedding_prediction'],
                'tacl_prediction': tacl_pred['tacl_prediction'],
                'embedding_topk': embedding_pred.get('embedding_topk', []),
                'tacl_topk': tacl_pred.get('tacl_topk', []),
                'llama_correct': llama_pred['llama_prediction'] == llama_pred['true_label'],
                'embedding_correct': embedding_pred['embedding_prediction'] == embedding_pred['true_label'],
                'tacl_correct': tacl_pred['tacl_prediction'] == tacl_pred['true_label']
            })

    print(f"✅ 匹配完成！成功匹配 {len(merged_results)} 个样本")
    return merged_results

def analyze_badcases(merged_data: List[Dict], train_labels: Dict[str, int], test_labels: Dict[str, int]) -> List[Dict]:
    """分析badcase，生成详细的Excel数据"""
    detailed_results = []

    for item in merged_data:
        def clean_string(s):
            if not s:
                return ""
            import re
            return re.sub(r'[\x00-\x1f\x7f-\x9f]', '', str(s))

        # 基础信息
        true_label = clean_string(item['true_label'])
        result_item = {
            'index': item['llama_index'],
            'question': clean_string(item['query']),
            'true_label': true_label,
            'true_label_train_count': train_labels.get(true_label, 0),
            'true_label_test_count': test_labels.get(true_label, 0),

            # LLaMA预测
            'llama_prediction': clean_string(item['llama_prediction']),
            'llama_prediction_train_count': train_labels.get(clean_string(item['llama_prediction']), 0),
            'llama_prediction_test_count': test_labels.get(clean_string(item['llama_prediction']), 0),
            'llama_correct': item['llama_correct'],

            # 失败模型列表 (放在前面便于查看)
            'failed_models': '',

            # Embedding Top-3预测
            'embedding_top1': '',
            'embedding_top1_train_count': 0,
            'embedding_top1_test_count': 0,
            'embedding_top1_similarity': 0.0,
            'embedding_top1_correct': False,
            'embedding_top2': '',
            'embedding_top2_train_count': 0,
            'embedding_top2_test_count': 0,
            'embedding_top2_similarity': 0.0,
            'embedding_top2_correct': False,
            'embedding_top3': '',
            'embedding_top3_train_count': 0,
            'embedding_top3_test_count': 0,
            'embedding_top3_similarity': 0.0,
            'embedding_top3_correct': False,

            # TACL Top-3预测
            'tacl_top1': '',
            'tacl_top1_train_count': 0,
            'tacl_top1_test_count': 0,
            'tacl_top1_confidence': 0.0,
            'tacl_top1_correct': False,
            'tacl_top2': '',
            'tacl_top2_train_count': 0,
            'tacl_top2_test_count': 0,
            'tacl_top2_confidence': 0.0,
            'tacl_top2_correct': False,
            'tacl_top3': '',
            'tacl_top3_train_count': 0,
            'tacl_top3_test_count': 0,
            'tacl_top3_confidence': 0.0,
            'tacl_top3_correct': False
        }

        # 填充Embedding Top-k信息
        embedding_topk = item.get('embedding_topk', [])
        embedding_any_correct = False
        for i, topk_item in enumerate(embedding_topk[:3]):  # 只取前3个
            rank = i + 1
            prediction = clean_string(topk_item['prediction'])
            similarity = topk_item.get('similarity_score', 0.0)
            is_correct = prediction == item['true_label']

            result_item[f'embedding_top{rank}'] = prediction
            result_item[f'embedding_top{rank}_train_count'] = train_labels.get(prediction, 0)
            result_item[f'embedding_top{rank}_test_count'] = test_labels.get(prediction, 0)
            result_item[f'embedding_top{rank}_similarity'] = similarity
            result_item[f'embedding_top{rank}_correct'] = is_correct

            if is_correct:
                embedding_any_correct = True

        # 填充TACL Top-k信息
        tacl_topk = item.get('tacl_topk', [])
        tacl_any_correct = False
        for i, topk_item in enumerate(tacl_topk[:3]):  # 只取前3个
            rank = i + 1
            prediction = clean_string(topk_item['prediction'])
            confidence = topk_item.get('confidence', 0.0)
            is_correct = prediction == item['true_label']

            result_item[f'tacl_top{rank}'] = prediction
            result_item[f'tacl_top{rank}_train_count'] = train_labels.get(prediction, 0)
            result_item[f'tacl_top{rank}_test_count'] = test_labels.get(prediction, 0)
            result_item[f'tacl_top{rank}_confidence'] = confidence
            result_item[f'tacl_top{rank}_correct'] = is_correct

            if is_correct:
                tacl_any_correct = True

        # 分析失败情况 - 只保留failed_models列
        failed_models = []
        if not item['llama_correct']:
            failed_models.append('LLaMA')
        if not embedding_any_correct:
            failed_models.append('Embedding')
        if not tacl_any_correct:
            failed_models.append('TACL')

        # 如果没有失败的模型，显示"无"而不是空字符串
        result_item['failed_models'] = ', '.join(failed_models) if failed_models else '无'

        detailed_results.append(result_item)

    return detailed_results

def main():
    # 文件路径
    llama_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_num10/generated_predictions.jsonl"
    embedding_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math_similarity_system/results/embedding-4b-top3-result-num10/final_results.json"
    tacl_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/TACL-result/20250915_num_10_top3.json"
    train_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math1_level7_data/wos_train.json"
    test_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math1_level7_data/num_beyond_10.json"

    print("🔄 加载数据...")

    # 加载预测结果
    llama_preds = load_llama_predictions(llama_file)
    embedding_preds = load_embedding_predictions(embedding_file)
    tacl_preds = load_tacl_predictions(tacl_file)

    # 加载训练集和测试集标签统计
    train_labels, test_labels = load_train_and_test_labels(train_file, test_file)

    print(f"✅ LLaMA预测数量: {len(llama_preds)}")
    print(f"✅ Embedding预测数量: {len(embedding_preds)}")
    print(f"✅ TACL预测数量: {len(tacl_preds)}")
    print(f"✅ 训练集标签统计: {len(train_labels)} 个不同标签")
    print(f"✅ 测试集标签统计: {len(test_labels)} 个不同标签")

    # 合并预测结果
    merged_data = merge_predictions(llama_preds, embedding_preds, tacl_preds)

    if len(merged_data) == 0:
        print("❌ 没有找到匹配的样本，无法进行分析")
        return

    print("🔄 分析badcase...")

    # 分析badcase
    detailed_results = analyze_badcases(merged_data, train_labels, test_labels)

    # 生成统计信息
    total_samples = len(detailed_results)
    all_failed = sum(1 for item in detailed_results if item['failed_models'] == 'LLaMA, Embedding, TACL')

    print(f"\n📊 Badcase分析统计:")
    print(f"  总样本数: {total_samples}")
    print(f"  三个模型都失败: {all_failed} ({all_failed/total_samples*100:.2f}%)")

    # 统计失败模型分布
    failed_model_counts = {}
    for item in detailed_results:
        failed_models = item['failed_models']
        if failed_models:
            failed_model_counts[failed_models] = failed_model_counts.get(failed_models, 0) + 1

    print(f"\n📊 失败模型分布:")
    for failed_models, count in sorted(failed_model_counts.items()):
        print(f"  {failed_models}: {count} ({count/total_samples*100:.2f}%)")

    # 保存到Excel
    df = pd.DataFrame(detailed_results)

    try:
        # 确保results目录存在
        import os
        results_dir = '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/results/badcase_analysis'
        os.makedirs(results_dir, exist_ok=True)

        output_file = os.path.join(results_dir, 'badcase_analysis_detailed.xlsx')
        df.to_excel(output_file, index=False)
        print(f"\n💾 详细badcase分析结果已保存到: {output_file}")
        print(f"📊 Excel文件包含 {len(df)} 行 × {len(df.columns)} 列数据")

        # 显示列名
        print(f"\n📋 Excel文件列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")

    except Exception as e:
        print(f"\n⚠️  Excel保存失败: {e}")
        csv_file = 'badcase_analysis_detailed.csv'
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"💾 详细badcase分析结果已保存为CSV: {csv_file}")

if __name__ == "__main__":
    main()
