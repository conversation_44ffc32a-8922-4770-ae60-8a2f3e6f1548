#!/usr/bin/env python3
"""
数据集差异分析脚本
对比6000题数据集与8月数据集的宏平均指标差异
"""

import json
import pandas as pd
from collections import defaultdict, Counter
from datetime import datetime
import os
import sys

def extract_knowledge_path(text: str) -> str:
    """从预测文本中提取知识点路径"""
    if not text:
        return ''
    
    # 移除<think>标签内容
    import re
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    text = text.strip()
    
    if not text:
        return ''
    
    # 按行分割并清理
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    knowledge_points = []
    
    for line in lines:
        # 跳过特殊标记行
        if any(marker in line for marker in ['```', '===', '---', '<|', '|>', 'assistant', 'user']):
            continue
        knowledge_points.append(line)
    
    return ' -> '.join(knowledge_points) if knowledge_points else ''

def analyze_prediction_results(predictions_file: str, test_file: str, dataset_name: str):
    """分析预测结果并计算指标"""
    print(f"\n=== 分析 {dataset_name} ===")
    print(f"预测文件: {predictions_file}")
    print(f"测试文件: {test_file}")
    
    # 加载测试集获取真实标签分布
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    true_label_counts = Counter()
    for item in test_data:
        if 'output' in item:
            output = item['output']
            lines = [line.strip() for line in output.split('\n') if line.strip()]
            if lines:
                true_path = ' -> '.join(lines)
                true_label_counts[true_path] += 1
    
    print(f"测试集样本数: {len(test_data)}")
    print(f"测试集唯一知识点数: {len(true_label_counts)}")
    
    # 分析预测结果
    knowledge_point_stats = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})
    
    try:
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    predicted_path = extract_knowledge_path(data.get('predict', ''))
                    true_path = extract_knowledge_path(data.get('label', ''))
                    
                    # 只处理预测的知识点
                    if predicted_path:
                        if predicted_path == true_path:
                            knowledge_point_stats[predicted_path]['tp'] += 1
                        else:
                            knowledge_point_stats[predicted_path]['fp'] += 1
                            
                except json.JSONDecodeError:
                    continue
                except Exception:
                    continue
        
        # 第二遍扫描计算FN
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    predicted_path = extract_knowledge_path(data.get('predict', ''))
                    true_path = extract_knowledge_path(data.get('label', ''))
                    
                    if true_path and true_path in knowledge_point_stats:
                        if predicted_path != true_path:
                            knowledge_point_stats[true_path]['fn'] += 1
                            
                except:
                    continue
                    
    except Exception as e:
        print(f"读取预测文件时出错: {e}")
        return None
    
    # 计算指标
    results = []
    for knowledge_point, stats in knowledge_point_stats.items():
        tp = stats['tp']
        fp = stats['fp'] 
        fn = stats['fn']
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        test_samples = true_label_counts.get(knowledge_point, 0)
        
        results.append({
            '知识点': knowledge_point,
            'TP': tp,
            'FP': fp,
            'FN': fn,
            'Precision': round(precision, 4),
            'Recall': round(recall, 4),
            'F1': round(f1, 4),
            '测试集样本数': test_samples,
            '数据集': dataset_name
        })
    
    df = pd.DataFrame(results)
    
    # 计算宏平均
    macro_precision = df['Precision'].mean()
    macro_recall = df['Recall'].mean()
    macro_f1 = df['F1'].mean()
    
    print(f"预测知识点数: {len(df)}")
    print(f"宏平均 Precision: {macro_precision:.4f}")
    print(f"宏平均 Recall: {macro_recall:.4f}")
    print(f"宏平均 F1: {macro_f1:.4f}")
    
    return df, {
        'dataset': dataset_name,
        'predicted_classes': len(df),
        'test_samples': len(test_data),
        'unique_test_classes': len(true_label_counts),
        'macro_precision': macro_precision,
        'macro_recall': macro_recall,
        'macro_f1': macro_f1
    }

def main():
    """主函数"""
    print("=== 数据集差异分析 ===")
    
    # 定义数据集配置
    datasets = [
        {
            'name': '6000题数据集',
            'predictions': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-09-13-54_6047/generated_predictions.jsonl',
            'test': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/data/math1_test.json'
        },
        {
            'name': '8月数据集(num10)',
            'predictions': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-17-22-14_num10/generated_predictions.jsonl',
            'test': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/data/math1_test_num10.json'
        }
    ]
    
    all_results = []
    summary_stats = []
    
    for dataset in datasets:
        if os.path.exists(dataset['predictions']) and os.path.exists(dataset['test']):
            df, stats = analyze_prediction_results(
                dataset['predictions'], 
                dataset['test'], 
                dataset['name']
            )
            if df is not None:
                all_results.append(df)
                summary_stats.append(stats)
        else:
            print(f"\n❌ {dataset['name']} 文件不存在")
            print(f"预测文件: {dataset['predictions']}")
            print(f"测试文件: {dataset['test']}")
    
    if len(all_results) >= 2:
        # 生成对比分析
        print("\n" + "="*60)
        print("数据集对比分析")
        print("="*60)
        
        for stats in summary_stats:
            print(f"\n{stats['dataset']}:")
            print(f"  测试样本数: {stats['test_samples']}")
            print(f"  测试集唯一类别数: {stats['unique_test_classes']}")
            print(f"  预测类别数: {stats['predicted_classes']}")
            print(f"  宏平均 Precision: {stats['macro_precision']:.4f}")
            print(f"  宏平均 Recall: {stats['macro_recall']:.4f}")
            print(f"  宏平均 F1: {stats['macro_f1']:.4f}")
        
        # 计算差异
        if len(summary_stats) == 2:
            diff_precision = summary_stats[0]['macro_precision'] - summary_stats[1]['macro_precision']
            diff_recall = summary_stats[0]['macro_recall'] - summary_stats[1]['macro_recall']
            diff_f1 = summary_stats[0]['macro_f1'] - summary_stats[1]['macro_f1']
            
            print(f"\n指标差异 ({summary_stats[0]['dataset']} - {summary_stats[1]['dataset']}):")
            print(f"  Precision差异: {diff_precision:+.4f}")
            print(f"  Recall差异: {diff_recall:+.4f}")
            print(f"  F1差异: {diff_f1:+.4f}")
        
        # 保存详细结果
        combined_df = pd.concat(all_results, ignore_index=True)

        # 确保results目录存在
        import os
        results_dir = '../../results/dataset_analysis'
        os.makedirs(results_dir, exist_ok=True)

        output_file = os.path.join(results_dir, f"dataset_comparison_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            combined_df.to_excel(writer, sheet_name='所有结果', index=False)
            
            # 按数据集分别保存
            for dataset_name in combined_df['数据集'].unique():
                df_subset = combined_df[combined_df['数据集'] == dataset_name].copy()
                df_subset = df_subset.drop('数据集', axis=1)
                sheet_name = dataset_name.replace('数据集', '').replace('(', '_').replace(')', '')
                df_subset.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"\n✅ 详细分析结果已保存到: {output_file}")
    
    else:
        print("\n❌ 需要至少两个数据集才能进行对比分析")

if __name__ == "__main__":
    main()
