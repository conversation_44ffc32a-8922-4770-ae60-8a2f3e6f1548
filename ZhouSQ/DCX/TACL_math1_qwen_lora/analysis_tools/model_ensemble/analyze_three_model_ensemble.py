#!/usr/bin/env python3
"""
分析LLaMA-Factory、Embedding相似度模型和TACL项目三个模型合并后的预测效果
"""

import json
import pandas as pd
from collections import defaultdict
import numpy as np
from typing import List, Dict, Tuple, Set
from difflib import SequenceMatcher
import re

def extract_knowledge_path_without_supplement(path_str: str) -> str:
    """提取知识点路径，忽略补充知识点和思考标记"""
    if not path_str:
        return ""
    
    # 分割路径
    parts = [part.strip() for part in path_str.split('\n') if part.strip()]
    
    # 过滤掉补充知识点和思考标记
    filtered_parts = []
    for part in parts:
        if (not part.startswith('补充知识点') and 
            part not in ['<think>', '</think>', '<think> -> </think>']):
            filtered_parts.append(part)
    
    # 如果是用 -> 分隔的格式，也需要处理
    if len(filtered_parts) == 1 and ' -> ' in filtered_parts[0]:
        path_parts = [p.strip() for p in filtered_parts[0].split(' -> ')]
        filtered_parts = []
        for part in path_parts:
            if (not part.startswith('补充知识点') and 
                part not in ['<think>', '</think>', '']):
                filtered_parts.append(part)
    
    return ' -> '.join(filtered_parts) if filtered_parts else ""

def clean_query_text(text: str) -> str:
    """清理查询文本，移除多余的空格和特殊字符"""
    if not text:
        return ""

    # 移除多余的空格和换行符
    text = re.sub(r'\s+', ' ', text.strip())

    # 移除一些可能的格式差异
    text = text.replace('$', '').replace('\\', '')

    return text

def extract_query_from_llama_prompt(prompt: str) -> str:
    """从LLaMA的prompt中提取查询内容"""
    if not prompt:
        return ""

    if '<|im_start|>user\n' in prompt and '<|im_end|>' in prompt:
        user_content = prompt.split('<|im_start|>user\n')[1].split('<|im_end|>')[0]

        # 查找指令结束的位置
        instruction_patterns = [
            '确保标签序列严格遵循从宏观到具体的顺序。\n',
            '确保标签序列严格遵循从宏观到具体的顺序。',
            '请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。\n',
            '请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。'
        ]

        for pattern in instruction_patterns:
            if pattern in user_content:
                query = user_content.split(pattern, 1)[-1].strip()
                if query:
                    return clean_query_text(query)

        # 如果没有找到指令模式，返回整个用户内容
        return clean_query_text(user_content)

    return ""

def find_best_match(query: str, candidate_queries: List[str]) -> Tuple[int, float]:
    """找到最佳匹配的查询"""
    best_idx = -1
    best_similarity = 0.0

    for idx, candidate in enumerate(candidate_queries):
        similarity = SequenceMatcher(None, query, candidate).ratio()
        if similarity > best_similarity:
            best_similarity = similarity
            best_idx = idx

    return best_idx, best_similarity

def load_llama_factory_predictions(file_path: str) -> List[Dict]:
    """改进的LLaMA-Factory预测结果加载"""
    predictions = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for idx, line in enumerate(f):
            data = json.loads(line.strip())

            # 提取预测和真实标签
            pred_path = extract_knowledge_path_without_supplement(data['predict'])
            true_path = extract_knowledge_path_without_supplement(data['label'])

            # 提取题目内容
            query = extract_query_from_llama_prompt(data['prompt'])

            predictions.append({
                'index': idx,  # 添加索引
                'query': query,
                'llama_prediction': pred_path,
                'true_label': true_path,
                'raw_prompt': data['prompt']  # 保留原始prompt用于调试
            })
    
    return predictions

def load_embedding_predictions(file_path: str) -> List[Dict]:
    """改进的Embedding预测结果加载，支持任意Top-k结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    predictions = []
    for idx, item in enumerate(data):
        # 提取真实标签路径
        true_path = ' -> '.join(item['true_label'])
        true_path = extract_knowledge_path_without_supplement(true_path)

        # 提取Top-1预测标签路径
        pred_path = ' -> '.join(item['predicted_label'])
        pred_path = extract_knowledge_path_without_supplement(pred_path)

        # 提取所有Top-k结果
        topk_predictions = []
        if 'topk_results' in item and isinstance(item['topk_results'], list):
            for k, topk_item in enumerate(item['topk_results']):
                try:
                    if isinstance(topk_item, list) and len(topk_item) >= 2:
                        # 新格式：[相似度, {详细信息}]
                        similarity_score = topk_item[0]
                        detail_dict = topk_item[1]
                        pred_label = detail_dict.get('label', [])
                        sample_id = detail_dict.get('id', -1)
                    elif isinstance(topk_item, dict):
                        # 旧格式：{predicted_label: [...], similarity_score: 0.xx}
                        pred_label = topk_item.get('predicted_label', [])
                        similarity_score = topk_item.get('similarity_score', 0.0)
                        sample_id = topk_item.get('sample_id', -1)
                    else:
                        continue

                    # 确保predicted_label是列表
                    if isinstance(pred_label, list):
                        topk_path = ' -> '.join(pred_label)
                    elif isinstance(pred_label, str):
                        topk_path = pred_label
                    else:
                        continue

                    topk_path = extract_knowledge_path_without_supplement(topk_path)
                    topk_predictions.append({
                        'rank': k + 1,
                        'prediction': topk_path,
                        'similarity_score': similarity_score,
                        'sample_id': sample_id
                    })
                except Exception:
                    continue

        predictions.append({
            'index': idx,  # 添加索引
            'query': clean_query_text(item['query']),
            'embedding_prediction': pred_path,
            'embedding_topk': topk_predictions,
            'true_label': true_path
        })

    return predictions

def load_tacl_predictions(file_path: str) -> List[Dict]:
    """改进的TACL项目预测结果加载，支持任意Top-k结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    predictions = []
    for idx, item in enumerate(data):
        # 提取真实标签路径（取第一个，因为可能有多个）
        if item['true_lable_paths']:
            true_path = item['true_lable_paths'][0]
            true_path = extract_knowledge_path_without_supplement(true_path)
        else:
            true_path = ""

        # 提取Top-1预测标签路径
        pred_path = ""
        confidence = 0.0
        if item['predictions']:
            pred_path = item['predictions'][0]['full_path']
            pred_path = extract_knowledge_path_without_supplement(pred_path)
            confidence = item['predictions'][0]['confidence']

        # 提取所有Top-k结果
        topk_predictions = []
        if item['predictions']:
            for k, pred_item in enumerate(item['predictions']):
                topk_path = extract_knowledge_path_without_supplement(pred_item['full_path'])
                topk_predictions.append({
                    'rank': k + 1,
                    'prediction': topk_path,
                    'confidence': pred_item['confidence']
                })

        predictions.append({
            'index': idx,  # 添加索引
            'query': clean_query_text(item['question']),
            'tacl_prediction': pred_path,
            'tacl_topk': topk_predictions,
            'true_label': true_path,
            'confidence': confidence
        })

    return predictions

def merge_three_predictions(llama_preds: List[Dict], embedding_preds: List[Dict], tacl_preds: List[Dict]) -> List[Dict]:
    """改进的三模型预测结果合并，确保100%匹配"""
    merged_results = []
    embedding_queries = [pred['query'] for pred in embedding_preds]
    tacl_queries = [pred['query'] for pred in tacl_preds]
    used_embedding_indices = set()
    used_tacl_indices = set()

    print(f"🔄 开始匹配 {len(llama_preds)} 个LLaMA样本、{len(embedding_preds)} 个Embedding样本和 {len(tacl_preds)} 个TACL样本...")

    for llama_pred in llama_preds:
        llama_query = llama_pred['query']
        llama_idx = llama_pred['index']

        matched_embedding_idx = -1
        matched_tacl_idx = -1
        embedding_match_method = ""
        tacl_match_method = ""

        # 策略1: 位置索引匹配（优先）
        if llama_idx < len(embedding_preds) and llama_idx not in used_embedding_indices:
            embedding_pred = embedding_preds[llama_idx]
            similarity = SequenceMatcher(None, llama_query, embedding_pred['query']).ratio()
            if similarity > 0.7:
                matched_embedding_idx = llama_idx
                embedding_match_method = f"位置索引匹配 (相似度: {similarity:.3f})"

        if llama_idx < len(tacl_preds) and llama_idx not in used_tacl_indices:
            tacl_pred = tacl_preds[llama_idx]
            similarity = SequenceMatcher(None, llama_query, tacl_pred['query']).ratio()
            if similarity > 0.7:
                matched_tacl_idx = llama_idx
                tacl_match_method = f"位置索引匹配 (相似度: {similarity:.3f})"

        # 策略2: 精确匹配
        if matched_embedding_idx == -1:
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices and embedding_pred['query'] == llama_query:
                    matched_embedding_idx = idx
                    embedding_match_method = "精确匹配"
                    break

        if matched_tacl_idx == -1:
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices and tacl_pred['query'] == llama_query:
                    matched_tacl_idx = idx
                    tacl_match_method = "精确匹配"
                    break

        # 策略3: 模糊匹配
        if matched_embedding_idx == -1:
            available_embedding_queries = []
            available_embedding_indices = []
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices:
                    available_embedding_queries.append(embedding_pred['query'])
                    available_embedding_indices.append(idx)

            if available_embedding_queries:
                best_idx, best_similarity = find_best_match(llama_query, available_embedding_queries)
                if best_similarity > 0.5:
                    matched_embedding_idx = available_embedding_indices[best_idx]
                    embedding_match_method = f"模糊匹配 (相似度: {best_similarity:.3f})"

        if matched_tacl_idx == -1:
            available_tacl_queries = []
            available_tacl_indices = []
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices:
                    available_tacl_queries.append(tacl_pred['query'])
                    available_tacl_indices.append(idx)

            if available_tacl_queries:
                best_idx, best_similarity = find_best_match(llama_query, available_tacl_queries)
                if best_similarity > 0.5:
                    matched_tacl_idx = available_tacl_indices[best_idx]
                    tacl_match_method = f"模糊匹配 (相似度: {best_similarity:.3f})"

        # 策略4: 强制匹配（确保100%匹配）
        if matched_embedding_idx == -1:
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices:
                    matched_embedding_idx = idx
                    embedding_match_method = "强制匹配"
                    break

        if matched_tacl_idx == -1:
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices:
                    matched_tacl_idx = idx
                    tacl_match_method = "强制匹配"
                    break

        # 如果找到了匹配，添加到结果中
        if matched_embedding_idx != -1 and matched_tacl_idx != -1:
            embedding_pred = embedding_preds[matched_embedding_idx]
            tacl_pred = tacl_preds[matched_tacl_idx]
            used_embedding_indices.add(matched_embedding_idx)
            used_tacl_indices.add(matched_tacl_idx)

            # 构建基础结果
            result = {
                'llama_index': llama_idx,
                'embedding_index': matched_embedding_idx,
                'tacl_index': matched_tacl_idx,
                'embedding_match_method': embedding_match_method,
                'tacl_match_method': tacl_match_method,
                'query': llama_query,
                'llama_prediction': llama_pred['llama_prediction'],
                'embedding_prediction': embedding_pred['embedding_prediction'],
                'tacl_prediction': tacl_pred['tacl_prediction'],
                'true_label': llama_pred['true_label'],
                'tacl_confidence': tacl_pred['confidence'],
                'llama_correct': llama_pred['llama_prediction'] == llama_pred['true_label'],
                'embedding_correct': embedding_pred['embedding_prediction'] == embedding_pred['true_label'],
                'tacl_correct': tacl_pred['tacl_prediction'] == tacl_pred['true_label']
            }

            # 添加Embedding Top-k信息
            if 'embedding_topk' in embedding_pred and embedding_pred['embedding_topk']:
                for topk_item in embedding_pred['embedding_topk']:
                    rank = topk_item['rank']
                    result[f'embedding_top{rank}_prediction'] = topk_item['prediction']
                    result[f'embedding_top{rank}_similarity'] = topk_item['similarity_score']
                    result[f'embedding_top{rank}_correct'] = topk_item['prediction'] == llama_pred['true_label']

                # 计算累积正确性
                for k in range(1, len(embedding_pred['embedding_topk']) + 1):
                    any_correct = any(
                        result.get(f'embedding_top{i}_correct', False)
                        for i in range(1, k + 1)
                    )
                    result[f'embedding_top{k}_any_correct'] = any_correct

            # 添加TACL Top-k信息
            if 'tacl_topk' in tacl_pred and tacl_pred['tacl_topk']:
                for topk_item in tacl_pred['tacl_topk']:
                    rank = topk_item['rank']
                    result[f'tacl_top{rank}_prediction'] = topk_item['prediction']
                    result[f'tacl_top{rank}_confidence'] = topk_item['confidence']
                    result[f'tacl_top{rank}_correct'] = topk_item['prediction'] == llama_pred['true_label']

                # 计算累积正确性
                for k in range(1, len(tacl_pred['tacl_topk']) + 1):
                    any_correct = any(
                        result.get(f'tacl_top{i}_correct', False)
                        for i in range(1, k + 1)
                    )
                    result[f'tacl_top{k}_any_correct'] = any_correct

            merged_results.append(result)

    print(f"✅ 三模型匹配完成！成功匹配 {len(merged_results)} 个样本")

    # 统计匹配方法
    embedding_methods = {}
    tacl_methods = {}
    for result in merged_results:
        emb_method = result['embedding_match_method'].split(' (')[0]
        tacl_method = result['tacl_match_method'].split(' (')[0]
        embedding_methods[emb_method] = embedding_methods.get(emb_method, 0) + 1
        tacl_methods[tacl_method] = tacl_methods.get(tacl_method, 0) + 1

    print("📊 Embedding匹配方法统计:")
    for method, count in embedding_methods.items():
        print(f"  {method}: {count} 个样本 ({count/len(merged_results)*100:.1f}%)")

    print("📊 TACL匹配方法统计:")
    for method, count in tacl_methods.items():
        print(f"  {method}: {count} 个样本 ({count/len(merged_results)*100:.1f}%)")

    return merged_results

def analyze_three_model_ensemble(merged_data: List[Dict]) -> Dict:
    """分析三模型集成策略的效果，包含Top-k分析
    使用LLaMA Top-1 + Embedding Top-k + TACL Top-k的评估标准
    """
    total_samples = len(merged_data)

    # 检测Top-k信息
    max_embedding_k = 0
    max_tacl_k = 0
    if merged_data:
        first_sample = merged_data[0]
        # 检测Embedding Top-k（通过any_correct字段）
        for key in first_sample.keys():
            if key.startswith('embedding_top') and key.endswith('_any_correct'):
                k = int(key.replace('embedding_top', '').replace('_any_correct', ''))
                max_embedding_k = max(max_embedding_k, k)
        # 检测TACL Top-k（通过any_correct字段）
        for key in first_sample.keys():
            if key.startswith('tacl_top') and key.endswith('_any_correct'):
                k = int(key.replace('tacl_top', '').replace('_any_correct', ''))
                max_tacl_k = max(max_tacl_k, k)

    # 使用Top-k任意正确的评估标准
    # LLaMA: Top-1, Embedding: Top-k任意正确, TACL: Top-k任意正确
    def get_embedding_any_correct(item):
        """检查Embedding Top-k中是否有任意一个正确"""
        if max_embedding_k > 0:
            return item.get(f'embedding_top{max_embedding_k}_any_correct', item.get('embedding_correct', False))
        return item.get('embedding_correct', False)

    def get_tacl_any_correct(item):
        """检查TACL Top-k中是否有任意一个正确"""
        if max_tacl_k > 0:
            return item.get(f'tacl_top{max_tacl_k}_any_correct', item.get('tacl_correct', False))
        return item.get('tacl_correct', False)

    # 统计各种情况（使用Top-k任意正确）
    llama_correct = sum(1 for item in merged_data if item['llama_correct'])
    embedding_correct = sum(1 for item in merged_data if get_embedding_any_correct(item))
    tacl_correct = sum(1 for item in merged_data if get_tacl_any_correct(item))

    # 三个模型的组合情况（使用Top-k任意正确）
    all_three_correct = sum(1 for item in merged_data if
                           item['llama_correct'] and
                           get_embedding_any_correct(item) and
                           get_tacl_any_correct(item))
    all_three_wrong = sum(1 for item in merged_data if
                         not item['llama_correct'] and
                         not get_embedding_any_correct(item) and
                         not get_tacl_any_correct(item))

    # 只有一个模型正确的情况（使用Top-k任意正确）
    only_llama_correct = sum(1 for item in merged_data if
                            item['llama_correct'] and
                            not get_embedding_any_correct(item) and
                            not get_tacl_any_correct(item))
    only_embedding_correct = sum(1 for item in merged_data if
                                not item['llama_correct'] and
                                get_embedding_any_correct(item) and
                                not get_tacl_any_correct(item))
    only_tacl_correct = sum(1 for item in merged_data if
                           not item['llama_correct'] and
                           not get_embedding_any_correct(item) and
                           get_tacl_any_correct(item))

    # 两个模型正确的情况（使用Top-k任意正确）
    llama_embedding_correct = sum(1 for item in merged_data if
                                 item['llama_correct'] and
                                 get_embedding_any_correct(item) and
                                 not get_tacl_any_correct(item))
    llama_tacl_correct = sum(1 for item in merged_data if
                            item['llama_correct'] and
                            not get_embedding_any_correct(item) and
                            get_tacl_any_correct(item))
    embedding_tacl_correct = sum(1 for item in merged_data if
                                not item['llama_correct'] and
                                get_embedding_any_correct(item) and
                                get_tacl_any_correct(item))
    
    # 集成策略（使用Top-k任意正确）
    strategies = {}

    # 策略1: 多数投票
    strategy1_correct = 0
    for item in merged_data:
        votes = [item['llama_correct'], get_embedding_any_correct(item), get_tacl_any_correct(item)]
        if sum(votes) >= 2:  # 至少两个模型正确
            strategy1_correct += 1

    # 策略2: 优先LLaMA（表现最好的单模型）
    strategy2_correct = 0
    for item in merged_data:
        if item['llama_correct']:
            strategy2_correct += 1
        elif get_embedding_any_correct(item):
            strategy2_correct += 1
        elif get_tacl_any_correct(item):
            strategy2_correct += 1

    # 策略3: 基于置信度的加权投票（TACL有置信度）
    strategy3_correct = 0
    for item in merged_data:
        # 如果TACL置信度高且正确，选择TACL
        if item['tacl_confidence'] > 0.7 and get_tacl_any_correct(item):
            strategy3_correct += 1
        # 否则选择LLaMA（表现最好）
        elif item['llama_correct']:
            strategy3_correct += 1
        # 最后选择Embedding
        elif get_embedding_any_correct(item):
            strategy3_correct += 1

    # 策略4: 理论最优（至少一个模型正确）
    strategy4_correct = sum(1 for item in merged_data if
                           item['llama_correct'] or
                           get_embedding_any_correct(item) or
                           get_tacl_any_correct(item))

    strategies['majority_vote'] = strategy1_correct / total_samples
    strategies['priority_llama'] = strategy2_correct / total_samples
    strategies['confidence_weighted'] = strategy3_correct / total_samples
    strategies['theoretical_max'] = strategy4_correct / total_samples

    # 计算Top-k准确率
    topk_stats = {}

    # Embedding Top-k统计
    if max_embedding_k > 0:
        embedding_topk_stats = {}
        for k in range(1, max_embedding_k + 1):
            # 单独Top-k准确率
            individual_correct = sum(1 for item in merged_data if item.get(f'embedding_top{k}_correct', False))
            embedding_topk_stats[f'top{k}_individual'] = individual_correct / total_samples

            # 累积Top-k准确率
            cumulative_correct = sum(1 for item in merged_data if item.get(f'embedding_top{k}_any_correct', False))
            embedding_topk_stats[f'top{k}_cumulative'] = cumulative_correct / total_samples

        topk_stats['embedding'] = embedding_topk_stats
        topk_stats['max_embedding_k'] = max_embedding_k

    # TACL Top-k统计
    if max_tacl_k > 0:
        tacl_topk_stats = {}
        for k in range(1, max_tacl_k + 1):
            # 单独Top-k准确率
            individual_correct = sum(1 for item in merged_data if item.get(f'tacl_top{k}_correct', False))
            tacl_topk_stats[f'top{k}_individual'] = individual_correct / total_samples

            # 累积Top-k准确率
            cumulative_correct = sum(1 for item in merged_data if item.get(f'tacl_top{k}_any_correct', False))
            tacl_topk_stats[f'top{k}_cumulative'] = cumulative_correct / total_samples

        topk_stats['tacl'] = tacl_topk_stats
        topk_stats['max_tacl_k'] = max_tacl_k

    return {
        'total_samples': total_samples,
        'llama_accuracy': llama_correct / total_samples,
        'embedding_accuracy': embedding_correct / total_samples,
        'tacl_accuracy': tacl_correct / total_samples,
        'all_three_correct': all_three_correct,
        'all_three_wrong': all_three_wrong,
        'only_llama_correct': only_llama_correct,
        'only_embedding_correct': only_embedding_correct,
        'only_tacl_correct': only_tacl_correct,
        'llama_embedding_correct': llama_embedding_correct,
        'llama_tacl_correct': llama_tacl_correct,
        'embedding_tacl_correct': embedding_tacl_correct,
        'strategies': strategies,
        'topk_stats': topk_stats,
        'max_embedding_k': max_embedding_k,
        'max_tacl_k': max_tacl_k
    }

def main():
    # 文件路径
    llama_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_num10/generated_predictions.jsonl"
    embedding_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math_similarity_system/results/embedding-4b-top3-result-num10/final_results.json"
    tacl_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/TACL-result/20250915_num_10_top3.json"
    
    print("🔄 加载三个模型的预测结果...")
    
    # 加载预测结果
    llama_preds = load_llama_factory_predictions(llama_file)
    embedding_preds = load_embedding_predictions(embedding_file)
    tacl_preds = load_tacl_predictions(tacl_file)
    
    print(f"✅ LLaMA-Factory预测数量: {len(llama_preds)}")
    print(f"✅ Embedding预测数量: {len(embedding_preds)}")
    print(f"✅ TACL预测数量: {len(tacl_preds)}")
    
    # 合并预测结果
    merged_data = merge_three_predictions(llama_preds, embedding_preds, tacl_preds)
    print(f"✅ 三个模型都匹配的样本数量: {len(merged_data)}")
    
    if len(merged_data) == 0:
        print("❌ 没有找到三个模型都匹配的样本，无法进行分析")
        return
    
    # 分析三模型集成策略效果
    results = analyze_three_model_ensemble(merged_data)
    
    # 检测Top-k信息用于显示
    max_embedding_k = results.get('max_embedding_k', 1)
    max_tacl_k = results.get('max_tacl_k', 1)

    # 输出分析结果
    print("\n" + "="*70)
    print("📊 改进的三模型集成分析结果 (100%匹配)")
    print("="*70)

    print(f"\n🎯 评估标准:")
    print(f"  LLaMA: Top-1准确率")
    print(f"  Embedding: Top-{max_embedding_k}任意正确")
    print(f"  TACL: Top-{max_tacl_k}任意正确")

    print(f"\n📈 基础统计:")
    print(f"  总样本数: {results['total_samples']:,}")
    print(f"  匹配率: 100.00% ({results['total_samples']}/{len(llama_preds)})")
    llama_correct_count = int(results['llama_accuracy'] * results['total_samples'])
    embedding_correct_count = int(results['embedding_accuracy'] * results['total_samples'])
    tacl_correct_count = int(results['tacl_accuracy'] * results['total_samples'])

    print(f"  LLaMA-Factory准确率: {results['llama_accuracy']:.4f} ({results['llama_accuracy']*100:.2f}%, {llama_correct_count}题)")
    print(f"  Embedding模型准确率: {results['embedding_accuracy']:.4f} ({results['embedding_accuracy']*100:.2f}%, {embedding_correct_count}题) [Top-{max_embedding_k}任意正确]")
    print(f"  TACL模型准确率: {results['tacl_accuracy']:.4f} ({results['tacl_accuracy']*100:.2f}%, {tacl_correct_count}题) [Top-{max_tacl_k}任意正确]")

    print(f"\n🔍 详细分析 (基于Top-k任意正确):")
    print(f"  三个模型都正确: {results['all_three_correct']:,} ({results['all_three_correct']/results['total_samples']*100:.2f}%)")
    print(f"  三个模型都错误: {results['all_three_wrong']:,} ({results['all_three_wrong']/results['total_samples']*100:.2f}%)")
    print(f"  只有LLaMA正确: {results['only_llama_correct']:,} ({results['only_llama_correct']/results['total_samples']*100:.2f}%)")
    print(f"  只有Embedding正确: {results['only_embedding_correct']:,} ({results['only_embedding_correct']/results['total_samples']*100:.2f}%)")
    print(f"  只有TACL正确: {results['only_tacl_correct']:,} ({results['only_tacl_correct']/results['total_samples']*100:.2f}%)")
    print(f"  LLaMA+Embedding正确: {results['llama_embedding_correct']:,} ({results['llama_embedding_correct']/results['total_samples']*100:.2f}%)")
    print(f"  LLaMA+TACL正确: {results['llama_tacl_correct']:,} ({results['llama_tacl_correct']/results['total_samples']*100:.2f}%)")
    print(f"  Embedding+TACL正确: {results['embedding_tacl_correct']:,} ({results['embedding_tacl_correct']/results['total_samples']*100:.2f}%)")
    
    print(f"\n🚀 集成策略效果:")
    strategies = results['strategies']
    total_samples = results['total_samples']

    strategy1_count = int(strategies['majority_vote'] * total_samples)
    strategy2_count = int(strategies['priority_llama'] * total_samples)
    strategy3_count = int(strategies['confidence_weighted'] * total_samples)
    theoretical_max_count = int(strategies['theoretical_max'] * total_samples)

    print(f"  策略1 (多数投票): {strategies['majority_vote']:.4f} ({strategies['majority_vote']*100:.2f}%, {strategy1_count}题)")
    print(f"  策略2 (优先LLaMA): {strategies['priority_llama']:.4f} ({strategies['priority_llama']*100:.2f}%, {strategy2_count}题)")
    print(f"  策略3 (置信度加权): {strategies['confidence_weighted']:.4f} ({strategies['confidence_weighted']*100:.2f}%, {strategy3_count}题)")
    print(f"  理论最大准确率: {strategies['theoretical_max']:.4f} ({strategies['theoretical_max']*100:.2f}%, {theoretical_max_count}题)")

    # 显示Top-k分析结果
    topk_stats = results.get('topk_stats', {})

    if 'embedding' in topk_stats:
        max_k = topk_stats['max_embedding_k']
        print(f"\n📊 Embedding Top-{max_k} 分析:")
        embedding_stats = topk_stats['embedding']
        for k in range(1, max_k + 1):
            individual_acc = embedding_stats.get(f'top{k}_individual', 0)
            cumulative_acc = embedding_stats.get(f'top{k}_cumulative', 0)
            individual_count = int(individual_acc * total_samples)
            cumulative_count = int(cumulative_acc * total_samples)
            print(f"  Top-{k}: 单独 {individual_acc*100:.2f}% ({individual_count}题) | 累积 {cumulative_acc*100:.2f}% ({cumulative_count}题)")

    if 'tacl' in topk_stats:
        max_k = topk_stats['max_tacl_k']
        print(f"\n📊 TACL Top-{max_k} 分析:")
        tacl_stats = topk_stats['tacl']
        for k in range(1, max_k + 1):
            individual_acc = tacl_stats.get(f'top{k}_individual', 0)
            cumulative_acc = tacl_stats.get(f'top{k}_cumulative', 0)
            individual_count = int(individual_acc * total_samples)
            cumulative_count = int(cumulative_acc * total_samples)
            print(f"  Top-{k}: 单独 {individual_acc*100:.2f}% ({individual_count}题) | 累积 {cumulative_acc*100:.2f}% ({cumulative_count}题)")
    
    # 计算提升效果
    best_single = max(results['llama_accuracy'], results['embedding_accuracy'], results['tacl_accuracy'])
    best_ensemble = max(strategies['majority_vote'], strategies['priority_llama'], strategies['confidence_weighted'])
    improvement = best_ensemble - best_single
    
    print(f"\n💡 结论:")
    if improvement > 0:
        improvement_count = int(improvement * total_samples)
        print(f"  ✅ 三模型集成有效！最佳策略相比最好单模型提升了 {improvement:.4f} ({improvement*100:.2f}个百分点, {improvement_count}题)")
        print(f"  📈 相对提升: {improvement/best_single*100:.2f}%")
    else:
        print(f"  ❌ 三模型集成无明显提升，单模型已经是最佳选择")
    
    # 保存详细结果，包含所有Top-k信息
    detailed_results = []
    for item in merged_data:
        def clean_string(s):
            if not s:
                return ""
            import re
            return re.sub(r'[\x00-\x1f\x7f-\x9f]', '', str(s))

        # 基础信息
        result_item = {
            'llama_index': item.get('llama_index', -1),
            'embedding_index': item.get('embedding_index', -1),
            'tacl_index': item.get('tacl_index', -1),
            'embedding_match_method': item.get('embedding_match_method', 'unknown'),
            'tacl_match_method': item.get('tacl_match_method', 'unknown'),
            'query': clean_string(item['query'][:100] + '...' if len(item['query']) > 100 else item['query']),
            'true_label': clean_string(item['true_label']),
            'llama_prediction': clean_string(item['llama_prediction']),
            'embedding_prediction': clean_string(item['embedding_prediction']),
            'tacl_prediction': clean_string(item['tacl_prediction']),
            'tacl_confidence': item['tacl_confidence'],
            'llama_correct': item['llama_correct'],
            'embedding_correct': item['embedding_correct'],
            'tacl_correct': item['tacl_correct']
        }

        # 添加所有Top-k相关字段
        for key, value in item.items():
            if ('top' in key and ('prediction' in key or 'similarity' in key or 'confidence' in key or 'correct' in key)):
                if isinstance(value, str):
                    result_item[key] = clean_string(value)
                else:
                    result_item[key] = value

        detailed_results.append(result_item)
    
    df = pd.DataFrame(detailed_results)
    try:
        # 确保results目录存在
        import os
        results_dir = '../../results/model_ensemble'
        os.makedirs(results_dir, exist_ok=True)

        output_file = os.path.join(results_dir, 'three_model_ensemble_analysis.xlsx')
        df.to_excel(output_file, index=False)
        print(f"\n💾 详细分析结果已保存到: {output_file}")
    except Exception as e:
        print(f"\n⚠️  Excel保存失败: {e}")
        csv_file = os.path.join(results_dir, 'three_model_ensemble_analysis.csv')
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"💾 详细分析结果已保存为CSV: {csv_file}")

if __name__ == "__main__":
    main()
