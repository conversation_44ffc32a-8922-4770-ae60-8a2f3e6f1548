#!/usr/bin/env python3
"""
分析LLaMA-Factory微调模型和Embedding相似度模型合并后的预测效果
"""

import json
import pandas as pd
from collections import defaultdict
import numpy as np
from typing import List, Dict, Tuple, Set
from difflib import SequenceMatcher
import re

def extract_knowledge_path_without_supplement(path_str: str) -> str:
    """提取知识点路径，忽略补充知识点和思考标记"""
    if not path_str:
        return ""

    # 分割路径
    parts = [part.strip() for part in path_str.split('\n') if part.strip()]

    # 过滤掉补充知识点和思考标记
    filtered_parts = []
    for part in parts:
        if (not part.startswith('补充知识点') and
            part not in ['<think>', '</think>', '<think> -> </think>']):
            filtered_parts.append(part)

    # 如果是用 -> 分隔的格式，也需要处理
    if len(filtered_parts) == 1 and ' -> ' in filtered_parts[0]:
        path_parts = [p.strip() for p in filtered_parts[0].split(' -> ')]
        filtered_parts = []
        for part in path_parts:
            if (not part.startswith('补充知识点') and
                part not in ['<think>', '</think>', '']):
                filtered_parts.append(part)

    return ' -> '.join(filtered_parts) if filtered_parts else ""

def clean_query_text(text: str) -> str:
    """清理查询文本，移除多余的空格和特殊字符"""
    if not text:
        return ""

    # 移除多余的空格和换行符
    text = re.sub(r'\s+', ' ', text.strip())

    # 移除一些可能的格式差异
    text = text.replace('$', '').replace('\\', '')

    return text

def extract_query_from_llama_prompt(prompt: str) -> str:
    """从LLaMA的prompt中提取查询内容"""
    if not prompt:
        return ""

    if '<|im_start|>user\n' in prompt and '<|im_end|>' in prompt:
        user_content = prompt.split('<|im_start|>user\n')[1].split('<|im_end|>')[0]

        # 查找指令结束的位置
        instruction_patterns = [
            '确保标签序列严格遵循从宏观到具体的顺序。\n',
            '确保标签序列严格遵循从宏观到具体的顺序。',
            '请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。\n',
            '请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。'
        ]

        for pattern in instruction_patterns:
            if pattern in user_content:
                query = user_content.split(pattern, 1)[-1].strip()
                if query:
                    return clean_query_text(query)

        # 如果没有找到指令模式，返回整个用户内容
        return clean_query_text(user_content)

    return ""

def find_best_match(query: str, candidate_queries: List[str]) -> Tuple[int, float]:
    """找到最佳匹配的查询"""
    best_idx = -1
    best_similarity = 0.0

    for idx, candidate in enumerate(candidate_queries):
        similarity = SequenceMatcher(None, query, candidate).ratio()
        if similarity > best_similarity:
            best_similarity = similarity
            best_idx = idx

    return best_idx, best_similarity

def load_llama_factory_predictions(file_path: str) -> List[Dict]:
    """改进的LLaMA-Factory预测结果加载"""
    predictions = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for idx, line in enumerate(f):
            data = json.loads(line.strip())

            # 提取预测和真实标签
            pred_path = extract_knowledge_path_without_supplement(data['predict'])
            true_path = extract_knowledge_path_without_supplement(data['label'])

            # 提取题目内容
            query = extract_query_from_llama_prompt(data['prompt'])

            predictions.append({
                'index': idx,  # 添加索引
                'query': query,
                'llama_prediction': pred_path,
                'true_label': true_path,
                'raw_prompt': data['prompt']  # 保留原始prompt用于调试
            })

    return predictions

def load_embedding_predictions(file_path: str) -> List[Dict]:
    """改进的Embedding预测结果加载，支持top-k结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    predictions = []
    for idx, item in enumerate(data):
        # 提取真实标签路径
        true_path = ' -> '.join(item['true_label'])
        true_path = extract_knowledge_path_without_supplement(true_path)

        # 提取top-1预测标签路径（主要预测）
        pred_path = ' -> '.join(item['predicted_label'])
        pred_path = extract_knowledge_path_without_supplement(pred_path)

        # 提取top-k结果
        topk_predictions = []
        if 'topk_results' in item and isinstance(item['topk_results'], list):
            for k, topk_item in enumerate(item['topk_results']):
                try:
                    # 处理两种不同的数据结构
                    if isinstance(topk_item, dict) and 'predicted_label' in topk_item:
                        # 旧格式：字典结构 (Top-3数据)
                        pred_label = topk_item['predicted_label']
                        similarity_score = topk_item.get('similarity_score', 0.0)
                        sample_id = topk_item.get('sample_id', -1)
                    elif isinstance(topk_item, list) and len(topk_item) >= 2:
                        # 新格式：[相似度, {详细信息}] (Top-10数据)
                        similarity_score = topk_item[0]
                        detail_dict = topk_item[1]
                        pred_label = detail_dict.get('label', [])
                        sample_id = detail_dict.get('id', -1)
                    else:
                        continue

                    # 确保predicted_label是列表
                    if isinstance(pred_label, list):
                        topk_path = ' -> '.join(pred_label)
                    elif isinstance(pred_label, str):
                        topk_path = pred_label
                    else:
                        continue

                    topk_path = extract_knowledge_path_without_supplement(topk_path)
                    topk_predictions.append({
                        'rank': k + 1,
                        'prediction': topk_path,
                        'similarity_score': similarity_score,
                        'sample_id': sample_id
                    })
                except Exception as e:
                    continue

        predictions.append({
            'index': idx,  # 添加索引
            'query': clean_query_text(item['query']),
            'embedding_prediction': pred_path,  # top-1预测
            'embedding_topk': topk_predictions,  # top-k预测列表
            'true_label': true_path
        })

    return predictions

def merge_predictions(llama_preds: List[Dict], embedding_preds: List[Dict]) -> List[Dict]:
    """改进的预测结果合并，确保100%匹配"""
    merged_results = []
    embedding_queries = [pred['query'] for pred in embedding_preds]
    used_embedding_indices = set()

    print(f"🔄 开始匹配 {len(llama_preds)} 个LLaMA样本和 {len(embedding_preds)} 个Embedding样本...")

    for llama_pred in llama_preds:
        llama_query = llama_pred['query']
        llama_idx = llama_pred['index']

        matched_embedding_idx = -1
        match_method = ""

        # 策略1: 位置索引匹配（优先）
        if llama_idx < len(embedding_preds) and llama_idx not in used_embedding_indices:
            embedding_pred = embedding_preds[llama_idx]
            # 检查相似度是否足够高
            similarity = SequenceMatcher(None, llama_query, embedding_pred['query']).ratio()
            if similarity > 0.7:  # 相似度阈值
                matched_embedding_idx = llama_idx
                match_method = f"位置索引匹配 (相似度: {similarity:.3f})"

        # 策略2: 精确匹配
        if matched_embedding_idx == -1:
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices and embedding_pred['query'] == llama_query:
                    matched_embedding_idx = idx
                    match_method = "精确匹配"
                    break

        # 策略3: 模糊匹配
        if matched_embedding_idx == -1:
            available_queries = []
            available_indices = []
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices:
                    available_queries.append(embedding_pred['query'])
                    available_indices.append(idx)

            if available_queries:
                best_idx, best_similarity = find_best_match(llama_query, available_queries)
                if best_similarity > 0.5:  # 降低阈值确保匹配
                    matched_embedding_idx = available_indices[best_idx]
                    match_method = f"模糊匹配 (相似度: {best_similarity:.3f})"

        # 策略4: 强制匹配（确保100%匹配）
        if matched_embedding_idx == -1:
            # 找到第一个未使用的embedding样本
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices:
                    matched_embedding_idx = idx
                    match_method = "强制匹配"
                    break

        if matched_embedding_idx != -1:
            embedding_pred = embedding_preds[matched_embedding_idx]
            used_embedding_indices.add(matched_embedding_idx)

            # 计算top-k准确率
            topk_correct = {}
            topk_predictions = {}
            for topk_item in embedding_pred.get('embedding_topk', []):
                rank = topk_item['rank']
                prediction = topk_item['prediction']
                is_correct = prediction == llama_pred['true_label']
                topk_correct[f'embedding_top{rank}_correct'] = is_correct
                topk_predictions[f'embedding_top{rank}_prediction'] = prediction
                topk_predictions[f'embedding_top{rank}_similarity'] = topk_item['similarity_score']

            # 计算top-k累积准确率（任意一个正确即为正确）
            topk_any_correct = {}
            for k in range(1, len(embedding_pred.get('embedding_topk', [])) + 1):
                any_correct = any(topk_correct.get(f'embedding_top{i}_correct', False) for i in range(1, k + 1))
                topk_any_correct[f'embedding_top{k}_any_correct'] = any_correct

            result_dict = {
                'llama_index': llama_idx,
                'embedding_index': matched_embedding_idx,
                'match_method': match_method,
                'query': llama_query,
                'llama_prediction': llama_pred['llama_prediction'],
                'embedding_prediction': embedding_pred['embedding_prediction'],
                'true_label': llama_pred['true_label'],
                'llama_correct': llama_pred['llama_prediction'] == llama_pred['true_label'],
                'embedding_correct': embedding_pred['embedding_prediction'] == embedding_pred['true_label']
            }

            # 添加top-k结果
            result_dict.update(topk_correct)
            result_dict.update(topk_predictions)
            result_dict.update(topk_any_correct)

            merged_results.append(result_dict)

    print(f"✅ 匹配完成！成功匹配 {len(merged_results)} 个样本")

    # 统计匹配方法
    match_methods = {}
    for result in merged_results:
        method = result['match_method'].split(' (')[0]  # 去掉相似度信息
        match_methods[method] = match_methods.get(method, 0) + 1

    print("📊 匹配方法统计:")
    for method, count in match_methods.items():
        print(f"  {method}: {count} 个样本 ({count/len(merged_results)*100:.1f}%)")

    return merged_results

def analyze_ensemble_strategies(merged_data: List[Dict]) -> Dict:
    """分析不同集成策略的效果，包括top-k结果"""
    total_samples = len(merged_data)

    # 基础统计
    llama_correct = sum(1 for item in merged_data if item['llama_correct'])
    embedding_correct = sum(1 for item in merged_data if item['embedding_correct'])
    both_correct = sum(1 for item in merged_data if item['llama_correct'] and item['embedding_correct'])
    both_wrong = sum(1 for item in merged_data if not item['llama_correct'] and not item['embedding_correct'])
    llama_only_correct = sum(1 for item in merged_data if item['llama_correct'] and not item['embedding_correct'])
    embedding_only_correct = sum(1 for item in merged_data if not item['llama_correct'] and item['embedding_correct'])

    # 检测最大k值
    max_k = 0
    for item in merged_data:
        for key in item.keys():
            if key.startswith('embedding_top') and ('_correct' in key or '_prediction' in key):
                # 提取k值，处理 embedding_top1_correct, embedding_top1_any_correct 等
                parts = key.split('top')[1].split('_')
                k = int(parts[0])
                max_k = max(max_k, k)
        if max_k > 0:  # 找到一个就够了
            break

    # Top-k准确率统计
    topk_stats = {}
    for k in range(1, max_k + 1):
        # 单个top-k准确率
        topk_key = f'embedding_top{k}_correct'
        if any(topk_key in item for item in merged_data):
            topk_correct = sum(1 for item in merged_data if item.get(topk_key, False))
            topk_stats[f'embedding_top{k}_accuracy'] = topk_correct / total_samples

        # 累积top-k准确率（top-1到top-k中任意一个正确）
        topk_any_key = f'embedding_top{k}_any_correct'
        if any(topk_any_key in item for item in merged_data):
            topk_any_correct = sum(1 for item in merged_data if item.get(topk_any_key, False))
            topk_stats[f'embedding_top{k}_any_accuracy'] = topk_any_correct / total_samples

    # 集成策略分析
    # 策略1: 优先LLaMA（LLaMA错误时选择Embedding top-1）
    strategy1_correct = llama_correct + embedding_only_correct

    # 策略2: 优先Embedding top-1（Embedding错误时选择LLaMA）
    strategy2_correct = embedding_correct + llama_only_correct

    # 策略3: LLaMA + Embedding top-k集成策略
    topk_ensemble_stats = {}
    for k in range(1, max_k + 1):
        topk_any_key = f'embedding_top{k}_any_correct'
        if any(topk_any_key in item for item in merged_data):
            # LLaMA正确 或 Embedding top-k中任意一个正确
            ensemble_correct = sum(1 for item in merged_data
                                 if item['llama_correct'] or item.get(topk_any_key, False))
            topk_ensemble_stats[f'llama_embedding_top{k}_ensemble_accuracy'] = ensemble_correct / total_samples

    # 理论最大准确率
    max_possible_accuracy = (total_samples - both_wrong) / total_samples

    result = {
        'total_samples': total_samples,
        'llama_accuracy': llama_correct / total_samples,
        'embedding_accuracy': embedding_correct / total_samples,
        'both_correct': both_correct,
        'both_wrong': both_wrong,
        'llama_only_correct': llama_only_correct,
        'embedding_only_correct': embedding_only_correct,
        'strategy1_accuracy': strategy1_correct / total_samples,  # 优先LLaMA
        'strategy2_accuracy': strategy2_correct / total_samples,  # 优先Embedding
        'max_possible_accuracy': max_possible_accuracy,
        'max_k': max_k
    }

    # 添加top-k统计
    result.update(topk_stats)
    result.update(topk_ensemble_stats)

    return result

def main():
    # 文件路径
    llama_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-17-22-14_num10/generated_predictions.jsonl"
    embedding_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math_similarity_system/results/embedding-4b-top10-result-num10/final_results.json"
    
    print("🔄 加载预测结果...")
    
    # 加载预测结果
    llama_preds = load_llama_factory_predictions(llama_file)
    embedding_preds = load_embedding_predictions(embedding_file)
    
    print(f"✅ LLaMA-Factory预测数量: {len(llama_preds)}")
    print(f"✅ Embedding预测数量: {len(embedding_preds)}")
    
    # 合并预测结果
    merged_data = merge_predictions(llama_preds, embedding_preds)
    print(f"✅ 成功匹配的样本数量: {len(merged_data)}")
    
    # 分析集成策略效果
    results = analyze_ensemble_strategies(merged_data)
    
    # 输出分析结果
    print("\n" + "="*60)
    print("📊 改进的模型集成分析结果 (100%匹配)")
    print("="*60)
    
    print(f"\n📈 基础统计:")
    print(f"  总样本数: {results['total_samples']:,}")
    print(f"  匹配率: 100.00% ({results['total_samples']}/{len(llama_preds)})")
    print(f"  LLaMA-Factory准确率: {results['llama_accuracy']:.4f} ({results['llama_accuracy']*100:.2f}%)")
    print(f"  Embedding模型准确率: {results['embedding_accuracy']:.4f} ({results['embedding_accuracy']*100:.2f}%)")
    
    print(f"\n🔍 详细分析:")
    print(f"  两个模型都正确: {results['both_correct']:,} ({results['both_correct']/results['total_samples']*100:.2f}%)")
    print(f"  两个模型都错误: {results['both_wrong']:,} ({results['both_wrong']/results['total_samples']*100:.2f}%)")
    print(f"  只有LLaMA正确: {results['llama_only_correct']:,} ({results['llama_only_correct']/results['total_samples']*100:.2f}%)")
    print(f"  只有Embedding正确: {results['embedding_only_correct']:,} ({results['embedding_only_correct']/results['total_samples']*100:.2f}%)")
    
    # 显示Top-k结果
    if results.get('max_k', 0) > 0:
        print(f"\n� Embedding Top-k准确率分析:")
        for k in range(1, results['max_k'] + 1):
            topk_acc_key = f'embedding_top{k}_accuracy'
            topk_any_acc_key = f'embedding_top{k}_any_accuracy'

            if topk_acc_key in results:
                print(f"  Top-{k}准确率: {results[topk_acc_key]:.4f} ({results[topk_acc_key]*100:.2f}%)")

            if topk_any_acc_key in results:
                print(f"  Top-{k}累积准确率: {results[topk_any_acc_key]:.4f} ({results[topk_any_acc_key]*100:.2f}%)")
    else:
        print(f"\n⚠️  没有检测到Top-k数据")

    print(f"\n🚀 集成策略效果:")
    print(f"  策略1 (优先LLaMA): {results['strategy1_accuracy']:.4f} ({results['strategy1_accuracy']*100:.2f}%)")
    print(f"  策略2 (优先Embedding): {results['strategy2_accuracy']:.4f} ({results['strategy2_accuracy']*100:.2f}%)")

    # 显示LLaMA + Embedding Top-k集成结果
    if results['max_k'] > 0:
        print(f"\n🎯 LLaMA + Embedding Top-k集成策略:")
        for k in range(1, results['max_k'] + 1):
            ensemble_key = f'llama_embedding_top{k}_ensemble_accuracy'
            if ensemble_key in results:
                print(f"  LLaMA + Embedding Top-{k}: {results[ensemble_key]:.4f} ({results[ensemble_key]*100:.2f}%)")

    print(f"  理论最大准确率: {results['max_possible_accuracy']:.4f} ({results['max_possible_accuracy']*100:.2f}%)")

    # 计算提升效果
    llama_baseline = results['llama_accuracy']
    embedding_baseline = results['embedding_accuracy']
    best_single = max(llama_baseline, embedding_baseline)

    # 找到最佳集成策略
    best_ensemble = max(results['strategy1_accuracy'], results['strategy2_accuracy'])
    if results['max_k'] > 0:
        for k in range(1, results['max_k'] + 1):
            ensemble_key = f'llama_embedding_top{k}_ensemble_accuracy'
            if ensemble_key in results:
                best_ensemble = max(best_ensemble, results[ensemble_key])

    improvement = best_ensemble - best_single
    
    print(f"\n💡 结论:")
    if improvement > 0:
        print(f"  ✅ 模型集成有效！最佳策略相比Embedding模型提升了 {improvement:.4f} ({improvement*100:.2f}个百分点)")
        print(f"  📈 相对提升: {improvement/embedding_baseline*100:.2f}%")
    else:
        print(f"  ❌ 模型集成无明显提升，Embedding模型已经是最佳选择")
    
    # 保存详细结果
    detailed_results = []
    for item in merged_data:
        # 清理字符串，移除可能导致Excel错误的特殊字符
        def clean_string(s):
            if not s:
                return ""
            # 移除控制字符和其他可能有问题的字符
            import re
            return re.sub(r'[\x00-\x1f\x7f-\x9f]', '', str(s))

        # 基础信息
        result_dict = {
            'llama_index': item.get('llama_index', -1),
            'embedding_index': item.get('embedding_index', -1),
            'match_method': item.get('match_method', 'unknown'),
            'query': clean_string(item['query'][:100] + '...' if len(item['query']) > 100 else item['query']),
            'true_label': clean_string(item['true_label']),
            'llama_prediction': clean_string(item['llama_prediction']),
            'embedding_prediction': clean_string(item['embedding_prediction']),
            'llama_correct': item['llama_correct'],
            'embedding_correct': item['embedding_correct'],
            'both_models_agree': item['llama_prediction'] == item['embedding_prediction']
        }

        # 添加所有top-k相关信息
        for key, value in item.items():
            if key.startswith('embedding_top') and ('correct' in key or 'prediction' in key or 'similarity' in key):
                if 'prediction' in key:
                    result_dict[key] = clean_string(value)
                else:
                    result_dict[key] = value

        detailed_results.append(result_dict)

    df = pd.DataFrame(detailed_results)
    try:
        # 确保results目录存在
        import os
        results_dir = '../../results/model_ensemble'
        os.makedirs(results_dir, exist_ok=True)

        output_file = os.path.join(results_dir, 'model_ensemble_analysis.xlsx')
        df.to_excel(output_file, index=False)
        print(f"\n💾 详细分析结果已保存到: {output_file}")
    except Exception as e:
        print(f"\n⚠️  Excel保存失败: {e}")
        # 保存为CSV作为备选
        csv_file = os.path.join(results_dir, 'model_ensemble_analysis.csv')
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"💾 详细分析结果已保存为CSV: {csv_file}")

if __name__ == "__main__":
    main()
