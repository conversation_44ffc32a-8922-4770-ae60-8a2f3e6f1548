#!/usr/bin/env python3
"""
统一三个模型的预测结果格式，输出为JSON文件
将所有预测结果统一成完整的知识点路径格式
支持任意Top-k结果的自动检测和处理
"""

import json
import pandas as pd
from typing import List, Dict, Tuple, Set
from difflib import SequenceMatcher
import re

def clean_query_text(text: str) -> str:
    """清理查询文本，移除多余的空格和特殊字符"""
    if not text:
        return ""
    
    # 移除多余的空格和换行符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除一些可能的格式差异
    text = text.replace('$', '').replace('\\', '')
    
    return text

def extract_query_from_llama_prompt(prompt: str) -> str:
    """从LLaMA的prompt中提取查询内容"""
    if not prompt:
        return ""
    
    if '<|im_start|>user\n' in prompt and '<|im_end|>' in prompt:
        user_content = prompt.split('<|im_start|>user\n')[1].split('<|im_end|>')[0]
        
        # 查找指令结束的位置
        instruction_patterns = [
            '确保标签序列严格遵循从宏观到具体的顺序。\n',
            '确保标签序列严格遵循从宏观到具体的顺序。',
            '请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。\n',
            '请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。'
        ]
        
        for pattern in instruction_patterns:
            if pattern in user_content:
                query = user_content.split(pattern, 1)[-1].strip()
                if query:
                    return clean_query_text(query)
        
        # 如果没有找到指令模式，返回整个用户内容
        return clean_query_text(user_content)
    
    return ""

def convert_to_full_path(path_parts: List[str]) -> str:
    """将路径部分转换为完整的知识点路径"""
    if not path_parts:
        return ""
    
    # 过滤掉思考标记
    filtered_parts = []
    for part in path_parts:
        part = part.strip()
        if part and part not in ['<think>', '</think>']:
            filtered_parts.append(part)
    
    return ' -> '.join(filtered_parts) if filtered_parts else ""

def load_llama_factory_predictions(file_path: str) -> List[Dict]:
    """加载LLaMA-Factory模型的预测结果"""
    predictions = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for idx, line in enumerate(f):
            data = json.loads(line.strip())
            
            # 提取题目内容
            query = extract_query_from_llama_prompt(data['prompt'])
            
            # 提取预测路径 - 转换为完整路径格式
            predict_text = data['predict']
            if predict_text:
                # 移除<think>标记
                predict_text = predict_text.replace('<think>', '').replace('</think>', '')
                # 分割成路径部分
                predict_parts = [part.strip() for part in predict_text.split('\n') if part.strip()]
                predict_path = convert_to_full_path(predict_parts)
            else:
                predict_path = ""
            
            predictions.append({
                'index': idx,
                'query': query,
                'llama_prediction': predict_path
            })
    
    return predictions

def load_embedding_predictions(file_path: str) -> List[Dict]:
    """加载Embedding模型的预测结果，支持任意Top-k结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    predictions = []
    for idx, item in enumerate(data):
        # 提取Top-1预测标签路径 - 已经是完整路径格式
        pred_path = ' -> '.join(item['predicted_label'])

        # 提取所有Top-k结果
        topk_predictions = []
        if 'topk_results' in item and isinstance(item['topk_results'], list):
            for k, topk_item in enumerate(item['topk_results']):
                try:
                    if isinstance(topk_item, list) and len(topk_item) >= 2:
                        # 新格式：[相似度, {详细信息}]
                        similarity_score = topk_item[0]
                        detail_dict = topk_item[1]
                        pred_label = detail_dict.get('label', [])
                        sample_id = detail_dict.get('id', -1)
                    elif isinstance(topk_item, dict):
                        # 旧格式：{predicted_label: [...], similarity_score: 0.xx}
                        pred_label = topk_item.get('predicted_label', [])
                        similarity_score = topk_item.get('similarity_score', 0.0)
                        sample_id = topk_item.get('sample_id', -1)
                    else:
                        continue

                    # 确保predicted_label是列表
                    if isinstance(pred_label, list):
                        topk_path = ' -> '.join(pred_label)
                    elif isinstance(pred_label, str):
                        topk_path = pred_label
                    else:
                        continue

                    topk_predictions.append({
                        'prediction': topk_path
                    })
                except Exception as e:
                    continue

        predictions.append({
            'index': idx,
            'query': clean_query_text(item['query']),
            'embedding_prediction': [topk_item['prediction'] for topk_item in topk_predictions] if topk_predictions else [pred_path],
            'true_label': ' -> '.join(item.get('true_label', [])) if 'true_label' in item else ""
        })

    return predictions

def load_tacl_predictions(file_path: str) -> List[Dict]:
    """加载TACL项目的预测结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    predictions = []
    for idx, item in enumerate(data):
        # 提取预测标签路径（取rank=1的预测）- 已经是完整路径格式
        pred_path = ""
        if item['predictions']:
            pred_path = item['predictions'][0]['full_path']
        
        predictions.append({
            'index': idx,
            'query': clean_query_text(item['question']),
            'tacl_prediction': pred_path,
            'confidence': item['predictions'][0]['confidence'] if item['predictions'] else 0.0
        })
    
    return predictions

def detect_topk_value(embedding_preds: List[Dict]) -> int:
    """自动检测Top-k值"""
    max_k = 0
    for pred in embedding_preds[:10]:  # 只检查前10个样本
        if 'embedding_topk' in pred and pred['embedding_topk']:
            max_k = max(max_k, len(pred['embedding_topk']))
        if max_k > 0:  # 找到一个就够了
            break
    return max_k

def find_best_match(query: str, candidate_queries: List[str]) -> Tuple[int, float]:
    """找到最佳匹配的查询"""
    best_idx = -1
    best_similarity = 0.0

    for idx, candidate in enumerate(candidate_queries):
        similarity = SequenceMatcher(None, query, candidate).ratio()
        if similarity > best_similarity:
            best_similarity = similarity
            best_idx = idx

    return best_idx, best_similarity

def merge_and_unify_predictions(llama_preds: List[Dict], embedding_preds: List[Dict], tacl_preds: List[Dict]) -> List[Dict]:
    """合并并统一三个模型的预测结果"""
    unified_results = []
    used_embedding_indices = set()
    used_tacl_indices = set()
    
    print(f"🔄 开始统一 {len(llama_preds)} 个样本的预测结果...")
    
    for llama_pred in llama_preds:
        llama_query = llama_pred['query']
        llama_idx = llama_pred['index']
        
        matched_embedding_idx = -1
        matched_tacl_idx = -1
        
        # 策略1: 位置索引匹配（优先）
        if llama_idx < len(embedding_preds) and llama_idx not in used_embedding_indices:
            embedding_pred = embedding_preds[llama_idx]
            similarity = SequenceMatcher(None, llama_query, embedding_pred['query']).ratio()
            if similarity > 0.7:
                matched_embedding_idx = llama_idx
        
        if llama_idx < len(tacl_preds) and llama_idx not in used_tacl_indices:
            tacl_pred = tacl_preds[llama_idx]
            similarity = SequenceMatcher(None, llama_query, tacl_pred['query']).ratio()
            if similarity > 0.7:
                matched_tacl_idx = llama_idx
        
        # 策略2: 精确匹配
        if matched_embedding_idx == -1:
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices and embedding_pred['query'] == llama_query:
                    matched_embedding_idx = idx
                    break
        
        if matched_tacl_idx == -1:
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices and tacl_pred['query'] == llama_query:
                    matched_tacl_idx = idx
                    break
        
        # 策略3: 模糊匹配
        if matched_embedding_idx == -1:
            available_embedding_queries = []
            available_embedding_indices = []
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices:
                    available_embedding_queries.append(embedding_pred['query'])
                    available_embedding_indices.append(idx)
            
            if available_embedding_queries:
                best_idx, best_similarity = find_best_match(llama_query, available_embedding_queries)
                if best_similarity > 0.5:
                    matched_embedding_idx = available_embedding_indices[best_idx]
        
        if matched_tacl_idx == -1:
            available_tacl_queries = []
            available_tacl_indices = []
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices:
                    available_tacl_queries.append(tacl_pred['query'])
                    available_tacl_indices.append(idx)
            
            if available_tacl_queries:
                best_idx, best_similarity = find_best_match(llama_query, available_tacl_queries)
                if best_similarity > 0.5:
                    matched_tacl_idx = available_tacl_indices[best_idx]
        
        # 策略4: 强制匹配（确保100%匹配）
        if matched_embedding_idx == -1:
            for idx, embedding_pred in enumerate(embedding_preds):
                if idx not in used_embedding_indices:
                    matched_embedding_idx = idx
                    break
        
        if matched_tacl_idx == -1:
            for idx, tacl_pred in enumerate(tacl_preds):
                if idx not in used_tacl_indices:
                    matched_tacl_idx = idx
                    break
        
        # 如果找到了匹配，添加到结果中
        if matched_embedding_idx != -1 and matched_tacl_idx != -1:
            embedding_pred = embedding_preds[matched_embedding_idx]
            tacl_pred = tacl_preds[matched_tacl_idx]
            used_embedding_indices.add(matched_embedding_idx)
            used_tacl_indices.add(matched_tacl_idx)

            # 构建统一结果，只保留必要字段
            result = {
                'question': llama_query,
                'llama_prediction': llama_pred['llama_prediction'],
                'embedding_prediction': embedding_pred['embedding_prediction'],  # 这里已经是Top-k预测列表
                'tacl_prediction': tacl_pred['tacl_prediction'],
                'true_label': embedding_pred.get('true_label', '')
            }

            unified_results.append(result)
    
    print(f"✅ 统一完成！成功处理 {len(unified_results)} 个样本")
    return unified_results

def main():
    # 文件路径
    llama_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_num10/generated_predictions.jsonl"
    embedding_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math_similarity_system/results/embedding-4b-top10-result-num10/final_results.json"
    tacl_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/TACL-result/20250915_num_10_top3.json"
    
    print("🔄 加载三个模型的预测结果...")
    
    # 加载预测结果
    llama_preds = load_llama_factory_predictions(llama_file)
    embedding_preds = load_embedding_predictions(embedding_file)
    tacl_preds = load_tacl_predictions(tacl_file)
    
    print(f"✅ LLaMA-Factory预测数量: {len(llama_preds)}")
    print(f"✅ Embedding预测数量: {len(embedding_preds)}")
    print(f"✅ TACL预测数量: {len(tacl_preds)}")

    # 自动检测Top-k值
    detected_k = detect_topk_value(embedding_preds)
    print(f"🔍 自动检测到Top-{detected_k}结果")

    # 合并并统一预测结果
    unified_results = merge_and_unify_predictions(llama_preds, embedding_preds, tacl_preds)
    
    # 保存为JSON文件
    # 确保results目录存在
    import os
    results_dir = '../../results/unified_predictions'
    os.makedirs(results_dir, exist_ok=True)

    output_file = os.path.join(results_dir, "unified_model_predictions.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(unified_results, f, ensure_ascii=False, indent=2)

    print(f"\n💾 统一的预测结果已保存到: {output_file}")
    print(f"📊 总共包含 {len(unified_results)} 个样本")
    
    # 显示前2个样本作为示例，包含Top-k信息
    print(f"\n📋 前2个样本示例:")
    for i, sample in enumerate(unified_results[:2]):
        print(f"\n样本 {i+1}:")
        print(f"  题目: {sample['question'][:100]}...")
        print(f"  真实标签: {sample.get('true_label', 'N/A')}")
        print(f"  LLaMA预测: {sample['llama_prediction']}")
        print(f"  TACL预测: {sample['tacl_prediction']}")

        # 显示Embedding Top-k结果
        embedding_preds = sample['embedding_prediction']
        if isinstance(embedding_preds, list):
            print(f"  Embedding预测 (Top-{len(embedding_preds)}):")
            for i, pred in enumerate(embedding_preds[:5]):  # 只显示前5个
                print(f"    Top-{i+1}: {pred}")
            if len(embedding_preds) > 5:
                print(f"    ... 还有 {len(embedding_preds) - 5} 个预测结果")
        else:
            print(f"  Embedding预测: {embedding_preds}")

    # 统计Top-k信息
    if unified_results:
        first_sample = unified_results[0]
        embedding_preds = first_sample['embedding_prediction']
        if isinstance(embedding_preds, list):
            max_k = len(embedding_preds)
            print(f"\n📊 Top-k统计信息:")
            print(f"  检测到Top-{max_k}结果")
            print(f"  每个样本的embedding_prediction字段包含{max_k}个候选预测")
            print(f"  JSON结构简洁，所有Top-k信息集中在embedding_prediction数组中")

if __name__ == "__main__":
    main()
