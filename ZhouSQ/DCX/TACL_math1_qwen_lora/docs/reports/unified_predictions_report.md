# 统一模型预测结果报告

## 📊 生成概述

成功将三个模型的预测结果统一为标准的知识点路径格式，并输出为JSON文件。

## 🔍 数据统计

- **总样本数**: 243个数学题目
- **匹配成功率**: 100.00% (243/243)
- **输出文件**: `unified_model_predictions.json`
- **数据集**: num10 (eval_2025-09-15-17-22-14_num10)

## 📋 JSON文件结构

每个样本包含以下字段：
```json
{
  "question": "数学题目内容",
  "llama_prediction": "小学数学新知识树 -> ... -> 具体知识点",
  "embedding_prediction": "小学数学新知识树 -> ... -> 具体知识点",
  "tacl_prediction": "小学数学新知识树 -> ... -> 具体知识点",
  "tacl_confidence": 0.xxxx
}
```

## 🎯 格式统一特点

### ✅ 完整路径格式
- 所有预测结果都采用 `小学数学新知识树 -> 数与代数 -> ...` 的完整路径格式
- 平均路径深度: 7层
- 包含补充知识点编号（如 `补充知识点2515`）

### ✅ 数据清理
- 移除了LLaMA预测中的 `<think>` 和 `</think>` 标记
- 统一了文本格式，移除多余空格和特殊字符
- 保持了原始的知识点层级结构

### ✅ 匹配质量
- 使用多层次匹配算法确保100%样本覆盖
- 位置索引匹配为主要方法（99%+的样本）
- 保留了TACL模型的置信度信息

## 📈 预测结果示例

### 样本1: 分数乘法问题
**题目**: 专家建议每人每天糖摄入量最好控制在 1/40 kg以内...
- **LLaMA**: `小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算 -> 分数乘法 -> 分数与整数的乘法 -> 分数乘整数`
- **Embedding**: `小学数学新知识树 -> 数与代数 -> 数的运算 -> 运算定律与简便运算 -> 整数除法的性质 -> 运用除法的运算性质解决实际问题 -> 补充知识点1901`
- **TACL**: `小学数学新知识树 -> 数与代数 -> 数的认识 -> 分数的认识 -> 分数化小数 -> 分数化成小数 -> 补充知识点564`

### 样本2: 比例应用问题
**题目**: 大约从一万年前开始青藏高原平均每年上升约 7/100 米...
- **LLaMA**: `小学数学新知识树 -> 数与代数 -> 数的认识 -> 分数的认识 -> 分数的意义、读写及分类 -> 分数与除法的关系 -> 分数与除法关系的应用-`
- **Embedding**: `小学数学新知识树 -> 数与代数 -> 数的认识 -> 整数的认识 -> 整数的改写与近似数 -> 整数的改写 -> 整万数的改写`
- **TACL**: `小学数学新知识树 -> 数与代数 -> 比和比例 -> 比例 -> 图上距离、实际距离的换算 -> 由比例尺求实际距离 -> 补充知识点2515`

### 样本3: 几何折叠问题
**题目**: 毛毛的学习桌有点不平稳他就用一张厚 3/16 mm的牛皮纸对折三次后...
- **LLaMA**: `小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算 -> 分数乘法 -> 求一个数的几分之几的问题 -> 利用整体的几分之几解决问题`
- **Embedding**: `小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算 -> 分数乘法 -> 求一个数的几分之几的问题 -> 利用整体的几分之几解决问题`
- **TACL**: `小学数学新知识树 -> 图形与几何 -> 图形的拼组 -> 图形的折叠问题 -> 绳长对折问题 -> 补充知识点3988 -> 补充知识点3989`

## 💡 关键特点

### 🔍 模型差异性
- **LLaMA**: 更倾向于数的运算分类，特别是分数运算
- **Embedding**: 在运算定律和实际应用方面有独特见解
- **TACL**: 能够识别几何问题，分类更加多样化

### 📊 置信度分布
- TACL置信度范围: 0.34 - 0.50
- 平均置信度: ~0.40
- 置信度信息可用于质量评估和结果筛选

### 🎯 应用价值
- **教育研究**: 可用于分析不同模型的知识点分类偏好
- **模型评估**: 便于比较三个模型的预测差异
- **集成学习**: 为模型集成提供标准化的输入数据
- **知识图谱**: 可用于构建数学知识点的关联分析

## 📁 文件信息

- **文件名**: `unified_model_predictions.json`
- **文件大小**: ~1.7MB
- **编码格式**: UTF-8
- **JSON格式**: 美化输出，便于阅读
- **数据完整性**: 100%样本覆盖，无缺失值

## 🎉 总结

成功创建了包含243个数学题目及其三个模型预测结果的统一JSON文件。所有预测结果都采用了标准的知识点路径格式，便于后续的分析、比较和应用。这个统一的数据集为深入研究不同模型的知识点分类能力提供了宝贵的资源。
