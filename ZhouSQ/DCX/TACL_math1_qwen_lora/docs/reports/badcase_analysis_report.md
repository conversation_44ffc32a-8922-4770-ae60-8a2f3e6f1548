# 三模型Badcase分析报告

## 📊 分析概述

### 🎯 分析目标
对LLaMA、Embedding和TACL三个模型在数学知识点分类任务上的失败案例进行深度分析，识别模型弱点和改进方向。

### 📁 数据来源
- **LLaMA预测**: `/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_num10/generated_predictions.jsonl`
- **Embedding预测**: `/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math_similarity_system/results/embedding-4b-top3-result-num10/final_results.json`
- **TACL预测**: `/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/TACL-result/20250915_num_10_top3.json`
- **数据集**: `/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math_data/num_beyond_10.json`

## 🔍 核心发现

### 📈 基础统计
- **总样本数**: 243个数学题目
- **数据集标签种类**: 76种不同的知识点标签
- **样本匹配率**: 100% (243/243)

### 🎯 模型失败情况
| 失败类型 | 数量 | 占比 | 说明 |
|---------|------|------|------|
| **三个模型都失败** | 91个 | 37.45% | 最难预测的题目 |
| **只有LLaMA正确** | 15个 | 6.17% | LLaMA独有优势 |
| **只有Embedding正确** | 13个 | 5.35% | Embedding独有优势 |
| **只有TACL正确** | 32个 | 13.17% | TACL独有优势 |

## 📊 详细Excel分析结果

### 🗂️ Excel文件结构 (`badcase_analysis_detailed.xlsx`)
**总计**: 243行 × 31列

#### **基础信息列 (5列)**
1. `index` - 样本索引
2. `question` - 题目内容
3. `true_label` - 真实标签
4. `dataset_label_count` - 该标签在数据集中的频次
5. `failed_models` - 失败的模型列表

#### **LLaMA预测列 (2列)**
6. `llama_prediction` - LLaMA预测结果
7. `llama_correct` - LLaMA是否正确

#### **Embedding Top-3预测列 (9列)**
8-10. `embedding_top1/2/3` - Top-3预测结果
11-13. `embedding_top1/2/3_similarity` - 相似度分数
14-16. `embedding_top1/2/3_correct` - 是否正确

#### **TACL Top-3预测列 (9列)**
17-19. `tacl_top1/2/3` - Top-3预测结果
20-22. `tacl_top1/2/3_confidence` - 置信度分数
23-25. `tacl_top1/2/3_correct` - 是否正确

#### **分析标记列 (6列)**
26. `all_models_failed` - 三个模型都失败
27. `only_llama_correct` - 只有LLaMA正确
28. `only_embedding_correct` - 只有Embedding正确
29. `only_tacl_correct` - 只有TACL正确
30. `embedding_any_correct` - Embedding任一预测正确
31. `tacl_any_correct` - TACL任一预测正确

## 🔥 最难预测的标签类型

### Top-10 最高失败率标签
1. **位置与方向** - 100%失败率 (1/1样本)
2. **立体图形拼搭** - 100%失败率 (1/1样本)
3. **行程问题** - 100%失败率 (1/1样本)
4. **分数加减法** - 100%失败率 (2/2样本)
5. **100以内加减法** - 100%失败率 (1/1样本)
6. **20以内减法** - 100%失败率 (1/1样本)
7. **两三位数加减法** - 100%失败率 (1/1样本)
8. **表内乘法** - 100%失败率 (1/1样本)
9. **6的乘法口诀** - 100%失败率 (3/3样本)
10. **整数平均分** - 100%失败率 (1/1样本)

### 🔢 高频标签表现分析
| 标签 | 数据集频次 | 测试样本 | 全失败率 |
|------|------------|----------|----------|
| 分数乘整数 | 14 | 14 | 42.9% |
| 商不变规律 | 14 | 14 | 7.1% |
| 整十整百数乘法 | 10 | 10 | 60.0% |
| 整数四则混合运算 | 10 | 10 | 70.0% |
| 两位数除法 | 8 | 8 | 37.5% |

## 🎯 模型特异性分析

### LLaMA独有优势
- **擅长领域**: 分数乘法、整数四则混合运算
- **特点**: 在复杂运算规律方面表现较好
- **独有正确预测**: 15个样本

### Embedding独有优势
- **擅长领域**: 整数四则混合运算、整数估算
- **特点**: 在相似题型识别方面有优势
- **独有正确预测**: 13个样本

### TACL独有优势
- **擅长领域**: 两位数除法、商的变化规律、质量单位换算
- **特点**: 在基础运算和单位换算方面表现突出
- **独有正确预测**: 32个样本（最多）

## 💡 改进建议

### 1. **针对高失败率标签**
- **几何与空间**: 位置方向、立体图形需要加强训练
- **基础运算**: 低年级的加减乘除法需要重点关注
- **应用题**: 行程问题等复杂应用题需要专项训练

### 2. **模型集成策略**
- **TACL优势明显**: 在基础运算方面可以给TACL更高权重
- **LLaMA适合复杂推理**: 在多步骤运算中优先考虑LLaMA
- **Embedding补充作用**: 在相似题型识别中发挥作用

### 3. **数据增强方向**
- **低频标签**: 对出现频次少的标签进行数据增强
- **困难样本**: 针对三模型都失败的91个样本进行专项分析
- **边界案例**: 关注模型预测分歧较大的样本

## 📁 输出文件

1. **`badcase_analysis_detailed.xlsx`** - 完整的243×31列分析数据
2. **`badcase_analysis.py`** - 分析脚本源码
3. **`badcase_analysis_report.md`** - 本分析报告

## ✅ 结论

通过详细的badcase分析，我们发现：

1. **37.45%的样本三个模型都无法正确预测**，这些是最需要关注的困难样本
2. **TACL模型在基础运算方面表现最好**，独有正确预测最多
3. **几何空间类题目是所有模型的共同弱点**
4. **低频标签的预测准确率普遍较低**，需要数据增强

这份分析为模型改进和集成策略优化提供了详实的数据支撑。
