'''
NCCL_SOCKET_IFNAME="eno1" NCCL_IB_DISABLE="1" NCCL_P2P_DISABLE="1" NCCL_DEBUG="WARN" PYTORCH_CUDA_ALLOC_CONF="expandable_segments:True" llamafactory-cli train --stage sft --do_train True --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B --preprocessing_num_workers 16 --finetuning_type lora --template qwen3 --flash_attn auto --dataset_dir data --dataset math1_enhanced --cutoff_len 2048 --learning_rate 5e-05 --num_train_epochs 3.0 --max_samples 100000 --per_device_train_batch_size 4 --gradient_accumulation_steps 8 --lr_scheduler_type cosine --max_grad_norm 1.0 --logging_steps 10 --save_steps 600 --warmup_steps 0 --packing False --enable_thinking True --report_to none --output_dir saves/Qwen3-8B-Thinking/lora/train_2025-09-19-16-54-11 --bf16 True --plot_loss True --trust_remote_code True --ddp_timeout 180000000 --include_num_input_tokens_seen True --optim adamw_torch --lora_rank 8 --lora_alpha 256 --lora_dropout 0 --lora_target all --deepspeed cache/ds_z3_config.json
'''

'''
conda activate htc && export NCCL_SOCKET_IFNAME=eno1 && export NCCL_IB_DISABLE=1 && export NCCL_P2P_DISABLE=1 && export NCCL_DEBUG=WARN && export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True && llamafactory-cli train     --stage sft     --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B     --preprocessing_num_workers 16     --finetuning_type lora     --quantization_method bnb     --template qwen3     --flash_attn auto     --dataset_dir data     --eval_dataset math1_test     --cutoff_len 2048     --max_samples 150000     --per_device_eval_batch_size 4     --predict_with_generate True     --report_to none     --max_new_tokens 512     --top_p 0.01     --temperature 0.01     --output_dir saves/Qwen3-8B-Thinking/lora/eval_2025-09-18-16-54-11     --trust_remote_code True     --ddp_timeout 180000000     --do_predict True     --adapter_name_or_path saves/Qwen3-8B-Thinking/lora/train_2025-09-18-16-54-11
'''
