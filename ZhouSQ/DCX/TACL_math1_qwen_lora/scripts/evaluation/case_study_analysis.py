#!/usr/bin/env python3
"""
Case Study深入分析脚本
分析具体的错误案例，找出模型预测错误的原因
"""

import json
import pandas as pd
from collections import defaultdict
import re

def extract_knowledge_path(text: str) -> str:
    """从预测文本中提取知识点路径"""
    if not text:
        return ''
    
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    text = text.strip()
    
    if not text:
        return ''
    
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    knowledge_points = []
    
    for line in lines:
        if any(marker in line for marker in ['```', '===', '---', '<|', '|>', 'assistant', 'user']):
            continue
        knowledge_points.append(line)
    
    return ' -> '.join(knowledge_points) if knowledge_points else ''

def analyze_specific_category(predictions_file: str, target_category: str, dataset_name: str, max_examples: int = 10):
    """分析特定类别的预测错误案例"""
    print(f"\n=== Case Study: {target_category[:60]}... ===")
    print(f"数据集: {dataset_name}")
    
    correct_predictions = []
    wrong_predictions = []
    
    try:
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    predicted_path = extract_knowledge_path(data.get('predict', ''))
                    true_path = extract_knowledge_path(data.get('label', ''))
                    
                    # 检查是否涉及目标类别
                    if predicted_path == target_category or true_path == target_category:
                        case = {
                            'line_num': line_num,
                            'question': data.get('instruction', ''),
                            'predicted': predicted_path,
                            'true': true_path,
                            'is_correct': predicted_path == true_path
                        }
                        
                        if case['is_correct']:
                            correct_predictions.append(case)
                        else:
                            wrong_predictions.append(case)
                            
                except:
                    continue
                    
    except Exception as e:
        print(f"读取文件出错: {e}")
        return
    
    total_cases = len(correct_predictions) + len(wrong_predictions)
    accuracy = len(correct_predictions) / total_cases if total_cases > 0 else 0
    
    print(f"总相关案例数: {total_cases}")
    print(f"正确预测: {len(correct_predictions)}")
    print(f"错误预测: {len(wrong_predictions)}")
    print(f"准确率: {accuracy:.3f}")
    
    if len(wrong_predictions) > 0:
        print(f"\n--- 错误案例分析 (显示前{min(max_examples, len(wrong_predictions))}个) ---")
        
        # 分析错误类型
        error_types = defaultdict(list)
        for case in wrong_predictions:
            if case['predicted'] == target_category:
                # 模型错误预测为目标类别
                error_types['False Positive'].append(case)
            elif case['true'] == target_category:
                # 模型未能预测出目标类别
                error_types['False Negative'].append(case)
        
        for error_type, cases in error_types.items():
            if len(cases) > 0:
                print(f"\n{error_type} 案例 ({len(cases)}个):")
                for i, case in enumerate(cases[:max_examples], 1):
                    print(f"\n案例 {i}:")
                    print(f"题目: {case['question'][:100]}...")
                    print(f"真实标签: {case['true'][:80]}...")
                    print(f"预测标签: {case['predicted'][:80]}...")
    
    if len(correct_predictions) > 0:
        print(f"\n--- 正确案例样本 (显示前{min(3, len(correct_predictions))}个) ---")
        for i, case in enumerate(correct_predictions[:3], 1):
            print(f"\n正确案例 {i}:")
            print(f"题目: {case['question'][:100]}...")
            print(f"标签: {case['true'][:80]}...")

def main():
    """主函数"""
    print("=== Case Study 深入分析 ===")
    
    # 基于之前的分析结果，选择几个有代表性的类别进行case study
    case_study_targets = [
        # 6000题中表现最差的大样本类目
        "小学数学新知识树 -> 数与代数 -> 数的运算 -> 小数的四则运算 -> 小数的加法和减法 -> 利用小数的加、减法混合运算解决实际问题 -> 小数加减法应用题",
        "小学数学新知识树 -> 数与代数 -> 数的运算 -> 运算定律与简便运算 -> 小数加、减法简便运算 -> 与小数减法相关的简便运算 -> 小数加减法简算综合",
        "小学数学新知识树 -> 数与代数 -> 数的运算 -> 整数的四则运算 -> 表内除法 -> 乘除法解决问题(表内) -> 除法应用题(7、8、9)",
        
        # 6000题中表现最好的大样本类目（作为对比）
        "小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算 -> 分数的加、减法 -> 分数的加、减法混合运算的应用 -> 解决问题(一半的一半应用)",
        "小学数学新知识树 -> 数与代数 -> 应用题 -> 常见的数学问题 -> 鸽巢问题 -> 最不利原则 -> 最不利"
    ]
    
    datasets = [
        {
            'name': '6000题数据集',
            'predictions': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-09-13-54_6047/generated_predictions.jsonl'
        },
        {
            'name': '8月数据集(num10)',
            'predictions': '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-17-22-14_num10/generated_predictions.jsonl'
        }
    ]
    
    for target in case_study_targets:
        print("\n" + "="*80)
        for dataset in datasets:
            if os.path.exists(dataset['predictions']):
                analyze_specific_category(
                    dataset['predictions'], 
                    target, 
                    dataset['name'],
                    max_examples=5
                )
            else:
                print(f"\n❌ {dataset['name']} 预测文件不存在")
        print("="*80)

if __name__ == "__main__":
    import os
    main()
