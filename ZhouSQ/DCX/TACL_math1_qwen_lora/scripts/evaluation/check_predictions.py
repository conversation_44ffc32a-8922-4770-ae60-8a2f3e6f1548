#!/usr/bin/env python3
"""
检查 generated_predictions.jsonl 文件中每行 predict 和 label 数量是否一致的脚本
"""

import json
import sys
from pathlib import Path

def parse_label_string(label_str):
    """
    解析标签字符串，提取标签列表
    
    Args:
        label_str (str): 标签字符串
        
    Returns:
        list: 标签列表
    """
    if not label_str:
        return []
    
    # 去除首尾空白字符
    label_str = label_str.strip()
    
    # 如果是列表形式的字符串，直接解析
    if label_str.startswith('[') and label_str.endswith(']'):
        try:
            result = json.loads(label_str)
            if isinstance(result, list):
                return result
        except:
            pass
    
    # 尝试按行分割处理
    lines = label_str.split('\n')
    labels = []
    for line in lines:
        line = line.strip()
        # 过滤掉特殊标记和空行
        if line and not line.startswith('``') and not line.startswith('[') and not line.endswith(']'):
            # 如果行内有多个标签，按逗号分割
            if ',' in line:
                sub_labels = [x.strip() for x in line.split(',') if x.strip()]
                labels.extend(sub_labels)
            else:
                labels.append(line)
    
    return labels

def parse_knowledge_path(text):
    """
    解析知识路径文本，提取标签列表

    Args:
        text (str): 包含知识路径的文本

    Returns:
        list: 标签列表
    """
    if not text:
        return []

    # 移除<think>标签内容
    import re
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    text = text.strip()

    if not text:
        return []

    # 按行分割并过滤空行
    lines = [line.strip() for line in text.split('\n') if line.strip()]

    # 过滤掉非标签行（如包含特殊字符的行）
    labels = []
    for line in lines:
        # 跳过包含特殊标记的行
        if any(marker in line for marker in ['```', '===', '---', '<|', '|>', 'assistant', 'user']):
            continue
        labels.append(line)

    return labels

def check_predictions_labels_consistency(file_path):
    """
    检查预测文件中每行的predict和label数量是否一致

    Args:
        file_path (str): JSONL文件路径
    """
    file_path = Path(file_path)

    if not file_path.exists():
        print(f"错误: 文件 {file_path} 不存在")
        return

    print(f"正在检查文件: {file_path}")
    print("=" * 80)

    inconsistent_count = 0
    total_count = 0
    predict_lengths = {}
    label_lengths = {}

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    predict_raw = data.get('predict', '')
                    label_raw = data.get('label', '')

                    # 解析predict和label
                    predict_labels = parse_knowledge_path(predict_raw)
                    label_labels = parse_knowledge_path(label_raw)

                    predict_count = len(predict_labels)
                    label_count = len(label_labels)

                    # 统计长度分布
                    predict_lengths[predict_count] = predict_lengths.get(predict_count, 0) + 1
                    label_lengths[label_count] = label_lengths.get(label_count, 0) + 1

                    is_consistent = predict_count == label_count

                    if not is_consistent:
                        inconsistent_count += 1
                        print(f"第 {line_num} 行不一致:")
                        print(f"  predict 数量: {predict_count}")
                        print(f"  predict 内容: {predict_labels}")
                        print(f"  label 数量: {label_count}")
                        print(f"  label 内容: {label_labels}")
                        print()
                    elif line_num <= 3:  # 显示前3行的详细信息
                        print(f"第 {line_num} 行 (一致):")
                        print(f"  predict 数量: {predict_count}")
                        print(f"  label 数量: {label_count}")
                        print(f"  标签路径: {predict_labels}")
                        print()

                    total_count += 1

                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行 JSON 解析错误: {e}")
                    print(f"原始内容: {line[:100]}...")
                    print()
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    print()

    except Exception as e:
        print(f"读取文件时出错: {e}")
        return

    print("=" * 80)
    print("📊 统计分析:")
    print(f"总行数: {total_count}")
    print(f"一致行数: {total_count - inconsistent_count}")
    print(f"不一致行数: {inconsistent_count}")
    print(f"一致性比例: {((total_count - inconsistent_count) / total_count * 100):.2f}%" if total_count > 0 else "N/A")

    print(f"\n📈 Predict标签数量分布:")
    for length in sorted(predict_lengths.keys()):
        print(f"  {length}个标签: {predict_lengths[length]}行 ({predict_lengths[length]/total_count*100:.1f}%)")

    print(f"\n📈 Label标签数量分布:")
    for length in sorted(label_lengths.keys()):
        print(f"  {length}个标签: {label_lengths[length]}行 ({label_lengths[length]/total_count*100:.1f}%)")

    if inconsistent_count == 0:
        print(f"\n✅ 所有行的predict和label标签数量都一致！")
    else:
        print(f"\n❌ 发现 {inconsistent_count} 行标签数量不一致，需要检查数据质量。")

if __name__ == "__main__":
    # 默认文件路径
    default_file_path = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-16-15-22-14_6047_buchong/generated_predictions.jsonl"
    
    # 如果提供了命令行参数，则使用参数中的路径
    file_path = sys.argv[1] if len(sys.argv) > 1 else default_file_path
    
    check_predictions_labels_consistency(file_path)