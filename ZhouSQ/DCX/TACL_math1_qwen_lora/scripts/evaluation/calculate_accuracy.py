import json
import re

def normalize_content(content):
    """
    标准化内容，移除"补充知识点"行，但保留其他所有内容
    """
    lines = content.split('\n')
    # 过滤掉包含"补充知识点"的行
    filtered_lines = [line for line in lines if '补充知识点' not in line]
    # 重新组合并去除末尾换行符
    return '\n'.join(filtered_lines).rstrip('\n')

def calculate_accuracy(file_path, output_file=None):
    """
    计算预测准确率，只有当predict和label除了"补充知识点"行外完全一致才算准确
    如果指定了output_file，则将不准确的预测输出到该文件
    """
    total_count = 0
    correct_count = 0
    incorrect_predictions = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():  # 确保不是空行
                data = json.loads(line)
                predict = data.get('predict', '')
                label = data.get('label', '')
                prompt = data.get('prompt', '')
                
                # 标准化内容，移除"补充知识点"行
                predict_normalized = normalize_content(predict)
                label_normalized = normalize_content(label)
                
                total_count += 1
                if predict_normalized == label_normalized:
                    correct_count += 1
                else:
                    # 如果不准确且指定了输出文件，保存不准确的预测
                    if output_file is not None:
                        incorrect_predictions.append({
                            "id": total_count,
                            "prompt": prompt,
                            "predict": predict,
                            "label": label,
                            "predict_normalized": predict_normalized,
                            "label_normalized": label_normalized
                        })
    
    accuracy = correct_count / total_count if total_count > 0 else 0
    
    # 如果指定了输出文件，将不准确的预测写入文件
    if output_file is not None and incorrect_predictions:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(incorrect_predictions, f, ensure_ascii=False, indent=2)
        print(f"不准确的预测已保存到: {output_file}")
    
    return accuracy, correct_count, total_count

if __name__ == "__main__":
    file_path = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-16-15-22-14/generated_predictions.jsonl"
    output_file = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/incorrect_predictions-8b-level7.json"
    
    accuracy, correct, total = calculate_accuracy(file_path, output_file)
    
    print(f"总样本数: {total}")
    print(f"正确预测数: {correct}")
    print(f"准确率: {accuracy:.4f} ({correct}/{total})")
    print(f"准确率百分比: {accuracy*100:.2f}%")