#!/usr/bin/env python3
"""
调试8月数据集(num10)的样本数计算
"""

import json
from collections import defaultdict
import re

def extract_knowledge_path(text: str) -> str:
    if not text:
        return ''
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    text = text.strip()
    if not text:
        return ''
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    knowledge_points = []
    for line in lines:
        if any(marker in line for marker in ['```', '===', '---', '<|', '|>', 'assistant', 'user']):
            continue
        knowledge_points.append(line)
    return ' -> '.join(knowledge_points) if knowledge_points else ''

def main():
    print('=== 8月数据集(num10)详细分析 ===')
    
    # 1. 分析测试集中的真实标签分布
    test_file = '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/data/math1_test_num10.json'
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    print(f'测试集总样本数: {len(test_data)}')
    
    true_label_counts = defaultdict(int)
    for item in test_data:
        if 'output' in item:
            output = item['output']
            lines = [line.strip() for line in output.split('\n') if line.strip()]
            if lines:
                true_path = ' -> '.join(lines)
                true_label_counts[true_path] += 1
    
    print(f'测试集中唯一知识点数: {len(true_label_counts)}')
    
    # 统计不同样本数阈值的知识点数量
    thresholds = [1, 3, 5, 10, 15, 20]
    for threshold in thresholds:
        count = len([kp for kp, samples in true_label_counts.items() if samples >= threshold])
        print(f'测试集中样本数≥{threshold}的知识点数: {count}')
    
    print('\n测试集中所有知识点的样本数分布:')
    sorted_true_labels = sorted(true_label_counts.items(), key=lambda x: x[1], reverse=True)
    for i, (kp, count) in enumerate(sorted_true_labels, 1):
        marker = "✓" if count >= 5 else " "
        print(f'{marker} {i:2d}. {count}个样本: {kp[:70]}...' if len(kp) > 70 else f'{marker} {i:2d}. {count}个样本: {kp}')
    
    print('\n' + '='*80)
    
    # 2. 分析预测结果中出现的知识点
    predictions_file = '/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/eval_2025-09-15-17-22-14_num10/generated_predictions.jsonl'
    
    knowledge_point_stats = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})
    
    # 第一遍扫描：计算TP和FP
    with open(predictions_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                predicted_path = extract_knowledge_path(data.get('predict', ''))
                true_path = extract_knowledge_path(data.get('label', ''))
                
                if predicted_path:
                    if predicted_path == true_path:
                        knowledge_point_stats[predicted_path]['tp'] += 1
                    else:
                        knowledge_point_stats[predicted_path]['fp'] += 1
                        
            except:
                continue
    
    print(f'预测中出现的知识点数: {len(knowledge_point_stats)}')
    
    # 筛选出在测试集中样本数≥5且在预测中出现的知识点
    qualified_kps_5 = []
    qualified_kps_10 = []
    
    for kp in knowledge_point_stats.keys():
        test_samples = true_label_counts.get(kp, 0)
        if test_samples >= 5:
            qualified_kps_5.append((kp, test_samples))
        if test_samples >= 10:
            qualified_kps_10.append((kp, test_samples))
    
    print(f'预测中出现且测试集样本数≥5的知识点数: {len(qualified_kps_5)}')
    print(f'预测中出现且测试集样本数≥10的知识点数: {len(qualified_kps_10)}')
    
    print('\n符合条件(≥5)的知识点详情:')
    qualified_kps_5.sort(key=lambda x: x[1], reverse=True)
    for i, (kp, count) in enumerate(qualified_kps_5, 1):
        tp = knowledge_point_stats[kp]['tp']
        fp = knowledge_point_stats[kp]['fp']
        print(f'{i:2d}. {count}个样本 (TP:{tp}, FP:{fp}): {kp[:60]}...' if len(kp) > 60 else f'{i:2d}. {count}个样本 (TP:{tp}, FP:{fp}): {kp}')
    
    if qualified_kps_10:
        print('\n符合条件(≥10)的知识点详情:')
        qualified_kps_10.sort(key=lambda x: x[1], reverse=True)
        for i, (kp, count) in enumerate(qualified_kps_10, 1):
            tp = knowledge_point_stats[kp]['tp']
            fp = knowledge_point_stats[kp]['fp']
            print(f'{i:2d}. {count}个样本 (TP:{tp}, FP:{fp}): {kp[:60]}...' if len(kp) > 60 else f'{i:2d}. {count}个样本 (TP:{tp}, FP:{fp}): {kp}')
    
    print('\n' + '='*80)
    print('关键理解:')
    print('我们的评估脚本只统计"预测中出现的知识点"，而不是测试集中的所有知识点')
    print('这是因为我们采用的是"只统计预测标签"的策略')
    print('所以即使测试集中有更多样本数≥5的知识点，但如果模型没有预测到它们，就不会被计入评估')
    
    print(f'\n总结对比:')
    print(f'- 测试集中样本数≥5的知识点总数: {len([kp for kp, count in true_label_counts.items() if count >= 5])}')
    print(f'- 预测中出现且样本数≥5的知识点数: {len(qualified_kps_5)}')
    print(f'- 测试集中样本数≥10的知识点总数: {len([kp for kp, count in true_label_counts.items() if count >= 10])}')
    print(f'- 预测中出现且样本数≥10的知识点数: {len(qualified_kps_10)}')

if __name__ == "__main__":
    main()
