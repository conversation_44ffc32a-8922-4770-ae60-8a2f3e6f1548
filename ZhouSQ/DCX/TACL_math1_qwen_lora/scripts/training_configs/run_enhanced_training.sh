#!/bin/bash

# 增强训练脚本 - 基于badcase分析的改进训练
# 使用方法: bash scripts/training_configs/run_enhanced_training.sh

set -e

echo "🚀 开始增强训练流程..."

# 1. 创建增强数据集
echo "📊 步骤1: 创建增强数据集"
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora
python scripts/data_enhancement/create_enhanced_dataset.py

# 2. 验证数据集
echo "📊 步骤2: 验证增强数据集"
python -c "
import json
with open('LLaMA-Factory/data/math1_enhanced.json', 'r') as f:
    data = json.load(f)
print(f'✅ 增强数据集加载成功，共 {len(data)} 个样本')

# 检查数据格式
sample = data[0]
required_keys = ['instruction', 'input', 'output']
for key in required_keys:
    if key not in sample:
        raise ValueError(f'缺少必需字段: {key}')
print('✅ 数据格式验证通过')
"

# 3. 开始训练
echo "🔥 步骤3: 开始增强训练"
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory

# 使用LLaMA-Factory的训练命令
llamafactory-cli train \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B \
    --stage sft \
    --do_train \
    --finetuning_type lora \
    --template qwen3 \
    --dataset math1_enhanced \
    --cutoff_len 2048 \
    --learning_rate 3e-05 \
    --num_train_epochs 5.0 \
    --max_samples 150000 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 8 \
    --lr_scheduler_type cosine \
    --warmup_steps 100 \
    --bf16 \
    --logging_steps 5 \
    --save_steps 200 \
    --eval_steps 200 \
    --eval_strategy steps \
    --do_eval \
    --eval_dataset math1_test \
    --load_best_model_at_end \
    --metric_for_best_model eval_loss \
    --lora_target all \
    --lora_rank 16 \
    --lora_alpha 512 \
    --lora_dropout 0.1 \
    --enable_thinking \
    --deepspeed cache/ds_z3_config.json \
    --output_dir saves/Qwen3-8B-Thinking/lora/enhanced_train_$(date +%Y-%m-%d-%H-%M-%S) \
    --overwrite_output_dir \
    --plot_loss

echo "✅ 增强训练完成！"

# 4. 自动评估
echo "📊 步骤4: 自动评估新模型"
LATEST_MODEL=$(ls -td saves/Qwen3-8B-Thinking/lora/enhanced_train_* | head -1)
echo "最新模型路径: $LATEST_MODEL"

# 在测试集上评估
llamafactory-cli train \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B \
    --adapter_name_or_path $LATEST_MODEL \
    --template qwen3 \
    --stage sft \
    --do_predict \
    --dataset math1_test_num10 \
    --cutoff_len 2048 \
    --per_device_eval_batch_size 4 \
    --predict_with_generate \
    --bf16 \
    --output_dir $LATEST_MODEL/eval_enhanced \
    --overwrite_output_dir

echo "🎉 增强训练和评估流程完成！"
echo "📁 模型保存位置: $LATEST_MODEL"
echo "📁 评估结果位置: $LATEST_MODEL/eval_enhanced"
