# LLaMA模型改进执行计划

## 🎯 基于Badcase分析的具体改进方案

### 📊 问题诊断总结
- **失败率**: 61.3% (149/243)
- **主要问题**: 细粒度分类错误 (65.8%)、运算复杂度误判 (22.1%)
- **关键发现**: 数学竞赛题识别不足、运算vs认识概念混淆

### 🚀 立即可执行的改进步骤

#### **步骤1: 数据增强 (预计耗时: 30分钟)**
```bash
# 1. 创建增强数据集
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora
python scripts/data_enhancement/create_enhanced_dataset.py

# 2. 验证数据集质量
python -c "
import json
with open('LLaMA-Factory/data/math1_enhanced.json', 'r') as f:
    data = json.load(f)
print(f'增强数据集: {len(data)} 个样本')
"
```

#### **步骤2: 优化训练配置 (预计耗时: 5分钟)**
```bash
# 使用优化后的训练参数
cp scripts/training_configs/enhanced_training_config.yaml LLaMA-Factory/config/
```

#### **步骤3: 执行增强训练 (预计耗时: 4-6小时)**
```bash
# 方法1: 使用自动化脚本
chmod +x scripts/training_configs/run_enhanced_training.sh
bash scripts/training_configs/run_enhanced_training.sh

# 方法2: 手动执行 (如果需要更多控制)
cd LLaMA-Factory
llamafactory-cli train \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B \
    --stage sft \
    --do_train \
    --finetuning_type lora \
    --template qwen3 \
    --dataset math1_enhanced \
    --cutoff_len 2048 \
    --learning_rate 3e-05 \
    --num_train_epochs 5.0 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 8 \
    --lora_rank 16 \
    --lora_alpha 512 \
    --lora_dropout 0.1 \
    --warmup_steps 100 \
    --save_steps 200 \
    --eval_steps 200 \
    --do_eval \
    --eval_dataset math1_test \
    --load_best_model_at_end \
    --enable_thinking \
    --deepspeed cache/ds_z3_config.json \
    --output_dir saves/Qwen3-8B-Thinking/lora/enhanced_train_$(date +%Y-%m-%d-%H-%M-%S)
```

#### **步骤4: 评估改进效果 (预计耗时: 30分钟)**
```bash
# 1. 在测试集上评估新模型
LATEST_MODEL=$(ls -td LLaMA-Factory/saves/Qwen3-8B-Thinking/lora/enhanced_train_* | head -1)

cd LLaMA-Factory
llamafactory-cli train \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B \
    --adapter_name_or_path $LATEST_MODEL \
    --template qwen3 \
    --stage sft \
    --do_predict \
    --dataset math1_test_num10 \
    --cutoff_len 2048 \
    --per_device_eval_batch_size 4 \
    --predict_with_generate \
    --output_dir $LATEST_MODEL/eval_enhanced

# 2. 运行新的badcase分析
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora
python analysis_tools/badcase_analysis/badcase_analysis.py \
    --llama_file $LATEST_MODEL/eval_enhanced/generated_predictions.jsonl
```

### 📈 预期改进效果

#### **量化目标**
- **整体准确率**: 38.68% → 50%+ (目标提升30%)
- **数学竞赛题**: 当前几乎0% → 20%+ 
- **细粒度分类**: 减少层级5分歧从34.9%到20%以下
- **跨类别错误**: 从7.4%降至3%以下

#### **改进机制**
1. **数据增强**: 针对高错误率标签增加2-3倍样本
2. **对比学习**: 添加易混淆概念的对比样本
3. **参数优化**: 更大的LoRA rank和alpha提升表达能力
4. **训练策略**: 更多epoch + warmup + dropout防过拟合

### 🔧 进阶优化 (可选)

#### **A. 多阶段训练**
```bash
# 阶段1: 基础分类训练 (3 epochs)
# 阶段2: 困难样本训练 (2 epochs) 
# 阶段3: 对比学习训练 (1 epoch)
```

#### **B. 课程学习**
```bash
# 从简单样本开始，逐步增加难度
# 先训练大类别分类，再训练细粒度分类
```

#### **C. 集成学习**
```bash
# 训练多个LoRA适配器，最后集成预测
```

### 📊 监控指标

#### **训练过程监控**
- `training_loss`: 应该稳定下降
- `eval_loss`: 不应该持续上升 (过拟合信号)
- `learning_rate`: cosine调度应该平滑

#### **效果评估指标**
- 整体准确率提升
- 各层级分歧减少比例
- 特定错误类型改善情况

### ⚠️ 注意事项

1. **GPU内存**: 增加的LoRA参数可能需要更多显存
2. **训练时间**: 5个epoch + 增强数据会显著增加训练时间
3. **过拟合风险**: 密切监控eval_loss，必要时早停
4. **数据质量**: 确保增强数据的标签正确性

### 🎯 成功标准

**短期目标 (1周内)**:
- [ ] 完成数据增强和重新训练
- [ ] 整体准确率提升至45%以上
- [ ] 数学竞赛题准确率提升至15%以上

**中期目标 (2周内)**:
- [ ] 整体准确率达到50%以上
- [ ] 细粒度分类错误减少50%
- [ ] 建立持续改进流程

**长期目标 (1个月内)**:
- [ ] 整体准确率达到55%以上
- [ ] 各类错误模式都有显著改善
- [ ] 建立自动化的模型优化pipeline
