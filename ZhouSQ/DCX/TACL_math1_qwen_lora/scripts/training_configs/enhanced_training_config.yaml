# 增强训练配置 - 针对badcase分析结果优化
# 基于您当前的训练配置进行改进

# 基础配置
bf16: true
cutoff_len: 2048
dataset: math1_enhanced  # 使用增强数据集
dataset_dir: data
ddp_timeout: 180000000
deepspeed: cache/ds_z3_config.json
do_train: true
enable_thinking: true
finetuning_type: lora
flash_attn: auto

# 优化的训练参数
gradient_accumulation_steps: 8
include_num_input_tokens_seen: true
learning_rate: 3.0e-05  # 降低学习率，更稳定的训练
logging_steps: 5
lr_scheduler_type: cosine
max_grad_norm: 1.0
max_samples: 150000  # 增加样本数量
num_train_epochs: 5.0  # 增加训练轮数
optim: adamw_torch
packing: false
per_device_train_batch_size: 2
plot_loss: true
preprocessing_num_workers: 16
report_to: none
save_steps: 200  # 更频繁的保存
warmup_steps: 100  # 添加warmup

# LoRA优化配置
lora_alpha: 512  # 增加alpha值，增强LoRA效果
lora_dropout: 0.1  # 添加dropout防止过拟合
lora_rank: 16  # 增加rank，提升表达能力
lora_target: all

# 模型配置
model_name_or_path: /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B
stage: sft
template: qwen3
trust_remote_code: true

# 输出配置
output_dir: saves/Qwen3-8B-Thinking/lora/enhanced_train_$(date +%Y-%m-%d-%H-%M-%S)

# 评估配置
eval_strategy: steps
eval_steps: 200
do_eval: true
eval_dataset: math1_test
load_best_model_at_end: true
metric_for_best_model: eval_loss
greater_is_better: false

# 早停配置
early_stopping_patience: 3
early_stopping_threshold: 0.01
