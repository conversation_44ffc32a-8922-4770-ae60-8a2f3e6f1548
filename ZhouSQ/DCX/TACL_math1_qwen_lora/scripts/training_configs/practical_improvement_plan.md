# 实用改进方案 - 基于LLaMA-Factory可行性

## ✅ 完全可行的改进方案

### 方案1: 数据增强 (推荐指数: ⭐⭐⭐⭐⭐)
**效果**: 预期提升10-15个百分点
**实现难度**: 简单
**执行时间**: 30分钟准备 + 4-6小时训练

```bash
# 1. 创建增强数据集
python scripts/data_enhancement/create_enhanced_dataset.py

# 2. 使用增强数据集训练
cd LLaMA-Factory
llamafactory-cli train \
    --dataset math1_enhanced \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B \
    --stage sft \
    --do_train \
    --finetuning_type lora \
    --template qwen3 \
    --cutoff_len 2048 \
    --num_train_epochs 4.0 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 8 \
    --enable_thinking \
    --deepspeed cache/ds_z3_config.json \
    --output_dir saves/Qwen3-8B-Thinking/lora/enhanced_$(date +%Y%m%d_%H%M%S)
```

### 方案2: LoRA参数优化 (推荐指数: ⭐⭐⭐⭐)
**效果**: 预期提升5-8个百分点
**实现难度**: 极简单
**执行时间**: 只需修改训练参数

```bash
# 在训练命令中添加以下参数
--lora_rank 16 \
--lora_alpha 512 \
--lora_dropout 0.1
```

### 方案3: 学习率和训练策略优化 (推荐指数: ⭐⭐⭐⭐)
**效果**: 预期提升3-5个百分点
**实现难度**: 极简单
**执行时间**: 只需修改训练参数

```bash
# 在训练命令中添加以下参数
--learning_rate 3e-05 \
--warmup_steps 100 \
--num_train_epochs 4.0 \
--max_samples 150000
```

## 🎯 组合方案 (推荐)

### 完整优化命令
```bash
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory

llamafactory-cli train \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-8B \
    --stage sft \
    --do_train \
    --finetuning_type lora \
    --template qwen3 \
    --dataset math1_enhanced \
    --cutoff_len 2048 \
    --learning_rate 3e-05 \
    --num_train_epochs 4.0 \
    --max_samples 150000 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 8 \
    --lr_scheduler_type cosine \
    --warmup_steps 100 \
    --bf16 \
    --logging_steps 5 \
    --save_steps 200 \
    --eval_steps 200 \
    --eval_strategy steps \
    --do_eval \
    --eval_dataset math1_test \
    --load_best_model_at_end \
    --metric_for_best_model eval_loss \
    --lora_target all \
    --lora_rank 16 \
    --lora_alpha 512 \
    --lora_dropout 0.1 \
    --enable_thinking \
    --deepspeed cache/ds_z3_config.json \
    --output_dir saves/Qwen3-8B-Thinking/lora/optimized_$(date +%Y%m%d_%H%M%S) \
    --overwrite_output_dir \
    --plot_loss
```

## 📊 预期改进效果

### 保守估计
- **当前准确率**: 38.68%
- **数据增强**: +10% → 48.68%
- **LoRA优化**: +5% → 53.68%
- **训练策略**: +3% → 56.68%
- **总提升**: **18个百分点**

### 乐观估计
- **总准确率**: 可能达到60%+
- **数学竞赛题**: 从0%提升到20%+
- **细粒度分类**: 错误率减少40%+

## ⚠️ 风险评估

### 低风险
- ✅ 所有改进都基于标准参数
- ✅ 完全兼容现有环境
- ✅ 可以随时回退到原始配置

### 需要注意
- 📊 显存使用增加约10%
- ⏰ 训练时间增加约20-30%
- 💾 模型文件大小增加约2倍

## 🚀 执行建议

### 立即执行 (今天就可以开始)
1. **数据增强**: 最重要，效果最明显
2. **LoRA参数优化**: 最简单，风险最低
3. **学习率调优**: 成本最低，稳定性最好

### 分步验证
1. 先用小数据集验证配置正确性
2. 再用完整数据集进行训练
3. 对比前后效果，确认改进有效

### 成功标准
- [ ] 整体准确率提升到50%以上
- [ ] 数学竞赛题准确率提升到15%以上
- [ ] 训练过程稳定，无过拟合现象

## 💡 额外建议

### 如果显存不足
- 减少 `per_device_train_batch_size` 到 1
- 增加 `gradient_accumulation_steps` 到 16
- 使用 `fp16` 替代 `bf16`

### 如果训练时间过长
- 减少 `num_train_epochs` 到 3.0
- 减少 `max_samples` 到 100000
- 使用更少的 `save_steps` 和 `eval_steps`

### 监控指标
- `training_loss` 应该稳定下降
- `eval_loss` 不应该持续上升
- 定期检查生成的预测质量
