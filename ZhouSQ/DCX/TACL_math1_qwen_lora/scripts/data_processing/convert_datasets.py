import json
import os

def convert_dataset_format(input_file, output_file):
    """
    将qwen-fintune格式的数据集转换为LLaMA-Factory格式
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    
    # 统一的instruction，指导模型输出与题目最相关的9个标签
    instruction = "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。"
    # instruction = "请根据给定的数学题目内容，从小学数学知识树中选择与该题目最相关的9个标签。标签应该按照从宏观到具体的层次结构排列，包括学科分类、知识领域、题目类型、具体知识点等。"
    
    # 读取原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    # 转换数据格式
    converted_data = []
    
    for item in original_data:
        # 过滤掉以"补充知识点"开头的标签
        # filtered_labels = [label for label in item['doc_label'] if not label.startswith("补充知识点")]
        filtered_labels = [label for label in item['doc_label']]
        
        # 将doc_label数组转换为字符串格式，每个标签占一行
        labels_str = '\n'.join([f"{label}" for i, label in enumerate(filtered_labels)])
        
        converted_item = {
            "instruction": instruction,
            "input": item['doc_token'],
            "output": labels_str
        }
        
        converted_data.append(converted_item)
    
    # 保存转换后的数据
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(converted_data, f, ensure_ascii=False, indent=2)
    
    print(f"已成功转换 {len(converted_data)} 条数据从 {input_file} 到 {output_file}")

def main():
    # 定义输入和输出目录
    input_dir = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math1_level7_data"
    output_dir = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/LLaMA-Factory/data/converted-1"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 需要转换的文件列表
    files_to_convert = [
        ("wos_train.json", "math1_train.json"),
        ("wos_test.json", "math1_test.json"),
        # ("wos_train.json", "train_converted.json"), 
        # ("wos_val.json", "val_converted.json")
    ]
    
    # 转换每个文件
    for input_filename, output_filename in files_to_convert:
        input_path = os.path.join(input_dir, input_filename)
        output_path = os.path.join(output_dir, output_filename)
        
        if os.path.exists(input_path):
            convert_dataset_format(input_path, output_path)
        else:
            print(f"警告: 文件 {input_path} 不存在，跳过转换")
    
    print("\n所有数据集转换完成！")
    print(f"转换后的文件保存在: {output_dir}")

if __name__ == "__main__":
    main()
