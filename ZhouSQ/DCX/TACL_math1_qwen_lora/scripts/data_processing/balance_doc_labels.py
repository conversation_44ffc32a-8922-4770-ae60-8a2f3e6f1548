#!/usr/bin/env python3
"""
数据集标签平衡脚本
功能：筛选出doc_label数量>=40的类别，并保持题目多样性地保留40个样本
"""

import json
import random
from collections import defaultdict, Counter
from typing import List, Dict, Any
import argparse
import os
import re
import math

def extract_features(sample: Dict[str, Any]) -> Dict[str, Any]:
    """提取题目特征用于多样性分析 - 扩充版本"""
    features = {}

    # 获取题目文本和标签路径
    question = sample.get('doc_token', sample.get('question', ''))
    label_path = sample.get('doc_label', [])

    # ===== 1. 基础长度特征 =====
    features['length'] = len(question)
    features['length_category'] = 'short' if len(question) < 100 else 'medium' if len(question) < 200 else 'very_long' if len(question) > 400 else 'long'
    features['word_count'] = len(question.split())
    features['sentence_count'] = len([s for s in re.split(r'[。！？]', question) if s.strip()])

    # ===== 2. 数学符号和表达式特征 =====
    features['has_fraction'] = bool(re.search(r'frac\{|分之|\d+/\d+', question))
    features['has_decimal'] = bool(re.search(r'\d+\.\d+', question))
    features['has_percentage'] = '%' in question or '百分' in question
    features['has_negative'] = bool(re.search(r'-\d+', question))
    features['has_mathematical_symbols'] = any(symbol in question for symbol in ['≥', '≤', '≠', '≈', '∞', '√', '²', '³', '∵', '∴'])
    features['math_expression_count'] = len(re.findall(r'\$.*?\$|\\frac\{.*?\}\{.*?\}|\d+[+\-×÷*/]\d+', question))

    # ===== 3. 运算类型特征 =====
    features['has_addition'] = '+' in question or '加' in question or '和' in question
    features['has_subtraction'] = '-' in question or '减' in question or '差' in question
    features['has_multiplication'] = '×' in question or '*' in question or '乘' in question or '积' in question
    features['has_division'] = '÷' in question or '/' in question or '除' in question or '商' in question

    # 运算复杂度
    operations = ['+', '-', '×', '÷', '*', '/']
    operation_count = sum(question.count(op) for op in operations)
    features['operation_complexity'] = 'none' if operation_count == 0 else 'single' if operation_count == 1 else 'multiple'
    features['operation_count'] = operation_count

    # ===== 4. 题目结构特征 =====
    features['has_table'] = '|' in question or '---' in question or '表格' in question
    features['has_image_reference'] = any(word in question for word in ['图', '如图', '下图', '上图', '图形'])
    features['is_multiple_choice'] = any(pattern in question for pattern in ['A.', 'B.', 'C.', 'D.', 'A、', 'B、', 'C、', 'D、'])
    features['is_fill_blank'] = any(pattern in question for pattern in ['（）', '____', '( )', '空', '填空'])
    features['is_calculation'] = any(word in question for word in ['计算', '求', '等于', '得'])
    features['is_proof'] = any(word in question for word in ['证明', '说明', '推导', '验证'])
    features['is_comparison'] = any(word in question for word in ['比较', '大小', '哪个', '>', '<'])

    # ===== 5. 数学概念特征 =====
    features['is_geometry'] = any(word in question for word in ['三角形', '圆', '正方形', '长方形', '面积', '周长', '角度', '旋转', '平行', '垂直'])
    features['is_algebra'] = any(word in question for word in ['方程', '未知数', 'x', 'X', '代数', '解'])
    features['is_statistics'] = any(word in question for word in ['统计', '平均', '图表', '折线图', '柱状图', '数据'])
    features['is_probability'] = any(word in question for word in ['概率', '可能', '随机', '机会'])
    features['is_measurement'] = any(word in question for word in ['米', '千克', '升', '时间', '长度', '重量', '面积', '体积'])
    features['is_number_theory'] = any(word in question for word in ['质数', '合数', '因数', '倍数', '最大公约数', '最小公倍数'])

    # ===== 6. 特殊数学主题特征 =====
    features['is_finding_defective'] = any(word in question for word in ['次品', '天平', '称', '轻重'])
    features['is_rotation'] = any(word in question for word in ['旋转', '顺时针', '逆时针', '角度', '度'])
    features['is_fraction_operation'] = any(word in question for word in ['分数', '通分', '约分', '分母', '分子'])
    features['is_decimal_operation'] = any(word in question for word in ['小数', '小数点', '位数'])
    features['is_unit_conversion'] = any(word in question for word in ['换算', '单位', '转换'])
    features['is_pattern_finding'] = any(word in question for word in ['规律', '找规律', '按规律', '下一个'])

    # ===== 7. 应用题特征 =====
    features['is_word_problem'] = any(word in question for word in ['小明', '小红', '小李', '买', '卖', '花了', '用了', '应用'])
    features['has_real_context'] = any(word in question for word in ['学校', '商店', '家', '公园', '超市', '工厂'])
    features['has_units'] = any(unit in question for unit in ['米', '千克', '升', '元', '分', '秒', '时', '天', '年', 'kg', 'mm', 'cm', 'km'])

    # ===== 8. 数字特征 =====
    numbers = re.findall(r'\d+\.?\d*', question)
    features['number_count'] = len(numbers)
    if numbers:
        numeric_values = [float(n) for n in numbers if n]
        features['max_number'] = max(numeric_values)
        features['min_number'] = min(numeric_values)
        features['has_large_numbers'] = features['max_number'] > 1000
        features['has_small_decimals'] = any(0 < float(n) < 1 for n in numbers if '.' in n)
    else:
        features['max_number'] = 0
        features['min_number'] = 0
        features['has_large_numbers'] = False
        features['has_small_decimals'] = False

    # ===== 9. 语言和表达特征 =====
    features['has_chinese_numerals'] = any(word in question for word in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'])
    features['has_formal_language'] = any(word in question for word in ['∵', '∴', '设', '假设', '根据', '可得'])
    features['reasoning_word_count'] = sum(1 for word in ['因为', '所以', '由于', '根据', '可得', '因此', '故', '则'] if word in question)
    features['step_indicator_count'] = sum(1 for word in ['第一', '第二', '第三', '首先', '然后', '最后', '步骤', '①', '②', '③'] if word in question)

    # ===== 10. 知识点层次特征 =====
    if label_path:
        features['label_depth'] = len(label_path)
        features['main_category'] = label_path[1] if len(label_path) > 1 else 'unknown'
        features['sub_category'] = label_path[2] if len(label_path) > 2 else 'unknown'
        features['specific_topic'] = label_path[3] if len(label_path) > 3 else 'unknown'
    else:
        features['label_depth'] = 0
        features['main_category'] = 'unknown'
        features['sub_category'] = 'unknown'
        features['specific_topic'] = 'unknown'

    # ===== 11. 复杂度综合评估 =====
    complexity_score = 0
    complexity_score += features['sentence_count'] * 0.5
    complexity_score += features['math_expression_count'] * 0.3
    complexity_score += features['operation_count'] * 0.2
    complexity_score += features['reasoning_word_count'] * 0.4
    complexity_score += features['step_indicator_count'] * 0.3

    features['complexity_score'] = complexity_score
    features['complexity_level'] = 'simple' if complexity_score < 2 else 'medium' if complexity_score < 5 else 'complex'

    return features

def calculate_diversity_score(samples: List[Dict], selected_indices: List[int], candidate_idx: int) -> float:
    """计算添加候选样本后的多样性得分 - 增强版本"""
    if not selected_indices:
        return 1.0

    candidate_features = extract_features(samples[candidate_idx])

    # 计算与已选择样本的相似度
    similarities = []
    for idx in selected_indices:
        selected_features = extract_features(samples[idx])

        # 分类计算不同类型特征的相似度
        similarity_scores = []

        # 1. 布尔特征相似度
        bool_features = [k for k in candidate_features if isinstance(candidate_features[k], bool)]
        if bool_features:
            bool_similarity = sum(1 for k in bool_features
                                if k in selected_features and candidate_features[k] == selected_features[k])
            bool_similarity /= len(bool_features)
            similarity_scores.append(('bool', bool_similarity, 0.3))  # 权重0.3

        # 2. 分类特征相似度
        categorical_features = [k for k in candidate_features if isinstance(candidate_features[k], str)]
        if categorical_features:
            cat_similarity = sum(1 for k in categorical_features
                               if k in selected_features and candidate_features[k] == selected_features[k])
            cat_similarity /= len(categorical_features)
            similarity_scores.append(('categorical', cat_similarity, 0.4))  # 权重0.4

        # 3. 数值特征相似度
        numeric_features = [k for k in candidate_features if isinstance(candidate_features[k], (int, float))]
        if numeric_features:
            numeric_similarities = []
            for k in numeric_features:
                if k in selected_features:
                    val1, val2 = candidate_features[k], selected_features[k]
                    if val1 == 0 and val2 == 0:
                        numeric_similarities.append(1.0)
                    elif max(val1, val2) == 0:
                        numeric_similarities.append(0.0)
                    else:
                        # 使用更精细的数值相似度计算
                        max_val = max(abs(val1), abs(val2))
                        min_val = min(abs(val1), abs(val2))
                        if k in ['length', 'word_count', 'number_count']:
                            # 对于计数类特征，使用对数缩放
                            if max_val > 0:
                                numeric_similarities.append(min_val / max_val)
                            else:
                                numeric_similarities.append(1.0)
                        elif k in ['complexity_score']:
                            # 对于复杂度得分，使用差值相似度
                            diff = abs(val1 - val2)
                            max_possible_diff = max(val1, val2, 10)  # 假设最大差值
                            numeric_similarities.append(1 - diff / max_possible_diff)
                        else:
                            # 默认归一化相似度
                            numeric_similarities.append(min_val / max_val if max_val > 0 else 1.0)

            if numeric_similarities:
                avg_numeric_similarity = sum(numeric_similarities) / len(numeric_similarities)
                similarity_scores.append(('numeric', avg_numeric_similarity, 0.3))  # 权重0.3

        # 4. 计算加权总相似度
        if similarity_scores:
            total_weight = sum(weight for _, _, weight in similarity_scores)
            weighted_similarity = sum(score * weight for _, score, weight in similarity_scores) / total_weight
            similarities.append(weighted_similarity)
        else:
            similarities.append(0.5)  # 默认中等相似度

    # 多样性得分 = 1 - 平均相似度
    avg_similarity = sum(similarities) / len(similarities) if similarities else 0

    # 添加随机扰动避免完全相同的得分
    import random
    diversity_score = 1 - avg_similarity
    diversity_score += random.uniform(-0.01, 0.01)  # 小幅随机扰动

    return max(0, min(1, diversity_score))  # 确保在[0,1]范围内

def select_diverse_samples(samples: List[Dict], target_count: int = 40) -> List[Dict]:
    """使用贪心算法选择多样性最大的样本"""
    if len(samples) <= target_count:
        return samples
    
    selected_indices = []
    remaining_indices = list(range(len(samples)))
    
    # 1. 随机选择第一个样本
    first_idx = random.choice(remaining_indices)
    selected_indices.append(first_idx)
    remaining_indices.remove(first_idx)
    
    # 2. 贪心选择剩余样本
    while len(selected_indices) < target_count and remaining_indices:
        best_idx = None
        best_score = -1
        
        for candidate_idx in remaining_indices:
            score = calculate_diversity_score(samples, selected_indices, candidate_idx)
            if score > best_score:
                best_score = score
                best_idx = candidate_idx
        
        if best_idx is not None:
            selected_indices.append(best_idx)
            remaining_indices.remove(best_idx)
    
    return [samples[i] for i in selected_indices]

def balance_dataset(input_file: str, output_file: str, min_samples: int = 40, max_samples: int = 40):
    """平衡数据集中的doc_label分布"""
    
    print(f"📖 读取数据文件: {input_file}")
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 原始数据集大小: {len(data)} 个样本")
    
    # 按doc_label分组
    label_groups = defaultdict(list)
    for item in data:
        # 将doc_label转换为字符串作为key
        label_key = " -> ".join(item['doc_label'])
        label_groups[label_key].append(item)
    
    print(f"📈 发现 {len(label_groups)} 个不同的doc_label")
    
    # 统计各标签的样本数量
    label_counts = {label: len(samples) for label, samples in label_groups.items()}
    
    # 找出样本数>=min_samples的标签
    frequent_labels = {label: count for label, count in label_counts.items() if count >= min_samples}
    
    print(f"🎯 样本数>={min_samples}的标签: {len(frequent_labels)} 个")
    for label, count in sorted(frequent_labels.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {count:3d}个: {label[:80]}...")
    
    # 处理数据
    balanced_data = []
    processing_stats = {}
    
    for label, samples in label_groups.items():
        if len(samples) >= min_samples:
            # 需要平衡的标签
            print(f"\n🔄 处理标签 (原有{len(samples)}个样本): {label[:60]}...")
            
            if len(samples) > max_samples:
                # 使用多样性选择算法
                selected_samples = select_diverse_samples(samples, max_samples)
                print(f"  ✅ 从{len(samples)}个样本中选择了{len(selected_samples)}个多样性样本")
                processing_stats[label] = {'original': len(samples), 'selected': len(selected_samples), 'method': 'diversity_selection'}
            else:
                # 保留所有样本
                selected_samples = samples
                print(f"  ✅ 保留所有{len(selected_samples)}个样本")
                processing_stats[label] = {'original': len(samples), 'selected': len(selected_samples), 'method': 'keep_all'}
            
            balanced_data.extend(selected_samples)
        else:
            # 样本数不足，跳过
            print(f"⏭️  跳过标签 (仅{len(samples)}个样本): {label[:60]}...")
            processing_stats[label] = {'original': len(samples), 'selected': 0, 'method': 'skipped'}
    
    print(f"\n📊 处理结果统计:")
    print(f"  原始样本总数: {len(data)}")
    print(f"  平衡后样本总数: {len(balanced_data)}")
    print(f"  保留的标签数: {len([s for s in processing_stats.values() if s['selected'] > 0])}")
    print(f"  跳过的标签数: {len([s for s in processing_stats.values() if s['selected'] == 0])}")
    
    # 保存结果
    print(f"\n💾 保存平衡后的数据到: {output_file}")
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(balanced_data, f, ensure_ascii=False, indent=2)
    
    # 保存处理统计
    stats_file = output_file.replace('.json', '_stats.json')
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(processing_stats, f, ensure_ascii=False, indent=2)
    
    print(f"📈 处理统计保存到: {stats_file}")
    print("✅ 数据平衡完成!")
    
    return balanced_data, processing_stats

def main():
    parser = argparse.ArgumentParser(description='平衡数据集中的doc_label分布')
    parser.add_argument('--input', '-i', 
                       default='/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math1_level7_data/merge_deduplicated.json',
                       help='输入JSON文件路径')
    parser.add_argument('--output', '-o',
                       default='/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/math1_level7_data/merge_balanced.json',
                       help='输出JSON文件路径')
    parser.add_argument('--min-samples', type=int, default=40,
                       help='最小样本数阈值 (默认: 40)')
    parser.add_argument('--max-samples', type=int, default=40,
                       help='最大保留样本数 (默认: 40)')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子 (默认: 42)')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    
    print("🚀 开始数据集标签平衡处理")
    print(f"📁 输入文件: {args.input}")
    print(f"📁 输出文件: {args.output}")
    print(f"🎯 最小样本数: {args.min_samples}")
    print(f"🎯 最大保留样本数: {args.max_samples}")
    print(f"🎲 随机种子: {args.seed}")
    print("=" * 60)
    
    try:
        balanced_data, stats = balance_dataset(
            args.input, 
            args.output, 
            args.min_samples, 
            args.max_samples
        )
        
        print("\n🎉 处理完成!")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
