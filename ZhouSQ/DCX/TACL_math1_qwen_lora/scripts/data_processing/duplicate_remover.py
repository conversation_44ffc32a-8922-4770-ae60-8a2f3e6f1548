import re
import pandas as pd
from collections import defaultdict

def normalize_text(text):
    """标准化文本，忽略格式差异"""
    # 移除所有特殊符号和格式标记
    text = re.sub(r'[^\w\s公顷平方千米平方米]', '', text)
    # 替换所有空白字符为单个空格
    text = re.sub(r'\s+', ' ', text)
    # 转换为小写
    text = text.lower()
    # 去除首尾空格
    return text.strip()

def load_data(file_path):
    """加载数据"""
    try:
        # 尝试以JSON格式加载
        return pd.read_json(file_path)
    except:
        try:
            # 尝试以CSV格式加载
            return pd.read_csv(file_path)
        except:
            print(f"无法加载文件: {file_path}")
            return None

def find_duplicates(df, max_groups=10):
    """找出重复的文本组"""
    # 创建标准化文本到原始索引的映射
    text_groups = defaultdict(list)
    
    for idx, row in df.iterrows():
        normalized = normalize_text(row['doc_token'])
        text_groups[normalized].append(idx)
    
    # 过滤出有重复的组，并按重复数量排序
    duplicate_groups = [group for group in text_groups.values() if len(group) > 1]
    duplicate_groups.sort(key=lambda x: len(x), reverse=True)
    
    # 返回前max_groups组
    return duplicate_groups[:max_groups]

def review_duplicates(df, duplicate_groups):
    """交互式审核重复项"""
    # 创建一个集合来存储需要删除的索引
    to_delete = set()
    
    for i, group in enumerate(duplicate_groups, 1):
        print(f"\n===== 重复组 {i}/{len(duplicate_groups)} =====")
        print(f"该组包含 {len(group)} 条重复数据，索引为: {group}")
        
        # 显示每组数据的内容
        for j, idx in enumerate(group, 1):
            print(f"\n--- 数据 {j} (原始索引: {idx}) ---")
            # 只显示前200个字符，避免输出过长
            preview = df.loc[idx, 'doc_token'][:200] + ('...' if len(df.loc[idx, 'doc_token']) > 200 else '')
            print(preview)
        
        # 获取用户输入
        while True:
            user_input = input("\n请输入要删除的数据编号(用空格分隔)，或输入'y'保留全部，'n'删除全部: ").strip().lower()
            
            if user_input == 'y':
                # 保留全部，不添加到删除列表
                print("保留该组所有数据")
                break
            elif user_input == 'n':
                # 删除全部，添加所有索引到删除列表
                print(f"删除该组所有 {len(group)} 条数据")
                to_delete.update(group)
                break
            else:
                # 尝试解析用户输入的编号
                try:
                    # 用户输入的是1-based编号，转换为0-based索引
                    delete_indices = [int(num) - 1 for num in user_input.split()]
                    # 验证输入是否有效
                    valid = True
                    for idx in delete_indices:
                        if idx < 0 or idx >= len(group):
                            valid = False
                            break
                    
                    if valid:
                        # 添加要删除的原始索引
                        to_delete.update([group[idx] for idx in delete_indices])
                        print(f"已标记删除 {len(delete_indices)} 条数据")
                        break
                    else:
                        print(f"输入无效，请输入1到{len(group)}之间的数字")
                except ValueError:
                    print("输入格式错误，请输入数字、'y'或'n'")
    
    return to_delete

def main():
    # 加载数据
    file_path = input("请输入数据文件路径(JSON或CSV): ").strip()
    df = load_data(file_path)
    
    if df is None or 'doc_token' not in df.columns:
        print("数据加载失败或缺少'doc_token'列")
        return
    
    print(f"成功加载 {len(df)} 条数据")
    
    # 查找重复组
    print("正在查找重复数据...")
    duplicate_groups = find_duplicates(df)
    
    if not duplicate_groups:
        print("未发现重复数据")
        return
    
    print(f"发现 {len(duplicate_groups)} 组重复数据")
    
    # 审核重复项
    to_delete = review_duplicates(df, duplicate_groups)
    
    # 执行删除操作
    if to_delete:
        df_cleaned = df.drop(index=to_delete)
        print(f"\n已删除 {len(to_delete)} 条数据，剩余 {len(df_cleaned)} 条数据")
        
        # 保存清洗后的数据
        output_path = input("请输入保存清洗后数据的文件路径(JSON): ").strip()
        if not output_path.endswith('.json'):
            output_path += '.json'
        
        df_cleaned.to_json(output_path, orient='records', indent=2, force_ascii=False)
        print(f"清洗后的数据已保存到 {output_path}")
    else:
        print("\n未删除任何数据")

if __name__ == "__main__":
    main()
