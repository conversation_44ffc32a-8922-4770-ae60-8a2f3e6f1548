[{"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "专家建议每人每天糖摄入量最好控制在 $\\frac{1}{40}$ kg以内否则易导致蛀牙、肥胖和一些慢性疾病根据专家建议成人每个月（按30天算）的糖摄入量不超过多少千克已知每人每天糖摄入量最好控制在 $\\frac{1}{40}$ kg以内一个月按30天算求每个月糖摄入量不超过多少千克就是求30个 $\\frac{1}{40}$ kg是多少用乘法计算列式为 $\\frac{1}{40}\\times30=\\frac{3}{4}$ （kg）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "大约从一万年前开始青藏高原平均每年上升约 $\\frac{7}{100}$ 米按照这个速度50年它能长高多少米100年呢本题可根据每年上升的高度乘以年数来计算长高的高度计算50年长高的高度已知青藏高原平均每年上升约 $\\frac{7}{100}$ 米那么50年长高的高度为每年上升高度乘以50年即 $\\frac{7}{100}\\times50=\\frac{7}{2}$ （米）计算100年长高的高度同理100年长高的高度为每年上升高度乘以100年即 $\\frac{7}{100}\\times100=7$ （米）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "毛毛的学习桌有点不平稳他就用一张厚 $\\frac{3}{16}$ mm的牛皮纸对折三次后垫在桌子的一条腿下面恰好放稳了这条桌子腿短了多少毫米本题可先分析对折三次后纸的层数再结合每层纸的厚度求出桌子腿短的长度分析对折三次后纸的层数将纸对折1次纸会变成2层对折2次这2层纸又分别被对折此时纸的层数变为2×2 = 4层对折3次这4层纸又分别被对折纸的层数变为4×2 = 8层计算桌子腿短的长度已知每张牛皮纸的厚度是 $\\frac{3}{16}$ mm对折三次后纸有8层那么桌子腿短的长度就是8个 $\\frac{3}{16}$ mm用乘法计算可得 $\\frac{3}{16}×8=\\frac{3}{2}$ （mm）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "捆一摞书用一卷彩带的 $\\frac{2}{9}$ 捆3摞书一共需要用这卷彩带的几分之几本题可根据乘法的意义来求解捆 $3$ 摞书所需彩带占这卷彩带的比例已知捆一摞书用这卷彩带的 $\\frac{2}{9}$ 要求捆 $3$ 摞书一共需要用这卷彩带的几分之几就是求 $3$ 个 $\\frac{2}{9}$ 是多少根据乘法的意义可列式为 $\\frac{2}{9}\\times3$ 计算 $\\frac{2}{9}\\times3$ 时整数 $3$ 与分子 $2$ 相乘分母不变可得 $\\frac{2\\times3}{9}=\\frac{6}{9}$ 再对 $\\frac{6}{9}$ 进行约分分子分母同时除以 $3$ 得到 $\\frac{6\\div3}{9\\div3}=\\frac{2}{3}$ 所以捆 $3$ 摞书一共需要用这卷彩带的 $\\frac{2}{3}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "  $\\frac{1}{6}\\times10$ $\\frac{1}{6}$    $\\frac{3}{4}\\times1$ $\\frac{3}{4}$   $\\frac{7}{13}\\times0$ $\\frac{7}{13}$ $\\frac{9}{44}\\times11$ $3$ 本题可根据分数乘法的计算方法分别算出式子左边乘法算式的结果再与右边的数比较大小 - 比较 $\\frac{1}{6}\\times10$ 与 $\\frac{1}{6}$ 的大小 先计算 $\\frac{1}{6}\\times10=\\frac{10}{6}$ 因为 $\\frac{10}{6}>\\frac{1}{6}$ 所以 $\\frac{1}{6}\\times10>\\frac{1}{6}$  - 比较 $\\frac{3}{4}\\times1$ 与 $\\frac{3}{4}$ 的大小 根据任何数乘 $1$ 都得原数可得 $\\frac{3}{4}\\times1=\\frac{3}{4}$ 所以 $\\frac{3}{4}\\times1=\\frac{3}{4}$  - 比较 $\\frac{7}{13}\\times0$ 与 $\\frac{7}{13}$ 的大小 因为任何数乘 $0$ 都得 $0$ 所以 $\\frac{7}{13}\\times0=0$ 又因为 $0<\\frac{7}{13}$ 所以 $\\frac{7}{13}\\times0<\\frac{7}{13}$  - 比较 $\\frac{9}{44}\\times11$ 与 $3$ 的大小 先计算 $\\frac{9}{44}\\times11=\\frac{9}{4}=2.25$ 因为 $2.25<3$ 所以 $\\frac{9}{44}\\times11<3$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$\\frac{2}{13}$ ×5= $\\frac{3}{19}$ ×6=       $\\frac{5}{6}$ ×12=　　　　　　5× $\\frac{4}{11}$ =  $\\frac{5}{12}$ ×8=        $\\frac{2}{7}$ ×3=　　　　　　 6× $\\frac{3}{5}$ =       $\\frac{4}{21}$ ×9=1. 对于 $\\frac{2}{13}$ ×5分数乘整数用分子乘整数的积作分子分母不变即 $\\frac{2×5}{13}=\\frac{10}{13}$ 2. 对于 $\\frac{3}{19}$ ×6同理可得 $\\frac{3×6}{19}=\\frac{18}{19}$ 3. 对于 $\\frac{5}{6}$ ×12 $\\frac{5×12}{6}=5×2=10$ 4. 对于5× $\\frac{4}{11}$  $\\frac{5×4}{11}=\\frac{20}{11}$ 5. 对于 $\\frac{5}{12}$ ×8 $\\frac{5×8}{12}=\\frac{10}{3}$ 6. 对于 $\\frac{2}{7}$ ×3 $\\frac{2×3}{7}=\\frac{6}{7}$ 7. 对于6× $\\frac{3}{5}$  $\\frac{6×3}{5}=\\frac{18}{5}$ 8. 对于 $\\frac{4}{21}$ ×9 $\\frac{4×9}{21}=\\frac{12}{7}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "15千克的 $\\frac{3}{5}$ 是多少千克 求一个数的几分之几是多少用乘法计算所以求15千克的 $\\frac{3}{5}$ 是多少可列式为 $15\\times\\frac{3}{5}$ 15和5约分后再计算得出结果为9千克", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n整数乘分数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小明共有3本书每本书都是60页小明已经读了全部的 $\\frac{2}{3}$ 小明还有多少页没读本题可先算出书的总页数再根据已读的比例求出未读的比例最后求出未读的页数1. 计算书的总页数已知小明共有 3 本书每本书 60 页根据总页数 = 每本书的页数×书的本数可得总页数为 $60\\times3=180$ 页2. 计算未读的比例因为小明已经读了全部的 $\\frac{2}{3}$ 将全部页数看作单位“1”那么未读的比例为 $1-\\frac{2}{3}=\\frac{1}{3}$ 3. 计算未读的页数根据未读页数 = 总页数×未读的比例可得未读页数为 $180\\times\\frac{1}{3}=60$ 页", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n稍复杂的求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一瓶饮料有 $\\frac{9}{20}$ 升5瓶饮料有多少升求5瓶饮料有多少升就是求5个 $\\frac{9}{20}$ 升是多少用乘法计算列式为 $\\frac{9}{20}×5$ 根据分数乘整数的计算方法用分数的分子和整数相乘的积作分子分母不变能约分的先约分再计算 $\\frac{9}{20}×5=\\frac{9}{4}$ 升", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1只树袋熊一天大约吃 $\\frac{6}{7}$ kg的桉树叶10只树袋熊一天大约能吃多少千克桉树叶本题可根据乘法的意义来求解求几个相同加数的和的简便运算用乘法已知1只树袋熊一天大约吃 $\\frac{6}{7}$ kg的桉树叶求10只树袋熊一天吃的桉树叶重量就是求10个 $\\frac{6}{7}$ 是多少用乘法计算列式为 $\\frac{6}{7}\\times10=\\frac{60}{7}$ （kg）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "陈红看一本120页的故事书每天看这本书的 $\\frac{1}{10}$ , 8天看了这本书的几分之几已知每天看这本书的 $\\frac{1}{10}$ 要求8天看了这本书的几分之几就是求8个 $\\frac{1}{10}$ 是多少用乘法计算即 $\\frac{1}{10}\\times8=\\frac{8}{10}=\\frac{4}{5}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "王强从三楼爬到四楼用了 $\\frac{1}{5}$ 分钟照这样计算他从一楼爬到五楼用多少分钟从三楼到四楼爬了 $1$ 层楼梯用时 $\\frac{1}{5}$ 分钟从一楼到五楼要爬 $5-1=4$ 层楼梯每层用时 $\\frac{1}{5}$ 分钟那么 $4$ 层用时 $\\frac{1}{5}\\times4=\\frac{4}{5}$ 分钟", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "杯子中原来盛有800mL水小华将杯中的水倒出一些如下图求从杯子中倒出了多少毫升水列式正确的是( )A. $800\\times\\frac{3}{5}$    B. $800\\times\\frac{3}{8}$    C. $800\\times\\frac{5}{8}$    D. $800\\times\\frac{3}{9}$ 根据题目描述杯子中原有800mL水倒出一些后剩余500mL水因此倒出的水量为 $800-500=300$ mL接下来我们来分析选项A. $800\\times\\frac{3}{5}=480$ mL不符合题意B. $800\\times\\frac{3}{8}=300$ mL符合题意C. $800\\times\\frac{5}{8}=500$ mL不符合题意D. $800\\times\\frac{3}{9}=\\frac{800}{3}\\approx266.67$ mL不符合题意因此正确答案为B", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n整数乘分数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "  $\\frac{2}{9}$ ×3=        $\\frac{7}{100}$ ×20=        $\\frac{2}{11}$ ×12=　　　　　　　5× $\\frac{4}{11}$ =  $\\frac{5}{21}$ ×7=        $\\frac{3}{4}$ ×16=         $\\frac{3}{5}$ ×80=　　　　　　　60× $\\frac{7}{15}$ =1. 计算 $\\frac{2}{9}$ ×3分数乘整数用分数的分子和整数相乘的积作分子分母不变 $\\frac{2}{9}$ ×3 = $\\frac{2×3}{9}$ = $\\frac{6}{9}$ 约分后得 $\\frac{2}{3}$ 2. 计算 $\\frac{7}{100}$ ×20同理 $\\frac{7}{100}$ ×20 = $\\frac{7×20}{100}$ = $\\frac{140}{100}$ 约分后得 $\\frac{7}{5}$ 3. 计算 $\\frac{2}{11}$ ×12 $\\frac{2}{11}$ ×12 = $\\frac{2×12}{11}$ = $\\frac{24}{11}$ 4. 计算5× $\\frac{4}{11}$ 5× $\\frac{4}{11}$ = $\\frac{5×4}{11}$ = $\\frac{20}{11}$ 5. 计算 $\\frac{5}{21}$ ×7 $\\frac{5}{21}$ ×7 = $\\frac{5×7}{21}$ = $\\frac{35}{21}$ 约分后得 $\\frac{5}{3}$ 6. 计算 $\\frac{3}{4}$ ×16 $\\frac{3}{4}$ ×16 = $\\frac{3×16}{4}$ = 127. 计算 $\\frac{3}{5}$ ×80 $\\frac{3}{5}$ ×80 = $\\frac{3×80}{5}$ = 488. 计算60× $\\frac{7}{15}$ 60× $\\frac{7}{15}$ = $\\frac{60×7}{15}$ = 28", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "妈妈给姐姐和弟弟分蛋糕姐姐吃了整个蛋糕的 $\\frac{1}{3}$ 弟弟吃了姐姐剩下那部分蛋糕的 $\\frac{1}{2}$ 最后余下的给了妈妈谁吃到的蛋糕比较多请画图说明本题可先求出弟弟和妈妈分别吃了整个蛋糕的几分之几再比较三人吃的蛋糕占比大小1. 计算弟弟吃的蛋糕占整个蛋糕的比例已知姐姐吃了整个蛋糕的 $\\frac{1}{3}$ 那么剩下的蛋糕为 $1-\\frac{1}{3}=\\frac{2}{3}$ 因为弟弟吃了姐姐剩下那部分蛋糕的 $\\frac{1}{2}$ 所以弟弟吃的蛋糕占整个蛋糕的 $\\frac{2}{3}\\times\\frac{1}{2}=\\frac{1}{3}$ 2. 计算妈妈吃的蛋糕占整个蛋糕的比例用单位“1”减去姐姐和弟弟吃的蛋糕占比可得妈妈吃的蛋糕占比为 $1-\\frac{1}{3}-\\frac{1}{3}=\\frac{1}{3}$ 3. 比较三人吃的蛋糕占比大小姐姐吃了整个蛋糕的 $\\frac{1}{3}$ 弟弟吃了整个蛋糕的 $\\frac{1}{3}$ 妈妈吃了整个蛋糕的 $\\frac{1}{3}$ 即 $\\frac{1}{3}=\\frac{1}{3}=\\frac{1}{3}$ 所以姐姐、弟弟和妈妈吃到的蛋糕一样多4. 画图说明将整个蛋糕看作一个长方形平均分成3份姐姐吃了其中的1份即 $\\frac{1}{3}$ 剩下的2份中弟弟吃了其中的1份也就是剩下部分的 $\\frac{1}{2}$ 同样是整个蛋糕的 $\\frac{1}{3}$ 最后剩下的1份给了妈妈也是整个蛋糕的 $\\frac{1}{3}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n稍复杂的求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "淘气和笑笑帮助老师搬书他们两个各搬整堆书的一半10分钟后淘气完成了自己任务的 $\\frac{3}{4}$ 笑笑完成了自己任务的 $\\frac{2}{3}$ 淘气和笑笑分别搬了整堆书的几分之几因为他们各搬整堆书的一半也就是 $\\frac{1}{2}$ 淘气完成了自己任务的 $\\frac{3}{4}$ 那么淘气搬的是整堆书的 $\\frac{1}{2}\\times\\frac{3}{4}=\\frac{3}{8}$ 笑笑完成了自己任务的 $\\frac{2}{3}$ 那么笑笑搬的是整堆书的 $\\frac{1}{2}\\times\\frac{2}{3}=\\frac{1}{3}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数乘分数\n分数乘分数的实际应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "工地原有 $\\frac{9}{4}$ t水泥用了3天后还剩原来的 $\\frac{3}{5}$ 还剩多少吨列式( )A. $\\frac{9}{4}-\\frac{3}{5}$     B. $3\\times\\frac{3}{5}$     C. $\\frac{9}{4}\\times\\frac{3}{5}$    D. $\\frac{9}{4}\\times3$ 要求剩下水泥的吨数就是求 $\\frac{9}{4}$ t 的 $\\frac{3}{5}$ 是多少根据求一个数的几分之几是多少用乘法计算所以列式为 $\\frac{9}{4}\\times\\frac{3}{5}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数乘分数\n分数乘分数的实际应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "将这块木板锯成5段需要多少分钟将木板锯成2段需要 $\\frac{3}{5}$ 分钟即锯一次需要 $\\frac{3}{5}$ 分钟锯成5段需要锯4次因此总时间为 $4\\times\\frac{3}{5}=\\frac{12}{5}=2.4$ 分钟", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小华和小明骑自行车上学小华每分钟行 $\\frac{4}{15}$ 千米小明每小时行14千米他俩谁的速度快本题可先将两人的速度单位统一再比较两人速度的大小统一单位已知小华每分钟行 $\\frac{4}{15}$ 千米因为 $1$ 小时 $=60$ 分钟所以小华每小时行驶的距离为 $\\frac{4}{15}×60=16$ （千米）比较两人速度大小小华每小时行 $16$ 千米小明每小时行 $14$ 千米因为 $16＞14$ 所以小华的速度比小明快", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一个成年人一天大约需要 $\\frac{4}{5}$ 克的钙质一杯250毫升的鲜牛奶中含有的钙质约占一个成年人一天所需钙质的 $\\frac{3}{8}$ 这杯250毫升的鲜牛奶大约含有钙质多少克?本题可根据求一个数的几分之几是多少用乘法来计算这杯 $250$ 毫升鲜牛奶中含有的钙质已知一个成年人一天大约需要 $\\frac{4}{5}$ 克的钙质一杯 $250$ 毫升的鲜牛奶中含有的钙质约占一个成年人一天所需钙质的 $\\frac{3}{8}$ 那么这杯鲜牛奶含有的钙质为 $\\frac{4}{5}\\times\\frac{3}{8}=\\frac{3}{10}$ （克）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数乘分数\n分数乘分数的实际应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一种钢材每米重 $\\frac{8}{25}$ 千克现在有这种钢材125米共重多少千克要求125米钢材的重量因为每米重 $\\frac{8}{25}$ 千克也就是求125个 $\\frac{8}{25}$ 是多少用乘法计算列式为 $\\frac{8}{25}\\times125=8\\times5=40$ （千克）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数与整数的乘法\n分数乘整数"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "某果园果树种植面积为28.5公顷其中的 $\\frac{2}{5}$ 是苹果树的种植面积其他果树的种植面积是多少本题可先求出其他果树种植面积占总面积的比例再用总面积乘以该比例即可求出其他果树的种植面积计算其他果树种植面积占总面积的比例已知苹果树的种植面积占总面积的 $\\frac{2}{5}$ 将果园果树种植总面积看作单位“1”那么其他果树种植面积占总面积的比例为 $1-\\frac{2}{5}=\\frac{3}{5}$ 计算其他果树的种植面积已知果园果树种植面积为 $28.5$ 公顷其他果树种植面积占总面积的 $\\frac{3}{5}$ 根据“求一个数的几分之几是多少用乘法”可得其他果树的种植面积为 $28.5\\times\\frac{3}{5}=17.1$ （公顷）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n稍复杂的求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小红有零花钱30元如果小红取出她的零花钱的 $\\frac{1}{10}$ 给小东则两人的零花钱相等小东的零花钱有多少（请画图说明）本题可先求出小红给小东的零花钱数再根据两人零花钱的数量关系求出小东原有的零花钱数1. 计算小红给小东的零花钱数已知小红有零花钱 $30$ 元她取出零花钱的 $\\frac{1}{10}$ 给小东求一个数的几分之几是多少用乘法则小红给小东的零花钱为 $30\\times\\frac{1}{10}=3$ 元2. 分析小红给小东零花钱后两人零花钱的情况小红给小东 $3$ 元后两人零花钱相等此时小红的零花钱为 $30-3=27$ 元那么小东现在也有 $27$ 元3. 计算小东原来的零花钱数小东现在的零花钱 $27$ 元是在得到小红给的 $3$ 元后才有的所以小东原来的零花钱为 $27-3=24$ 元以下是画图说明首先画一条线段表示小红的零花钱 $30$ 元将其平均分成 $10$ 份取其中 $1$ 份（即 $3$ 元）给小东用虚线表示给小东的部分再画一条线段表示小东原来的零花钱在这条线段上加上小红给的 $3$ 元后和小红剩下的零花钱长度相等小红 ────────────────────── │───│───│───│ （给小东 $3$ 元） 剩下 $27$ 元 小东 ────────────── （原来的零花钱） 加上 $3$ 元后是 $27$ 元", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n稍复杂的求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "  $0.5\\times\\frac{4}{19}$        $4.5\\times\\frac{8}{27}$        $\\frac{5}{12}$ ×4.8       $\\frac{13}{21}$ ×0.14  $\\frac{7}{72}\\times0.72$       $\\frac{7}{16}\\times0.4$         $1.8\\times\\frac{5}{6}$        $3.3\\times\\frac{9}{22}$ 1. 计算 $0.5\\times\\frac{4}{19}$  - 先把 $0.5$ 化成分数 $\\frac{1}{2}$ 则 $\\frac{1}{2}\\times\\frac{4}{19}=\\frac{1\\times4}{2\\times19}=\\frac{2}{19}$ 2. 计算 $4.5\\times\\frac{8}{27}$  - 把 $4.5$ 化成分数 $\\frac{9}{2}$ 那么 $\\frac{9}{2}\\times\\frac{8}{27}=\\frac{9\\times8}{2\\times27}=\\frac{4}{3}$ 3. 计算 $\\frac{5}{12}\\times4.8$  - 把 $4.8$ 化成分数 $\\frac{24}{5}$ 所以 $\\frac{5}{12}\\times\\frac{24}{5}=\\frac{5\\times24}{12\\times5}=2$ 4. 计算 $\\frac{13}{21}\\times0.14$  - 把 $0.14$ 化成分数 $\\frac{14}{100}=\\frac{7}{50}$ 则 $\\frac{13}{21}\\times\\frac{7}{50}=\\frac{13\\times7}{21\\times50}=\\frac{13}{150}$ 5. 计算 $\\frac{7}{72}\\times0.72$  - 把 $0.72$ 化成分数 $\\frac{72}{100}$ 所以 $\\frac{7}{72}\\times\\frac{72}{100}=\\frac{7}{100}=0.07$ 6. 计算 $\\frac{7}{16}\\times0.4$  - 把 $0.4$ 化成分数 $\\frac{4}{10}=\\frac{2}{5}$ 则 $\\frac{7}{16}\\times\\frac{2}{5}=\\frac{7\\times2}{16\\times5}=\\frac{7}{40}$ 7. 计算 $1.8\\times\\frac{5}{6}$  - 把 $1.8$ 化成分数 $\\frac{9}{5}$ 那么 $\\frac{9}{5}\\times\\frac{5}{6}=\\frac{9\\times5}{5\\times6}=1.5$ 8. 计算 $3.3\\times\\frac{9}{22}$  - 把 $3.3$ 化成分数 $\\frac{33}{10}$ 所以 $\\frac{33}{10}\\times\\frac{9}{22}=\\frac{33\\times9}{10\\times22}=1.35$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n分数乘小数\n分数乘小数的计算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "过 $A、B$ 两点画一条直线根据题目要求需要过两点A和点B画一条直线在平面上任意两点可以确定一条唯一的直线因此连接点A和点B即可得到所求的直线", "output": "小学数学新知识树\n图形与几何\n平面图形\n直线、射线、线段\n根据长度画线段\n画线段、直线和射线"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "右下图是一张长方形纸折起来以后形成的图形已知 $∠1=25^{\\circ}$ 求 $∠2$ 的度数长方形的每个角都是 $90^{\\circ}$ 根据折叠的性质折叠后的角与原来的角相等已知 $∠1=25^{\\circ}$ 则折叠部分的角（即与 $∠1$ 重合的角）也是 $25^{\\circ}$ 因此 $∠1$ 和它重合的角加起来是 $25^{\\circ}+25^{\\circ}=50^{\\circ}$ 而 $∠1$ 、它重合的角和 $∠2$ 这三个角共同组成了长方形的一个角即 $90^{\\circ}$ 所以 $∠2=90^{\\circ}-50^{\\circ}=40^{\\circ}$ ", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n线段与角的综合\n角度直接运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "已知 $∠1=90^{\\circ},∠2=∠3$ 求 $∠2、∠3$ 的度数根据题目条件我们知道 $∠1=90^\\circ$ 且 $∠2=∠3$ 因为 $∠1$ 、 $∠2$ 和 $∠3$ 在同一条直线上它们的和为 $180^\\circ$ 因此我们可以得到 $∠1+∠2+∠3=180^\\circ$ 将 $∠1=90^\\circ$ 代入得到 $90^\\circ+∠2+∠3=180^\\circ$ 简化得到 $∠2+∠3=90^\\circ$ 又因为 $∠2=∠3$ 我们可以得到 $∠2=∠3=90^\\circ÷2=45^\\circ$ ", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n线段与角的综合\n角度直接运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先写出角的名称再把它们按从大到小的顺序排列___角 $>$ ___角 $>$ ___角 $>$ ___角 $>$ ___角根据图片中的角的度数可以判断每个角的类型1. 第一个角为90°是直角2. 第二个角为85°是锐角3. 第三个角为180°是平角4. 第四个角为150°是钝角5. 第五个角为360°是周角因此按从大到小的顺序排列为周角 $>$ 平角 $>$ 钝角 $>$ 直角 $>$ 锐角", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n角的分类及换算\n锐角、直角、钝角、平角和周角之间的大小关系"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "写出三角尺上各个角的度数第一张图片中的三角尺是一个含有30°、60°和90°角的直角三角尺因此∠1=30°∠2=60°∠3=90°第二张图片中的三角尺是一个等腰直角三角尺两个底角相等且为45°顶角为90°因此∠1=45°∠2=45°∠3=90°", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n用三角尺画角\n三角尺拼角"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "有（ ） 条直线有（ ） 条射线有（ ） 条线段根据题目中的图形分析1. 直线直线没有端点可以向两端无限延伸图中只有一条直线即通过点A、B、C的直线2. 射线射线有一个端点可以向一端无限延伸以每个点为端点可以得到射线具体为以A为端点的射线有2条（向左和向右）以B为端点的射线有2条（向左和向右）以C为端点的射线有2条（向左和向右）因此共有2+2+2=6条射线3. 线段线段有两个端点长度有限图中的线段有AB、BC、AC共3条线段因此答案分别为1条直线6条射线3条线段", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数线段、直线和射线"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "① 8 个百② 80 个十③ 8 个十④ 8 个一根据题目中的箭头指向608中的'8'是在个位但实际上在竖式计算中4位于个位2位于十位因此4×2实际上表示的是4个一乘以2个十结果为8个十答案选③", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的乘法\n三位数与两位数的进位乘法\n三位数乘两位数笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用竖式计算再用计算器验算 $\n15\\times201=\n$    $104\\times30=$    $305\\times23=$ 1. 计算15×201 - 先计算15×1 = 15再计算15×200 = 3000最后3000 + 15 = 30152. 计算104×30 - 先计算104×3 = 312再在积的末尾添上1个0得到31203. 计算305×23 - 先计算305×3 = 915再计算305×20 = 6100最后915 + 6100 = 7015用计算器验算时依次输入相应的数字和运算符号得出的结果应与上面计算的结果一致", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的乘法\n三位数与两位数的进位乘法\n三位数乘两位数笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先仔细观察因数的关系再计算 1. 对于第一张图片 - 4 × 25 = $4\\times25=100$ - 8 × 25 = $8\\times25=200$ - 16 × 25 = $16\\times25=400$ 2. 对于第二张图片 - 60 × 20 = $60\\times20=1200$ - 120 × 20 = $120\\times20=2400$ - 180 × 20 = $180\\times20=3600$", "output": "小学数学新知识树\n数与代数\n数的运算\n积的变化规律（整数乘法）\n一个因数变化的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一共行了多长的路叫作 ___ 每小时（或每分钟等）行的路程叫作___ 行了几小时（或几分钟等）叫作 ___在数学里一共走的路的长度就叫路程每小时或者每分钟等这样的单位时间里走的路程这就是速度而走了几小时或者几分钟等指的就是时间", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n行程问题\n普通行程问题\n速度、时间、路程之间的关系"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李芳家到公园有 1200 米她骑自行车每分钟行 240 米多少分钟可以到达这道题是已知 ___和 ___ 求 ___因为 ___ $\\bigcirc$ ___ $=$ ___ 所以这道题要用 ___法计算在行程问题里路程指一共走的距离速度是单位时间走的距离时间是行走用的时长题目里李芳家到公园的 1200 米是路程骑自行车每分钟行 240 米是速度问多少分钟到达就是求时间根据路程、速度和时间的关系路程÷速度 = 时间所以求时间要用除法计算", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n行程问题\n普通行程问题\n根据公式求时间"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一本童话书有 296 页 31 本童话书大约有 ___页本题可采用估算的方法来计算 $31$ 本童话书大约的页数在估算时我们可以把 $296$ 看作与它接近的整百数 $300$ 把 $31$ 看作与它接近的整十数 $30$ 然后根据乘法的意义求 $31$ 本童话书大约的页数就是求 $31$ 个 $296$ 大约是多少用乘法计算列式为 $296\\times31$ 估算时可转化为 $300\\times30=9000$ （页）所以 $31$ 本童话书大约有 $9000$ 页", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘两位数的估算\n三位数乘两位数的估算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据积的变化规律完成下表\n| 因数 | 18 | 36 | 18 | 18 |   | 因数 | 15 | 15 |   | 45 | 15 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 因数 | 18 | 36 | 18 | 18 |   |\n| 因数 | 15 | 15 |   | 45 | 15 |\n| 积 | 270 |   | 90 |   | 1350 |\n. 先看第二行已知 $18×15=270$ 现在一个因数从 $18$ 变为 $36$  $36÷18=2$ 即一个因数乘 $2$ 另一个因数 $15$ 不变根据积的变化规律一个因数不变另一个因数乘几积也乘几所以积 $270$ 也要乘 $2$  $270×2=540$ 2. 再看第三行已知 $18×15=270$ 现在积从 $270$ 变为 $90$  $270÷90=3$ 即积除以 $3$ 一个因数 $18$ 不变根据积的变化规律一个因数不变另一个因数除以几（ $0$ 除外）积也除以几所以另一个因数 $15$ 也要除以 $3$  $15÷3=5$ 3. 接着看第四行已知 $18×15=270$ 现在一个因数 $18$ 不变另一个因数从 $15$ 变为 $45$  $45÷15=3$ 即另一个因数乘 $3$ 根据积的变化规律一个因数不变另一个因数乘几积也乘几所以积 $270$ 也要乘 $3$  $270×3=810$ 4. 最后看第五行已知 $18×15=270$ 现在积从 $270$ 变为 $1350$  $1350÷15=2701$ 即积乘 $5$ 另一个因数 $15$ 不变根据积的变化规律一个因数不变另一个因数乘几积也乘几所以这个因数 $18$ 也要乘 $5$  $18×15=2702$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n积的变化规律（整数乘法）\n一个因数变化的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "点 $A$ 表示的数可能是算式（ ） 的积① $401\\times51$ ② $399\\times45$ ③ $399\\times51$ ④ $401\\times50$ 由图可知点A在靠近20000的位置并且点A表示的数字小于20000① 401 × 51 ≈ 400 × 50 = 20000和题意不符② 399 × 45 ≈ 400 × 45 = 18000符合题意③ 399 × 51 ≈ 400 × 50 = 20000和题意不符④ 401 × 50 ≈ 400 × 50 = 20000和题意不符所以点A表示的数可能是算式②的积", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘两位数的估算\n三位数乘两位数的估算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用竖式计算 $\n308\\times25=\n$       $\n27\\times235=\n$        $\n480\\times50=\n$ 1. 计算308×25 - 先用25个位上的5去乘3085×308 = 1540 - 再用25十位上的2去乘3082×308 = 616这里的616表示616个十即6160 - 最后把两次乘得的积相加1540 + 6160 = 77002. 计算27×235 - 先用27个位上的7去乘2357×235 = 1645 - 再用27十位上的2去乘2352×235 = 470这里的470表示470个十即4700 - 最后把两次乘得的积相加1645+4700 = 63453. 计算480×50 - 先计算48×5 = 240 - 因为两个因数末尾一共有2个0就在积240的末尾添上2个0结果是24000", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的乘法\n三位数与两位数的进位乘法\n三位数乘两位数笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "飞机每小时飞行 500 千米这是飞机飞行的 ___ 可以写成 ___ 读作 ___每小时飞行500千米这是飞机每小时飞行的路程也就是飞机飞行的速度速度的写法是把路程写在前面然后用斜线隔开后面写时间所以写成500千米/时读作就是按照从左到右的顺序读出来读作500千米每时", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n行程问题\n普通行程问题\n速度、时间、路程之间的关系"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用竖式计算 $\n29\\times130=\n$    $360\\times53=$    $12\\times305=$ 1. 计算29×130 - 先计算29×13按照两位数乘两位数的竖式计算方法9×13 = 11720×13 = 260117 + 260 = 377 - 因为130末尾有1个0所以在377后面添上1个0结果是37702. 计算360×53 - 先计算36×5336×3 = 10836×50 = 1800108+1800 = 1908 - 因为360末尾有1个0所以在1908后面添上1个0结果是190803. 计算12×305 - 用12分别去乘305的每一位12×5 = 6012×300 = 360060 + 3600 = 3660", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的乘法\n三位数与两位数的进位乘法\n三位数乘两位数笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "算一算           1. 第一个算式 $490\\times20=9800$ 2. 第二个算式 $108\\times30=3240$ 3. 第三个算式 $270\\times34=9180$ 4. 第四个算式 $33\\times230=7590$", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的乘法\n三位数与两位数的进位乘法\n三位数乘两位数笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "龙眼是漳州市的特色水果李叔叔开车从宁德去漳州采购龙眼（1） 2 小时后距离两地的中点还有 40 千米请在下面的线段图中用＂ $\\triangle$ ＂标出此时汽车所在的位置（2）李叔叔要采购 3 吨龙眼已经买了 150 箱龙眼每箱 15 千克还要再采购多少千克（3）返回时汽车每小时行 75 千米 3 小时后超过两地中点 40 千米①在上面的线段图中用标出此时汽车所在的位置②汽车从漳州到宁德一共要行驶多少千米（1）首先根据题目描述2小时后距离两地的中点还有40千米这意味着汽车此时位于中点前40千米的位置因此在线段图中从宁德到漳州的中点位置向前40千米处标记“ $\\triangle$ ”（2）李叔叔要采购3吨龙眼即3000千克已经买了150箱每箱15千克因此已经采购的龙眼重量为 $150\\times15=2250$ 千克还需要采购的龙眼重量为 $3000-2250=750$ 千克（3）①返回时汽车每小时行75千米3小时后超过两地中点40千米即在中点后40千米处因此在线段图中从宁德到漳州的中点位置向后40千米处标记“ $\\P$ ”②已知返回时汽车速度为每小时75千米行驶3小时根据“路程=速度×时间”可得行驶的路程为75×3=225千米因为此时超过中点40千米所以到中点的距离是225-40=185千米那么漳州到宁德的总距离为185×2=370千米", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n行程问题\n普通行程问题\n根据公式求路程"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "按照下图的方式摆第 5 个图形需要多少个小正方体观察图形最上面一层规定为第一层从上到下数出各个图形所有的小正方体数量第一个图形只有一层1个小正方体第二个图形有两层第一层有1个小正方体第二层比第一层多2个小正方体有3个小正方体共有( 1+3 )个第三个图形有三层第一层有1个小正方体第二层比第一层多2个小正方体第三层比第二层多3个小正方体共有( 1+3+6 )个依次类推第5个图形有五层第一层有1个小正方体第二层比第一层多2个小正方体第三层比第二层多3个小正方体第四层比第三层多4个小正方体第五层比第四层多5个小正方体即第一层有1个第二层有1+2=3（个）第三层有3+3=6（个）第四层有6+4=10（个）第五层有10+5=15（个）全部加起来1+3+6+10+15=35个是需要的小正方体的总数", "output": "小学数学新知识树\n数学竞赛\n计数\n立体图形计数\n组合图形数小正方体数量的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "___的四边形叫作梯形梯形的定义是只有一组对边平行的四边形叫作梯形", "output": "小学数学新知识树\n图形与几何\n平面图形\n梯形的特征及分类\n梯形的概念及特点\n梯形的概念"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "长方形相邻的两条边互相 ___ 相对的两条边互相 ___梯形的一组对边 ___ 另一组对边 ___在长方形中相邻的两条边所成的角是直角根据垂直的定义当两条直线相交成直角时这两条直线互相垂直所以长方形相邻的两条边互相垂直长方形相对的两条边无论怎么延长都不会相交根据平行的定义在同一平面内不相交的两条直线互相平行所以长方形相对的两条边互相平行梯形的定义是只有一组对边平行的四边形所以梯形有一组对边是平行的而另一组对边不平行", "output": "小学数学新知识树\n图形与几何\n平面图形\n梯形的特征及分类\n梯形的概念及特点\n梯形的概念"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": " ___统计图能清楚、直观地看出各种数量的多少便于比较条形统计图是用直条的长短来表示数量的多少这样我们一眼就能清楚、直观地看出各种数量的多少而且很便于对不同数量进行比较", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示一个单位的单式条形统计图\n以一当一的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据统计图回答问题5 个同学 3 个月看书情况统计图（1） ___看的书最多有 ___本 ___看的书最少有 ___本（2）看书最多的同学比最少的多看 ___本书（3） 5 个同学 3 个月一共看了 ___本书（1）根据统计图小敏看的书最多有 20 本小芳看的书最少有 10 本（2）看书最多的同学（小敏）比最少的（小芳）多看的书为 $20-10=10$ 本（3）5 个同学 3 个月一共看的书为 $12+20+10+18+16=76$ 本", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示多个单位的单式条形统计图\n以一当二的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下图是某一物体在不同时刻影子长度的统计图下午130这个物体影子长度大约是（ ） 厘米① 80② 135③ 150④ 160根据统计图13时（下午1时）的影子长度为80厘米14时（下午2时）的影子长度为160厘米下午130位于13时和14时之间影子长度应介于80厘米和160厘米之间因此下午130时这个物体影子长度大约是135厘米选②", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示多个单位的单式条形统计图\n以一当多的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面是四（2）班六个小组的同学创作的手抄报数量统计表\n|   |   |   |   |   |   |   |\n| --- | --- | --- | --- | --- | --- | --- |\n| 小组 | 一 | 二 | 三 | 四 | 五 | 六 |\n| 数量／份 | 3 | 5 | 7 | 6 | 4 | 5 |\n（1）用条形图把统计结果表示出来（2）第 ___小组的同学创作的手抄报数量最多（3）第 ___小组的同学创作的手抄报数量最少（4）平均每个小组创作多少份手抄报（1）根据统计表中的数据可以绘制条形图如下（2）观察条形图第三小组的手抄报数量最多为7份（3）观察条形图第一小组的手抄报数量最少为3份（4）平均每个小组创作的手抄报数量计算如下总数量 = 3 + 5 + 7 + 6 + 4 + 5 = 30份平均数量 = 30 ÷ 6 = 5（份）", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示一个单位的单式条形统计图\n以一当一的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "由右边的统计图可知 $a$ 的值大约是（ ） ① 10② 14③ 16④ 24由图知D＞B且D＜B+C则a＞10a＜10+6即10＜a＜16a的值大约是14故选②", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示多个单位的单式条形统计图\n以一当多的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据小猫钓鱼的统计表回答问题（1）把上面的数据用条形统计图表示出来（2） 4 只猫一共钓了 ___条鱼（3）比多钓 ___条鱼 的鱼比的鱼少 ___条（1）根据题目提供的数据可以得到每只猫钓鱼的数量分别为7条、5条、3条、6条（2）4只猫一共钓鱼的数量为 $7+5+3+6=21$ 条（3）第一只猫比第二只猫多钓的鱼数量为 $7-5=2$ 条第三只猫的鱼比第四只猫的鱼少的数量为 $6-3=3$ 条", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示一个单位的单式条形统计图\n以一当一的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面是四（1）班同学订报刊的人数统计表（1）用条形图把统计结果表示出来\n（2）每格代表 ___人\n（3）订《 ___》的同学最多\n（4）订阅《小学生报》和《读写天地》的共有 ___人（5）订阅《读写天地》和《小主人报》的共有 ___人（1）根据统计表中的数据可以画出条形统计图如下（2）观察条形图每格代表2人（3）比较各报刊的订阅人数订《中国儿童报》的同学最多为14人（4）订阅《小学生报》和《读写天地》的共有12 + 12 = 24人（5）订阅《读写天地》和《小主人报》的共有12 + 10 = 22人", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示多个单位的单式条形统计图\n以一当二的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面是四（2）班同学最喜欢的早餐统计表\n| 最喜欢的早餐 | 牛奶 | 豆浆 | 包子 | 人数 | 6 | 12 | 20 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 最喜欢的早餐 | 牛奶 | 豆浆 | 包子 |\n| 人数 | 6 | 12 | 20 |\n如果要制成条形统计图那么每格表示（ ） 人比较合适① 1② 2③ 5④ 10分析数据特点已知喜欢牛奶的有6人喜欢豆浆的有12人喜欢包子的有20人这些数据都是2的倍数（6÷2=312÷2=620÷2=10）选项①若每格表示1人绘制条形统计图时直条会比较长 虽然能表示数据但不够简洁选项②因为6、12、20都是2的倍数每格表示2人6÷2=3格12÷2=6格20÷2=10格 这样绘制的条形统计图简洁且能清晰表示数据选项③若每格表示5人6÷5=1……1不能完整表示6人的数据不合适选项④若每格表示10人6÷10=0.1无法准确表示数据不合适综上每格表示2人比较合适答案选②", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示多个单位的单式条形统计图\n以一当多的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据下列条形统计图填一填 纵轴1格表示 ___  纵轴1格表示 ___  纵轴1格表示 ___  纵轴1格表示 ___ 直条表示 ___ 直条表示 ___ 直条表示 ___ 直条表示 ___第一张图中纵轴从0到10分为2格因此每格表示 $10÷2=5$ 直条高度为8格表示 $5\\times8=40$ 第二张图中纵轴从0到20分为2格因此每格表示 $20÷2=10$ 直条高度为3格表示 $3\\times10=30$ 第三张图中纵轴从0到4分为2格因此每格表示 $4÷2=2$ 直条高度为5格表示 $2\\times5=10$ 第四张图中纵轴从0到50分为2格因此每格表示 $50÷2=25$ 直条高度为6格表示 $25\\times6=150$ ", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示多个单位的单式条形统计图\n以一当多的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "张师傅 3 小时可以做 27 个零件照这个速度 5 小时他能做多少个零件本题可先根据已知条件算出张师傅每小时做零件的个数再计算5小时做零件的个数已知张师傅3小时可以做27个零件要求每小时做零件的个数就是把27平均分成3份求每份是多少用除法计算可得 $27\\div3=9$ （个）即张师傅每小时能做9个零件那么5小时做零件的个数就是5个9相加用乘法计算可得 $9\\times5=45$ （个）列综合算式是27÷3×5=9×5=45（个）综上5小时他能做45个零件", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息看图列式计算2、请回答（2）根据图片可以得到以下信息1. 前四个部分每部分长度为7即 $7×4=28$ 2. 第五个部分长度为16因此总长度为 $28+16=44$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n根据分步式列综合式\n看图列综合算式并计算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "毛毛在计算＂ $18+\\square\\div9$ ＂时弄错了运算顺序先算加法后算除法了结果得数是 8 正确的得数应该是多少本题可先根据错误的运算顺序求出□里的数再按照正确的运算顺序计算出结果已知毛毛弄错了运算顺序先算加法后算除法即 $( 18+\\square )\\div9=8$ 根据“被除数 $=$ 除数 $\\times$ 商”可得 $18+\\square=8\\times9=72$ 再根据“加数 $=$ 和 $-$ 另一个加数”可得 $\\square=72-18=54$ 将 $\\square=54$ 代入原式 $18+\\square\\div9$ 得到 $18+54\\div9$ 根据数学运算顺序先算除法再算加法即 $18+54\\div9=18+6=24$ ", "output": "小学数学新知识树\n数学竞赛\n计算\n错中求解\n错中求解(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息看图列式计算2、请回答（1）根据图片女生有28人女生比男生多4人因此男生有 $28-4=24$ 人男生和女生总共有 $28+24=52$ 人", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n根据分步式列综合式\n看图列综合算式并计算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据下表列出相应的算式并计算\n| 乘数 | 乘数 | 综合算式 | 积 |   | 9 |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 乘数 | 乘数 | 综合算式 | 积 |\n|   | 9 |   |   |\n| 6 |   |   |   |\n根据表格第一个乘数为9第二个乘数为6因此综合算式为9×6计算得到积为54", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n根据分步式列综合式\n根据分步运算列综合算式(含括号)(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "爸爸今年几岁根据题目当亮亮9岁时爸爸38岁因此爸爸比亮亮大 $38-9=29$ 岁现在亮亮3岁所以爸爸的年龄为 $3+29=32$ 岁", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先判一判对的在括号里画＂ $\\sqrt{}$ ＂错的在括号里画＂ $\\times$ ＂再订正（1） 54-22+8 订正 （2） 12 ÷2×2 订正=54-30                                                                =12÷4=24（        ）            =3 （        ）1. 对于式子 $54-22+8$       在数学运算中加减法属于同级运算要按照从左到右的顺序依次计算      原答案先计算了 $22+8=30$ 再用 $54-30=24$ 这不符合运算顺序      正确的计算是先算 $54-22=32$ 再算 $32+8=40$ 2. 对于式子 $12\\div2\\times2$       乘除法也属于同级运算同样要按照从左到右的顺序依次计算      原答案先计算了 $2\\times2=4$ 再用 $12\\div54-22+80$ 不符合运算顺序      正确的计算是先算 $12\\div54-22+81$ 再算 $6\\times54-22+82$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n运算定律与简便运算\n无括号的运算顺序\n同级运算的运算顺序"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小明在计算＂ $\\square-25+15$ ＂时先算 $25+15=40$ 再算 $\\square-40$ 结果与实际相比相差多少本题可先分别求出正确结果和错误结果再计算二者的差值计算正确结果按照正确的运算顺序从左到右依次计算□ - 25 + 15可得□ - 25 + 15 = □ - 10计算错误结果小明先算25 + 15 = 40再算□ - 40所以错误结果为□ - 40计算结果差值用正确结果减去错误结果可得( □ - 10 ) - ( □ - 40 )=□ - 10 - □ + 40=30所以结果与实际相比相差30", "output": "小学数学新知识树\n数学竞赛\n计算\n错中求解\n错中求解(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息看图列式解答2、请回答（1）根据题目中的图片初始金额为50元先花费12元再花费18元因此剩余金额可以通过以下计算得到50 - 12 - 18 = 20元所以剩余金额为20元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n根据分步式列综合式\n看图列综合算式并计算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息商场开展促销活动每满 60 元减 12 元2、请回答（1）英英买一个铅笔盒、一盒铅笔、一本日记本50元够吗英英买一个铅笔盒、一盒铅笔、一本日记本的总价为23元 + 15元 + 18元 = 56元虽然总价为56元但未满60元不能享受每满60元减12元的优惠因此需要支付56元50元不够", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n优惠策略"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数学游戏＂ 24 点＂是一种数学游戏下图为抽取的 4 张牌请利用牌面上的数字和加、减、乘、除四种运算符（可加括号）把这 4 个数算成 24 每张牌必须使用一次且只能使用一次试着列式计算本题可以从等于24的一些算式入手如3×8=24,则组成的算式是4-1=36+2=83×8=24列综合算式是( 4 - 1 ) × ( 6 + 2 ) = 24", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n带有小括号的混合运算\n含有小括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "脱式计算（48-18）÷6 48-18÷6 72-（45-19） 9×（72÷8）1. 计算（48 - 18）÷6 - 先算括号里的48 - 18 = 30 - 再算30÷6 = 52. 计算48 - 18÷6 - 先算除法18÷6 = 3 - 再算减法48 - 3 = 453. 计算72 - （48-180） - 先算括号里的48-180 = 26 - 再算48-1824. 计算9×（72÷8） - 先算括号里的72÷48-183 - 再算9×48-184", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n带有小括号的混合运算\n含有小括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息商场开展促销活动每满 60 元减 12 元2、请回答（2）亮亮带 100 元买了一个削笔刀、一个铅笔盒和一本日记本还剩多少钱亮亮购买的物品总价为削笔刀 35 元 + 铅笔盒 23 元 + 日记本 18 元 = 76 元根据促销活动每满 60 元减 12 元因此可以减 12 元实际支付金额为76 元 - 12 元 = 64 元亮亮带了 100 元因此还剩100 元 - 64 元 = 36 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n优惠策略"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面算式中的图形代表多少（1） $10\\div\\triangle+5=7,\\triangle=( \\quad )$ （2） $38-\\square\\times8=6,\\square=( \\quad )$ （1）对于等式 $10\\div\\triangle+5=7$ 我们可以把 $10\\div\\triangle$ 看成一个整体因为一个加数+另一个加数 $=$ 和那么 $10\\div\\triangle$ 就等于和 $7$ 减去另一个加数 $5$ 即 $10\\div\\triangle=7-5=2$ 根据被除数÷除数=商那么除数 $\\triangle$ 就等于被除数 $10$ 除以商 $2$ 即 $\\triangle=10\\div2=5$ （2） 对于等式 $38-\\square\\times8=6$ 把 $\\square\\times8$ 看成一个整体因为被减数-减数=差那么 $\\square\\times8$ 就等于被减数 $38$ 减去差 $6$ 即 $\\square\\times8=38-6=32$ 根据因数 $\\times$ 因数 $=$ 积那么一个因数□就等于积 $32$ 除以另一个因数 $8$ 即 $\\square=32\\div8=4$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n不带括号的混合运算\n不含括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一本书 78 页还剩多少页没有看第六天从第几页开始看每天看8页看了5天共看了 $8\\times5=40$ 页这本书共有78页还剩 $78-40=38$ 页没有看第六天从第41页开始看", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n乘加、乘减混合运算\n乘加乘减应用题(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "请在适当的位置加上小括号使等式成立4＋3×9=63 20-12÷4=2 24÷3+1=61. 对于式子4＋3×9=63 - 按照原来的运算顺序是先算乘法3×9 = 27再算加法4 + 27 = 31结果不等于63 - 我们想要结果是63因为9乘7等于63所以把4 + 3用小括号括起来先算小括号里的4 + 3 = 7再算7×9=630等式就成立了2. 对于式子9=631÷4=2 - 原来的运算顺序是先算除法12÷9=633再算减法9=634结果不等于2 - 我们想要结果是2因为8除以4等于2所以把9=631用小括号括起来先算小括号里的9=631 = 8再算8÷9=637等式成立3. 对于式子24÷9=638 - 原来的运算顺序是先算除法24÷9=639再算加法20-120结果不等于6 - 我们想要结果是6因为24除以4等于6所以把20-121用小括号括起来先算小括号里的20-121 = 4再算24÷20-123等式成立", "output": "小学数学新知识树\n数与代数\n数的运算\n运算定律与简便运算\n有括号的运算顺序\n加小括号的综合问题(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "想一想每道题先算什么再计算 $57-36\\div9$ $4+5\\times4$ $31+7\\times7$ $50-30\\div5$ 1. 对于 $57-36\\div9$  - 根据数学运算顺序先算除法再算减法 - 先计算 $36\\div9=4$ 再计算 $57-4=53$ 2. 对于 $4+5\\times4$  - 先算乘法再算加法 - 先计算 $5\\times4=20$ 再计算 $57-360$ 3. 对于 $31+7\\times7$  - 先算乘法再算加法 - 先计算 $7\\times57-362$ 再计算 $57-363$ 4. 对于 $50-30\\div5$  - 先算除法再算减法 - 先计算 $30\\div57-365$ 再计算 $57-366$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n运算定律与简便运算\n无括号的运算顺序\n含有两级运算的运算顺序"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "国庆节到了超市开展优惠促销活动彩笔原价 12 元一盒（1）促销的彩笔一盒多少钱（2）每盒比原来便宜多少钱（1）促销时买2盒送1盒即3盒只需要支付2盒的钱因此3盒彩笔的总价为 $12\\times2=24$ 元每盒彩笔的促销价为 $24\\div3=8$ 元（2）原价为12元一盒促销价为8元一盒因此每盒比原来便宜 $12-8=4$ 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n优惠策略"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先填一填再列式算一算在没有括号的算式里只有加、减法或只有乘、除法从（        ） 往（        ） 按顺序计算分析第一个综合算式1. $25+14=39$ 2. $39-6=33$ 因此综合算式为 $25+14-6=39-6=33$ 分析第二个综合算式1. $6\\times4=24$ 2. $24\\div8=3$ 因此综合算式为 $6\\times4\\div8=24\\div8=3$ 在没有括号的算式里只有加、减法或只有乘、除法从左往右按顺序计算", "output": "小学数学新知识树\n数与代数\n数的运算\n运算定律与简便运算\n无括号的运算顺序\n同级运算的运算顺序"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "海海把＂ $\\square-12+8$ ＂计算成＂ $\\square-( 12+8 )$ ＂结果和实际相比相差多少请算一算首先按照正确的运算顺序 $\\square-12+8$ 就是先减12再加8而海海算成了 $\\square-( 12+8 )=\\square-12-8$ 也就是先减12再减8我们来比较这两个式子正确结果是加8错误结果是减8那么结果相差就是 $8+8=16$ ", "output": "小学数学新知识树\n数学竞赛\n计算\n错中求解\n错中求解(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先填一填再列式算一算在没有括号的算式里如果既有乘、除法又有加、减法要先算（        ） 法再算（        ）法1. 左边的算式4 × 9 = 36然后 27 + 36 = 63因此综合算式为 27 + 4 × 9 = 632. 右边的算式48 ÷ 6 = 8然后 98 - 8 = 90因此综合算式为 98 - 48 ÷ 6 = 903. 在没有括号的算式里如果既有乘、除法又有加、减法要先算乘、除法再算加、减法", "output": "小学数学新知识树\n数与代数\n数的运算\n运算定律与简便运算\n无括号的运算顺序\n含有两级运算的运算顺序"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一条彩带对折 2 次后每段长 6 分米如果对折 3 次每段长多少分米对折 1 次彩带被平均分成 2 段对折 2 次彩带被平均分成 $2×2=4$ 段对折 3 次彩带被平均分成 $2×2×2=8$ 段已知对折 2 次后每段长 6 分米因为对折 2 次后彩带被分成 4 段所以彩带总长度为 $6×4=24$ 分米对折 3 次后彩带被分成 8 段那么每段长度是 $24÷8=3$ 分米", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息如果每 8 根胡萝卜装一篮一共装几篮2、请回答（2）列式解答首先计算总共有多少根胡萝卜 $12+28=40$ 根然后根据每8根装一篮计算可以装几篮 $40\\div8=5$ 篮因此一共装5篮", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n除加、除减混合运算\n除加除减应用题(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "7 的4 倍是（        ） （        ） 的5 倍是 4052 比（        ） 少 121. 求 7 的 4 倍是多少根据乘法的意义求一个数的几倍是多少用乘法计算所以 7 的 4 倍就是 7×4 = 282. 已知一个数的 5 倍是 40求这个数根据除法的意义已知一个数的几倍是多少求这个数用除法计算所以这个数是 40÷5 = 83. 求 52 比哪个数少 1252 比这个数少 12说明这个数比 52 大 12求比一个数大几的数是多少用加法计算所以这个数是 52 + 12 = 64", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n不带括号的混合运算\n不含括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息根据题意将线段图补充完整并列式解答2、请回答（1）小雅计划折 99 只千纸鹤已经折了 50 只剩下的如果要在 7 天折完平均每天需要折几只根据题目小雅计划折 99 只千纸鹤已经折了 50 只因此剩下的千纸鹤数量为 $99-50=49$ 只剩下的 49 只需要在 7 天内折完因此平均每天需要折的千纸鹤数量为 $49\\div7=7$ 只", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n除加、除减混合运算\n除加除减应用题(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "将导图填写完整并列式解答（1）首先计算3天内修了多少千米每天修6千米修了3天因此修了 $6\\times3=18$ 千米水渠总长20千米已经修了18千米因此还剩 $20-18=2$ 千米（2）首先计算玫瑰花和康乃馨共有多少朵玫瑰花15朵康乃馨12朵因此共有 $15+12=27$ 朵每束9朵因此可以扎 $27\\div9=3$ 束", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n根据分步式列综合式\n根据分步运算列综合算式(含括号)(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "要使算式 $( \\square-3 )\\times6=36$ 成立 里应填 （        ） A． 9B． 8C． 6要使算式 $( \\square-3 )\\times6=36$ 成立首先将等式两边同时除以6得到 $\\square-3=6$ 然后将等式两边同时加上3得到 $\\square=9$ 因此□里应填 9", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n带有小括号的混合运算\n含有小括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面不可以用 $4\\times3+2$ 解决的是（        ）A.B.C.选项A中有4个三角形每个3根小棒再加上2根小棒可以用 $4\\times3+2$ 来表示选项B中有3个正方形每个4根小棒再加上2根小棒也可以用 $4\\times3+2$ 来表示选项C中有2个正方形每个4根小棒再加上1个三角形每个3根小棒用4×2+3来表示综上不可以用 $4\\times3+2$ 解决的是选项C", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n乘加、乘减混合运算\n乘加乘减应用题(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "我会算56-35÷7 （24+16）÷8 72÷9×3 5+4×91. 计算56 - 35÷7 - 先算除法35÷7 = 5 - 再算减法56 - 5 = 512. 计算（24 + 16）÷8 - 先算括号里的加法24 + 16 = 40 - 再算除法40÷8 = 53. 计算72÷9×3 - 按照从左到右的顺序先算除法72÷56-350 - 再算乘法8×56-3514. 计算56-352×9 - 先算乘法4×56-353 - 再算加法56-354", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n带有小括号的混合运算\n含有小括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一道综合算式先算 $32-18=14$ 再算 $14\\div7=2$ 它是（                    ） 题目先算32 - 18再用所得的差除以7所以要把32 - 18用括号括起来这个综合算式就是( 32 - 18 )÷7=2", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n根据分步式列综合式\n根据分步运算列综合算式(含括号)(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息面包店每天晚上 9 点就开始搞促销活动2、请回答（1）李叔叔带了 50 元买 3 个火腿肠面包需付多少钱首先计算3个火腿肠面包的总价 $3\\times8=24$ 元因为总价为24元未满30元所以不享受每满30元减8元的优惠因此李叔叔需付24元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n优惠策略"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息如果每 8 根胡萝卜装一篮一共装几篮2、请回答（1）根据题意将线段图填写完整 根据题目信息小白兔拔了12根胡萝卜小灰兔拔了28根胡萝卜因此线段图中应填写小白兔拔的为12根小灰兔拔的为28根", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据右下图亮亮列出的算式为 $8\\times4-22$ 那么他解决的问题是（        ） A． 1 盒水彩笔比 1 本练习本贵多少钱B．买 1 盒水彩笔比买 4 本练习本多花多少钱C．买 1 盒水彩笔比买 4 本练习本少花多少钱根据题目中的算式 $8\\times4-22$ 我们可以进行如下分析1. $8\\times4$ 表示买 4 本练习本的总费用即 $8\\times4=32$ 元2. $32-22$ 表示买 4 本练习本的费用与买 1 盒水彩笔的费用之差即 $32-22=10$ 元因此算式 $8\\times4-22$ 解决的问题是“买 1 盒水彩笔比买 4 本练习本少花多少钱”故正确答案为 C", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息面包店每天晚上 9 点就开始搞促销活动2、请回答（2）张阿姨买了 4 个牛角包和 2 个火腿肠面包需付多少钱张阿姨买了4个牛角包和2个火腿肠面包每个牛角包4元4个牛角包的总价为 $4\\times4=16$ 元每个火腿肠面包8元2个火腿肠面包的总价为 $8\\times2=16$ 元因此张阿姨购买的总价为 $16+16=32$ 元根据促销活动每满30元减8元张阿姨的总价为32元满足满30元的条件可以减8元所以张阿姨实际需付 $32-8=24$ 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n优惠策略"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如果 $\\square\\div3+5=10$ 则 $\\square=( \\quad )$ 如果 $20-\\triangle\\times3=8$ 则 $\\triangle=( \\quad )$ 本题可根据等式的性质分别求出□和 $\\triangle$ 的值步骤一求解□的值已知 $\\square\\div3+5=10$ 根据等式的性质等式两边同时减去同一个数等式仍然成立所以在等式两边同时减去 $5$ 可得 $\\square\\div3+5-5=10-5$ 即 $\\square\\div3=5$ 再根据等式的性质等式两边同时乘同一个数等式仍然成立在等式两边同时乘 $3$ 可得 $\\square\\div3\\times3=5\\times3$ 即 $\\square=15$ 步骤二求解 $\\triangle$ 的值已知 $20-\\triangle\\times3=8$ 根据等式的性质等式两边同时加上同一个数等式仍然成立在等式两边同时加上 $\\triangle\\times3$ 可得 $20-\\triangle\\times3+\\triangle\\times3=8+\\triangle\\times3$ 即 $20=8+\\triangle\\times3$ 然后等式两边同时减去 $8$ 可得 $3+5=100+\\triangle\\times3+5=101$ 即 $12=\\triangle\\times3$ 最后根据等式的性质等式两边同时除以同一个不为 $0$ 的数等式仍然成立在等式两边同时除以 $3$ 可得 $12\\div3=\\triangle\\times3\\div3$ 即 $\\triangle=4$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n不带括号的混合运算\n不含括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息杨老师带假日小队 6 名小朋友去游乐场用 50 元买门票够吗2、请回答（2）请从正确的方法中选择一种说明理由要判断 50 元够不够买门票我们要知道买门票总共花多少钱把老师和小朋友买门票的钱加起来得到总花费然后和 50 元比大小就能知道 50 元够不够", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n够不够问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息杨老师带假日小队 6 名小朋友去游乐场用 50 元买门票够吗2、请回答（1）谁的计算方法正确就在谁后面的括号里画＂ $\\sqrt{}$ ＂月月 $6\\times8=48$ （元） 50 元 $>48$ 元用 50 元买门票够（        ）欣欣 $6\\times8+15=63$ （元） 50 元 $2$ 元用 50 元买门票不够（        ）首先我们需要计算杨老师和6名小朋友去游乐场所需的门票总费用杨老师是成人需要购买成人票成人票价格为15元6名小朋友需要购买儿童票儿童票价格为8元因此6名小朋友的门票费用为 $6\\times8=48$ 元因此门票总费用为 $15+48=63$ 元比较总费用63元与50元可以得到50元不够支付63元的门票费用因此月月的计算方法不正确欣欣的计算方法正确除了先算出总费用再与50元作比较还可以用50元减去6名小朋友的门票费用求出剩余的钱数再与成人票价作比较如果大于成人票价说明够买一张成人票那么带的钱够如果小于成人票价说明不够买一张成人票那么带的钱不够因此亮亮的计算方法也是正确的", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n够不够问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "红红在计算＂ $24-\\square\\div4$ ＂时弄错了运算顺序先算减法再算除法结果得数是 3 正确的得数应该是多少本题可先根据错误的运算顺序求出 $□$ 里的数再按照正确的运算顺序计算出结果步骤一根据错误的运算顺序求出 $□$ 里的数红红弄错了运算顺序先算减法再算除法即 $( 24-□ )÷4=3$ 根据“被除数 $=$ 除数 $\\times$ 商”可得 $24-□=3\\times4=12$ 再根据“减数 $=$ 被减数 $-$ 差”可得 $□=24-12=12$ 步骤二按照正确的运算顺序计算结果将 $□=12$ 代入原式 $24-□÷4$ 得到 $24-12÷4$ 根据数学运算顺序先算除法再算减法即 $12\\div4=3$  $24-3=21$ ", "output": "小学数学新知识树\n数学竞赛\n计算\n错中求解\n错中求解(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在同一平面内两条直线都和第三条直线垂直那么这两条直线（ ） ① 相交② 互相平行③ 互相垂直④ 都有可能", "output": "小学数学新知识树\n图形与几何\n平面图形\n平行与垂直的特征、性质\n平行的特征及性质\n关于平行的相关判断"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数一数各有几组平行线 第一张图中有三条平行线因此有3组平行线第二张图是一个平行四边形有两组平行线第三张图是一个梯形只有一组平行线", "output": "小学数学新知识树\n图形与几何\n平面图形\n平行与垂直的特征、性质\n平行的特征及性质\n关于平行的相关判断"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "杨明在游泳池的 $A$ 处游泳现在他想尽快游上岸你能帮他设计一条最近的路线吗请在下图中画出来根据垂直线段最短的原理从点A向游泳池的岸边作垂线垂足为点B连接AB即为杨明尽快游上岸的最近路线", "output": "小学数学新知识树\n图形与几何\n平面图形\n平行与垂直的特征、性质\n过直线上或直线外一点作垂线\n点到直线的距离应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "四年级有 253 人去参观博物馆每辆车限乘 40 人一共需要多少辆车本题可根据总人数和每辆车限乘人数求出需要车的数量用总人数除以每辆车限乘的人数即 $253\\div40=6$ （辆） $\\cdots\\cdots13$ （人）这意味着坐满 $6$ 辆车后还剩下 $13$ 人剩下的 $13$ 人也需要 $1$ 辆车所以总共需要 $6+1=7$ 辆车", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "解决问题杨明带了 180 元去买衣服一件衬衫 60 元一条裤子 50 元（1）如果买衬衫最多可以买几件（2）如果买裤子最多可以买几条还剩多少钱（1）已知杨明带了180元一件衬衫60元要求买衬衫最多可买几件就是求180里有几个60用除法计算180÷60 = 3（件）（2）已知杨明带了180元一条裤子50元要求买裤子最多可买几条还剩多少钱就是求180里有几个50用除法计算180÷50 = 3（条）……30（元）所以最多可以买3条裤子还剩30元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "王村和李村各修一条水泥路通到公路怎么修最合理试着画一画我们知道两点之间垂直线段最短因此为了使王村和李村到公路的距离最短应该从王村和李村分别向公路作垂线这样垂足与王村、李村之间的线段就是最短路径即为最合理的修路方案", "output": "小学数学新知识树\n图形与几何\n平面图形\n平行与垂直的特征、性质\n过直线上或直线外一点作垂线\n点到直线的距离应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小猴摘了 258 个桃子每 30 个桃子装一筐这些桃子可以装满几筐至少再摘几个桃子就可以再装满一筐本题可根据除法运算求出能装满的筐数再根据余数求出再装满一筐还需的桃子数已知共有 258 个桃子每 30 个桃子装一筐求 258 里有几个 30用除法计算即 $258\\div30=8$ （筐） $\\cdots\\cdots18$ （个）这意味着可以装满 8 筐还剩 18 个桃子每筐要装 30 个桃子已经装了 18 个那么还需要 $30-18=12$ （个）桃子就可以再装满一筐", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "王奶奶沿着河边围了一块梯形菜地（菜地的一边靠着河岸且河岸足够长）菜地的上底长 6 米下底长 11 米两腰各是 8 米她共用了 22 米长的篱笆你知道她是怎么围的吗请画一画把菜地的下底靠着小河就可以省下下底11米长的篱笆这样仅需要上底6米、两腰各8米的篱笆共22米", "output": "小学数学新知识树\n图形与几何\n平面图形\n梯形的特征及分类\n梯形的周长\n等腰梯形周长相关的计算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "计算 $426\\div56$ 时可以把除数看作 ___或 ___来试商在计算除法试商时为了方便计算可以把除数看成与之接近的整十数或者方便计算的数56接近55也接近60所以计算 $426\\div56$ 时可以把除数看作55或60来试商", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n多位数除以两位数的试商\n除数不接近整十数的试商"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "计算 $187\\div24$ 时可以把除数看作 ___或 ___来试商在计算除法试商时为了方便计算我们通常会把除数看成与之接近的整十数或者比较容易计算的数24 接近 20同时 24 也接近 25所以计算 $187\\div24$ 时可以把除数看作 20 或 25 来试商", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n多位数除以两位数的试商\n除数不接近整十数的试商"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李阿姨带了 300 元钱去水果批发市场采购苹果一箱 56 元梨一箱 47 元（1）如果全部买苹果可以买多少箱还剩多少钱（2）如果先买两箱苹果剩下的钱用来买梨还可以买多少箱梨（1）用总钱数除以苹果每箱的价格得到能买的箱数和剩余的钱数300÷56 = 5（箱）……20（元）所以可以买5箱苹果还剩20元（2）先算出买两箱苹果花的钱56×2 = 112（元）再算出剩下的钱300 - 112 = 188（元）最后用剩下的钱除以梨每箱的价格得到能买梨的箱数188÷47 = 4（箱）所以还可以买4箱梨", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n多位数除以两位数的试商\n除数不接近整十数的试商"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用竖式计算（标注的要验算） $117\\div13=$ $520\\div65=$ $495\\div55=$ $352\\div44=$ $403\\div47=$ $210\\div34=$ 1. $117\\div13=9$ 2. $520\\div65=8$ 3. $495\\div55=9$ （验算 $55\\times9=495$ ）4. $352\\div44=8$ 5. $403\\div47=8...27$ 6. $210\\div34=6...6$ （验算 $34\\times6+6=210$ ）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n多位数除以两位数的试商\n除数不接近整十数的试商"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用竖式计算 $382\\div39=$ $445\\div54=$ $185\\div26=$ $219\\div25=$ $200\\div24=$ $206\\div26=$ 1. 计算 $382\\div39$  - 先看被除数的前两位 $38$  $38\\lt39$ 所以要看前三位 $382$  - 想 $39$ 乘几接近 $382$ 因为 $39\\times9=351$  $39\\times10=390$  $382-351=31$ 所以 $382\\div39=9\\cdots\\cdots31$ 2. 计算 $445\\div54$  - 被除数前两位 $44\\lt54$ 看前三位 $445$  - 因为 $54\\times8=432$  $54\\times9=486$  $445-432=13$ 所以 $445\\div54=8\\cdots\\cdots13$ 3. 计算 $185\\div26$  - 被除数前两位 $18\\lt26$ 看前三位 $185$  - 因为 $26\\times7=182$  $26\\times9=3510$  $9=3511$ 所以 $185\\div9=3512\\cdots\\cdots3$ 4. 计算 $219\\div25$  - 被除数前两位 $21\\lt25$ 看前三位 $219$  - 因为 $25\\times9=3513$  $25\\times9=3514$  $9=3515$ 所以 $219\\div9=3516\\cdots\\cdots19$ 5. 计算 $200\\div24$  - 被除数前两位 $20\\lt24$ 看前三位 $200$  - 因为 $24\\times9=3517$  $24\\times9=3518$  $9=3519$ 所以 $200\\div10=3900\\cdots\\cdots8$ 6. 计算 $206\\div26$  - 被除数前两位 $20\\lt26$ 看前三位 $206$  - 因为 $26\\times7=182$  $26\\times9=3510$  $10=3903$ 所以 $206\\div9=3512\\cdots\\cdots24$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n多位数除以两位数的试商\n除数不接近整十数的试商"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如果除数除以 18要使商不变被除数应（ ） ① 除以 18② 乘 18③ 不变④ 乘 9根据商不变的规律被除数和除数同时乘或除以相同的数（0除外）商不变现在除数除以18要让商不变那被除数也得除以18所以答案选①", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$390\\div30$ 商的最高位是 ___位商是 ___在计算 $390\\div30$ 时因为被除数的前两位是 $39$ 除数是 $30$  $39>30$ 所以商是两位数商的最高位是十位 $390\\div30=13$ 所以商是 $13$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$1500\\div400=( \\quad )$ ① $3\\cdots\\cdots3$ ② $300\\cdots\\cdots3$ ③ $3\\cdots\\cdots300$ ④ $300\\cdot\\cdots\\cdots300$ 本题可根据有余数的除法运算来求解在计算 $1500\\div400$ 时根据商不变的性质被除数和除数同时缩小 $100$ 倍变为 $15\\div4$  $15\\div4=3\\cdots\\cdots3$ 但是需要注意的是当被除数和除数同时缩小 $100$ 倍时余数也跟着缩小了 $100$ 倍所以原来的余数应该把现在的余数扩大 $100$ 倍即 $3\\times100=300$ 因此 $1500\\div400=3\\cdots\\cdots300$ 答案选③", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变规律中余数的变化"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "解决问题学校食堂共有 436 名学生同时就餐如果每张桌子可以坐 12 人那么需要多少张桌子本题可根据总人数和每张桌子可坐人数用除法计算出需要桌子的数量若有余数则桌子数需要在商的基础上加 1已知共有 436 名学生每张桌子坐 12 人则可列出算式436÷12 = 36（张）……4（人）这意味着坐满 36 张桌子后还剩下 4 人这 4 人也需要 1 张桌子所以总共需要的桌子数为36 + 1 = 37（张）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n用三位数除以两位数(商是两位数)解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用竖式计算并验算 $1196\\div23=$ $722\\div19=$ $\n798\\div57=\n$ 1. 计算1196÷23 - 先用119除以23商5余4再把6落下来46除以23商2所以1196÷23 = 52 - 验算根据“被除数 = 商×除数”52×23 = 1196计算正确2. 计算722÷19 - 用72除以19商3余15再把2落下来152除以19商8所以722÷19 = 38 - 验算38×19 = 722计算正确3. 计算798÷57 - 79除以57商1余22把8落下来228除以57商4所以798÷57 = 14 - 验算14×57 = 798计算正确", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n三位数除以两位数(商是两位数)的笔算除法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "5 $1\\div56$ 的商是两位数 里可以填 ___ 如果商是一位数 里可以填 ___要使商是两位数被除数的前两位需要大于或等于除数56因此□里可以填的数字有6、7、8、9要使商是一位数被除数的前两位需要小于除数56因此□里可以填的数字有1、2、3、4、5", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如果被除数乘 20要使商不变除数应（ ） ① 除以 20② 乘 40③ 不变④ 乘 20根据商不变的规律被除数和除数同时乘或除以相同的数（0除外）商不变现在被除数乘20要使商不变除数也应该乘20答案选④", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用竖式计算 $\n648\\div36=\\quad\n$ $875\\div27=\\quad$ $539\\div24=$ 1. 计算648÷36 - 先用被除数的前两位64除以3664÷36商1余28把8落下来288÷36商8所以648÷36 = 182. 计算875÷27 - 用被除数的前两位87除以2787÷27商3余6把5落下来65÷27商2余11所以875÷27 = 32……113. 计算539÷24 - 用被除数的前两位53除以2453÷24商2余5把9落下来59÷24商2余11所以539÷24 = 22……11", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n三位数除以两位数(商是两位数)的笔算除法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "两位数除三位数商可能是 ___位数也可能是 ___位数我们可以通过举例说明比如三位数除以两位数时最小的情况100÷99商是1余数是1商是一位数再看较大的情况990÷10 = 99商是两位数所以两位数除三位数商可能是一位数也可能是两位数", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "两个数的商是 8 如果被除数和除数都扩大为原来的 8 倍商是（ ） ① 64② 8③ 1④ 无法确定被除数和除数同时扩大相同的倍数（ $0$ 除外）商不变本题中被除数和除数都扩大为原来的 $8$ 倍所以商还是 $8$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下列算式中商是两位数的是（ ） ① $920\\div74$ ② $854\\div86$ ③ $285\\div47$ ④ $290\\div30$ 判断商是几位数我们可以比较被除数的前两位与除数的大小如果被除数的前两位大于或等于除数商就是两位数如果被除数的前两位小于除数商就是一位数对于① $920\\div74$ 被除数前两位 $92$ 大于除数 $74$ 所以商是两位数对于② $854\\div86$ 被除数前两位 $85$ 小于除数 $86$ 所以商是一位数对于③ $285\\div47$ 被除数前两位 $28$ 小于除数 $47$ 所以商是一位数对于④ $290\\div30$ 被除数前两位 $29$ 小于除数 $30$ 所以商是一位数综上商是两位数的是①", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在 里填上＂ $>$ ＂＂ $<$ ＂或＂ $=$ ＂360 $\\div$ 30360 $\\div$ 36 42 $\\div$ 7420 $\\div$ 70 75 $\\div$ 5 225 $\\div$ 15240 $\\div$ 80240 $\\div$ 8 480 $\\div$ 4048 $\\div$ 4 56 $\\div$ 8 560 $\\div$ 70对于360÷30○360÷36观察两边算式可知被除数不变除数越大则商越小因此应填“＞”  对于42÷7○420÷70观察两边算式可知被除数和除数同时扩大10倍则商不变因此应填“=”对于75÷5○225÷15观察两边算式可知被除数和除数同时扩大3倍则商不变 因此应填“=”      对于240÷80○240÷8观察两边算式可知被除数不变除数越小则商越大因此应填“＜”  对于480÷40○48÷4观察两边算式可知被除数和除数同时缩小10倍则商不变因此应填“=”对于56÷8○560÷70观察两边算式可知56÷8=560÷80被除数不变除数变小则商变大因此应填“＜”    ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "你能很快地判断出下列各题的商是几位数吗 ①被除数的前两位是 14小于除数 18因此商的最高位在十位上商是 2 位数②被除数的前两位是 63大于除数 36因此商的最高位在十位上商是 2 位数③被除数的前两位是 36小于除数 38因此商的最高位在个位上商是 1 位数④被除数的前两位是 76大于除数 53因此商的最高位在十位上商是 2 位数", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "直接写出得数 $380\\div20=$ $720\\div80=$ $66\\div33=$ $840\\div21=$ $560\\div14=$ $25\\times80=$ $50\\times9\\div45=$ $25\\times6\\div6=$ 1. 计算 $380\\div20$ 根据商不变的性质把被除数和除数同时缩小10倍变成 $38\\div2=19$ 2. 计算 $720\\div80$ 同样根据商不变的性质被除数和除数同时缩小10倍得到 $72\\div8=9$ 3. 计算 $66\\div33$ 直接计算可得 $66\\div33=2$ 4. 计算 $840\\div21$ 可以把840看成 $40\\times21$ 所以 $840\\div21=40$ 5. 计算 $560\\div14$ 把560看成 $40\\times14$ 那么 $560\\div14=40$ 6. 计算 $25\\times80$ 先算 $25\\times8=200$ 再在积的末尾添上1个0结果是20007. 计算 $50\\times9\\div45$ 先算 $50\\times9=450$ 再算 $450\\div45=10$ 8. 计算 $25\\times6\\div6$ 先算 $25\\times6=150$ 再算 $150\\div2=190$ 也可以根据乘除混合运算的性质先算 $6\\div2=191$ 再算 $25\\times2=192$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "要使 $654\\div\\square8$ 的商是两位数 里最大可以填 ___要使654÷□8的商是两位数只需要确保被除数的前两位数大于或等于除数□8即65≥□8所以□里可以填1、2、3、4、5因此□里最大可以填5", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "运用商不变的性质计萛 $\n4800\\div800=\\quad$ $800\\div30=\\quad$ $9500\\div700=\\quad\n$ $8400\\div60=$ 1. 计算 $4800\\div800$ 根据商不变的性质被除数和除数同时除以 $100$ 即 $4800\\div800=( 4800\\div100 )\\div( 800\\div100 )=48\\div8=6$ 2. 计算 $800\\div30$ 被除数和除数同时除以 $10$  $800\\div30=( 800\\div10 )\\div( 30\\div10 )=80\\div3$  $80\\div3=26\\cdots\\cdots2$ 因为同时缩小了 $10$ 倍所以余数要扩大 $10$ 倍结果为 $26\\cdots\\cdots20$ 3. 计算 $9500\\div700$ 被除数和除数同时除以 $100$  $9500\\div700=( 9500\\div100 )\\div( 700\\div100 )=95\\div7$  $95\\div7=13\\cdots\\cdots4$ 因为同时缩小了 $100$ 倍所以余数要扩大 $100$ 倍结果为 $13\\cdots\\cdots400$ 4. 计算 $8400\\div60$ 被除数和除数同时除以 $10$  $8400\\div60=( 8400\\div10 )\\div( 60\\div10 )=840\\div6=140$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变规律中余数的变化"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先找规律再写出商 $12\\div6=$ $8\\div2=$ $45\\div15=$ $72\\div8=$ $120\\div60=$ $80\\div20=$ $450\\div150=$ $720\\div80=$ $1200\\div600=$ $800\\div200=$ $4500\\div1500=$ $7200\\div800=$ 首先明确商不变的规律在除法里被除数和除数同时乘或除以相同的数（0除外）商不变①对于 $12\\div6$  $120\\div60$ 中被除数 $12$ 变为 $120$ 是乘 $10$ 除数 $6$ 变为 $60$ 也是乘 $10$  $1200\\div600$ 中被除数 $12$ 变为 $1200$ 是乘 $100$ 除数 $6$ 变为 $600$ 也是乘 $100$ 根据商不变规律 $12\\div6=120\\div60=1200\\div600=2$ ②对于 $8\\div2$  $80\\div20$ 中被除数 $8$ 变为 $80$ 是乘 $10$ 除数 $2$ 变为 $20$ 也是乘 $10$  $800\\div200$ 中被除数 $8$ 变为 $800$ 是乘 $100$ 除数 $2$ 变为 $200$ 也是乘 $100$ 根据商不变规律 $8\\div2=80\\div20=800\\div200=4$ ③对于 $45\\div15$  $450\\div150$ 中被除数 $45$ 变为 $450$ 是乘 $10$ 除数 $15$ 变为 $150$ 也是乘 $10$  $4500\\div1500$ 中被除数 $45$ 变为 $4500$ 是乘 $100$ 除数 $15$ 变为 $1500$ 也是乘 $100$ 根据商不变规律 $45\\div15=450\\div150=4500\\div1500=3$ ④对于 $72\\div8$  $720\\div80$ 中被除数 $72$ 变为 $720$ 是乘 $10$ 除数 $8$ 变为 $80$ 也是乘 $10$  $7200\\div800$ 中被除数 $72$ 变为 $7200$ 是乘 $100$ 除数 $8$ 变为 $800$ 也是乘 $100$ 根据商不变规律 $72\\div6=1200\\div6=1201\\div6=1202$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$47\\div68$ 要使商是两位数 里最小可以填 ___ 最大可以填 ___为了使商是两位数被除数的前两位数必须大于或等于除数68即□4≥68这意味着□里可以填7、8或9所以□最小填7最大填9", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "解决问题张老师去商店买了 64 本笔记本结账时售货员收了张老师 600 元找零 72 元还赠送了 2 本同款笔记本每本笔记本实际上只需支付多少钱首先算出张老师买笔记本实际花的钱数用付的钱减去找零的钱即 600 - 72 = 528 元然后算张老师实际得到的笔记本数量买了 64 本还赠送 2 本一共 64 + 2 = 66 本最后求每本笔记本实际支付的钱数用实际花的钱除以实际得到的本数528 ÷ 66 = 8 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n多位数除以两位数的试商\n除数不接近整十数的试商"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$92\\div3=30\\cdots\\cdots2$ 若被除数和除数同时扩大到原来的 10 倍则余数为（ ） ① 2② 20③ 200④ 2000根据商不变的性质被除数和除数同时扩大到原来的10倍商不变但余数会跟着扩大相同的倍数原来的余数是2被除数和除数同时扩大到原来的10倍后余数也扩大10倍 $2\\times10=20$ 所以余数为20", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变规律中余数的变化"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下列各算式的商与 $9600\\div640$ 相等的是（ ） ① $9600\\div64$ ② $96\\div64$ ③ $480\\div32$ ④ $960\\div640$ 根据商不变的规律被除数和除数同时乘或除以相同的数（0除外）商不变①中除数由 $640$ 变为 $64$ 被除数不变商改变②中被除数由 $9600$ 变为 $96$ 除数由 $640$ 变为 $64$ 被除数和除数变化倍数不同商改变③中由于 $9600\\div20=480$  $640\\div20=32$ 即被除数和除数同时除以20得到 $480\\div32$ 商不变④中被除数由 $9600$ 变为 $960$ 除数不变商改变综上与 $9600\\div640$ 相等的是 $480\\div32$ 答案选③", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "两个数相除的商是 6 若两数都除以 3 则商是 ___根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变本题中两个数相除商是6当被除数和除数都除以3时商不变所以商还是6", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$275\\div34$ 的商是 ___位数 $512\\div46$ 的商是 ___位数判断商是几位数我们可以比较被除数的前两位与除数的大小对于 $275\\div34$ 被除数 $275$ 的前两位是 $27$ 除数是 $34$ 因为 $2746$ 所以商是两位数", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$A\\divB=12$ 若 $A、B$ 同时除以 4 则商是 ___ 若 $A$ 乘 $4,B$ 除以 2 则商是 ___本题可根据商的变化规律来求解1. 求 $A$ 、 $B$ 同时除以 $4$ 时的商根据商不变的规律被除数和除数同时乘或除以相同的数（ $0$ 除外）商不变已知 $A\\divB=12$ 当 $A$ 、 $B$ 同时除以 $4$ 时商不变所以商还是 $12$ 2. 求 $A$ 乘 $4$  $B$ 除以 $2$ 时的商当被除数 $A$ 乘 $4$ 时商也会乘 $4$ 此时商变为 $12×4=48$ 当除数 $B$ 除以 $2$ 时商反而会乘 $2$ 那么 $48$ 再乘 $2$ 即 $48×2=96$ 所以 $A$ 乘 $4$  $B$ 除以 $2$ 时商是 $96$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商的变化规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$275\\div34$ 的商是 ___位数 $512\\div46$ 的商是 ___位数判断商是几位数我们可以比较被除数的前两位与除数的大小对于 $275\\div34$ 被除数 $275$ 的前两位是 $27$ 除数是 $34$ 因为 $2746$ 所以商是两位数", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "两个数相除的商是 6 若两数都除以 3 则商是 ___根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变本题中两个数相除商是6当被除数和除数都除以3时商不变所以商还是6", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "选择正确的余数填在 里 $90\\div20=4\\cdots\\cdots$ （1,10） $360\\div80=4\\cdots\\cdots$ （4,40） $180\\div40=4\\cdots\\cdots$ （2,20） $3800\\div700=5\\cdots\\cdots$ （3,30,300）①被除数和除数同时除以 $10$  $90\\div20=( 90\\div10 )\\div( 20\\div10 )=9\\div2$  $9\\div2=4\\cdots\\cdots1$ 因为同时缩小了 $10$ 倍所以余数要扩大 $10$ 倍结果为 $4\\cdots\\cdots10$ ②被除数和除数同时除以 $10$  $360\\div80=( 360\\div10 )\\div( 80\\div10 )=36\\div8$  $36\\div8=4\\cdots\\cdots4$ 因为同时缩小了 $10$ 倍所以余数要扩大 $10$ 倍结果为 $4\\cdots\\cdots40$ ③被除数和除数同时除以 $10$  $180\\div40=( 180\\div10 )\\div( 40\\div10 )=18\\div4$  $18\\div4=4\\cdots\\cdots2$ 因为同时缩小了 $10$ 倍所以余数要扩大 $10$ 倍结果为 $4\\cdots\\cdots20$ ④被除数和除数同时除以 $100$  $3800\\div700=( 3800\\div100 )\\div( 700\\div100 )=38\\div7$  $38\\div7=5\\cdots\\cdots3$ 因为同时缩小了 $100$ 倍所以余数要扩大 $100$ 倍结果为 $5\\cdots\\cdots300$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变规律中余数的变化"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$92\\div3=30\\cdots\\cdots2$ 若被除数和除数同时扩大到原来的 10 倍则余数为（ ） ① 2② 20③ 200④ 2000根据商不变的性质被除数和除数同时扩大到原来的10倍商不变但余数会跟着扩大相同的倍数原来的余数是2被除数和除数同时扩大到原来的10倍后余数也扩大10倍 $2\\times10=20$ 所以余数为20", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变规律中余数的变化"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$38\\div38$ 要使商是一位数 里可以填 ___ 要使商是两位数 里最小可以填 ___要使商是一位数[图片2]里可以填的数字需要使得被除数小于380因此[图片2]里可以填1到9中的任意一个数字即1,2,3,4,5,6,7,8,9要使商是两位数[图片3]里最小可以填的数字需要使得被除数大于或等于380因此[图片3]里最小可以填3", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$38\\div38$ 要使商是一位数 里可以填 ___ 要使商是两位数 里最小可以填 ___要使商是一位数被除数的前两位□3要小于除数38因此□里可以填1,2,3要使商是两位数被除数的前两位□3要大于或等于除数38因此□里可以填4,5,6,7,8,9最小可以填4", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "三位数除以两位数商是（ ） ① 一位数② 两位数③ 三位数④ 一位数或两位数在三位数除以两位数时我们可以通过举例来判断商的位数比如100÷50 = 2商是一位数而100÷10 = 10商是两位数所以三位数除以两位数商可能是一位数也可能是两位数", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如果 $( A\\times20 )\\div( B\\times20 )=16$ 那么 $A\\divB=$ ___根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变在 $( A\\times20 )\\div( B\\times20 )$ 中被除数 $A$ 和除数 $B$ 都同时乘了20所以商不变那么 $A\\divB$ 的商和 $( A\\times20 )\\div( B\\times20 )$ 的商一样都是16", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$A\\divB=12$ 若 $A、B$ 同时除以 4 则商是 ___ 若 $A$ 乘 $4,B$ 除以 2 则商是 ___1. 求 $A$ 、 $B$ 同时除以 $4$ 时的商根据商不变的规律被除数和除数同时乘或除以相同的数（ $0$ 除外）商不变已知 $A\\divB=12$ 当 $A$ 、 $B$ 同时除以 $4$ 时商不变所以商还是 $12$ 2. 求 $A$ 乘 $4$  $B$ 除以 $2$ 时的商当被除数 $A$ 乘 $4$ 时商也会乘 $4$ 此时商变为 $12×4=48$ 当除数 $B$ 除以 $2$ 时商反而会乘 $2$ 那么 $48$ 再乘 $2$ 即 $48×2=96$ 所以 $A$ 乘 $4$  $B$ 除以 $2$ 时商是 $96$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商的变化规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在 $\\bigcirc$ 里填上＂ $>$ ＂＂ $ 425÷452. 比较168÷12和329÷12 - 在除法运算中当除数相同时被除数越大商越大 - 因为168 < 329所以168÷12 < 329÷123. 比较42÷2和420÷20 - 根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变 - 420÷20中420是42乘10得到的20是2乘10得到的被除数和除数同时乘10所以42÷2 = 420÷20", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商的变化规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如果 $( A\\times20 )\\div( B\\times20 )=16$ 那么 $A\\divB=$ ___根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变在 $( A\\times20 )\\div( B\\times20 )$ 中被除数 $A$ 和除数 $B$ 都同时乘了20所以商不变那么 $A\\divB$ 的商和 $( A\\times20 )\\div( B\\times20 )$ 的商一样都是16", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "三位数除以两位数商是（ ） ① 一位数② 两位数③ 三位数④ 一位数或两位数我们可以通过举例来判断商的位数当被除数的前两位小于除数时商是一位数比如 $100÷50=2$ 当被除数的前两位大于或等于除数时商是两位数比如 $120÷10=12$ 所以三位数除以两位数商可能是一位数也可能是两位数", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "两个数相除的商是 18 若被除数和除数都乘 6 则商是（ ） ① 18② 108③ 3④ 54根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变本题中被除数和除数都乘6那么商不变还是18所以选①", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在 $\\bigcirc$ 里填上＂ $>$ ＂＂ $ 425÷45②比较168÷12和329÷12在除法运算中当除数相同时被除数越大商越大因为168 < 329所以168÷12 < 329÷12③比较42÷2和420÷20根据商不变的性质被除数和除数同时乘或除以相同的数（0除外）商不变420÷20中420是42乘10得到的20是2乘10得到的被除数和除数同时乘10所以42÷2 = 420÷20", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "直接写出得数 $140\\div20=$ $300\\div60=$ $630\\div70=$ $450\\div90=$ $282\\div50\\approx$ $360\\div58\\approx$ $718\\div89\\approx$ $500\\div80\\approx$ $5600\\div800=$ $3500\\div700=$ $4800\\div1200=$ $24000\\div400=$ 1. 对于整十数相除如140÷20想14个十除以2个十就是14÷2 = 7同理300÷60 = 30÷6 = 5630÷70 = 63÷7 = 9450÷90 = 45÷9 = 55600÷800 = 56÷8 = 73500÷2 = 70÷2 = 714800÷2 = 72÷2 = 732. 对于估算 - 282÷50把282近似看成300300÷2 = 74 - 360÷58把58近似看成60360÷2 = 75 - 718÷89把718近似看成72089近似看成90720÷2 = 76 - 500÷80把500近似看成480480÷2 = 773. 24000÷40024000里面有240个百400里面有4个百240÷2 = 78", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商不变的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "  $48\\div45$ 要使商是一位数 里可以填 ___ 要使商是两位数 里可以填 ___要使商是一位数即 $□48\\div45$ 的商为一位数需要被除数的前两位小于除数也就是 $□4<45$ 因此□里可以填的数字为1, 2, 3, 4要使商是两位数即 $□48\\div45$ 的商为两位数需要被除数的前两位大于或等于除数也就是 $□4≥45$ 因此□里可以填的数字为5, 6, 7, 8, 9", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "花苗每棵 8 元树苗每棵 12 元 176 元最多可以买（ ） 棵树苗① 22② 14③ 15④ 8本题可根据“数量 = 总价÷单价”来计算 $176$ 元最多可买树苗的数量已知树苗每棵 $12$ 元总价是 $176$ 元则可买树苗的数量为 $176\\div12=14$ （棵） $\\cdots\\cdots8$ （元）这意味着 $176$ 元买 $14$ 棵树苗后还剩 $8$ 元剩下的钱不够再买 $1$ 棵树苗所以最多可以买 $14$ 棵树苗", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n用三位数除以两位数(商是两位数)解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下列问题中不能用 $180\\div20$ 这个算式来解决的是（ ） ① 育才小学的 180 名少先队员参加＂学雷锋＂活动平均分成 20 个组每组多少人② 刘叔叔带180元买文具盒每个文具盒20元最多可以买多少个文具盒③ 每套校服 180 元买 20 套要多少钱④ 科技书有 180 本是童话书的 20 倍童话书有多少本下面对每个选项进行分析①把180名少先队员平均分成20个组求每组多少人就是把180平均分成20份求每份是多少用除法计算列式为 $180\\div20$ ②刘叔叔带180元买每个20元的文具盒求最多可以买多少个就是求180里有几个20用除法计算列式为 $180\\div20$ ③每套校服180元买20套需要的钱数就是求20个180是多少用乘法计算列式为 $180\\times20$ 不能用 $180\\div20$ 解决④科技书有180本是童话书的20倍求童话书有多少本就是已知一个数的20倍是180求这个数用除法计算列式为 $180\\div20$ 所以不能用 $180\\div20$ 这个算式来解决的是③答案选C", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "直接写出得数 $140\\div20=$ $300\\div60=$ $630\\div70=$ $450\\div90=$ $282\\div50\\approx$ $360\\div58\\approx$ $718\\div89\\approx$ $500\\div80\\approx$ $5600\\div800=$ $3500\\div700=$ $4800\\div1200=$ $24000\\div400=$ 1. 对于整十数相除如140÷20想14个十除以2个十就是14÷2 = 7同理300÷60 = 30÷6 = 5630÷70 = 63÷7 = 9450÷90 = 45÷9 = 55600÷800 = 56÷8 = 73500÷2 = 70÷2 = 714800÷2 = 72÷2 = 732. 对于估算 - 282÷50把282近似看成300300÷2 = 74所以282÷50≈6 - 360÷58把58近似看成60360÷2 = 75所以360÷58≈6 - 718÷89把718近似看成72089近似看成90720÷2 = 76所以718÷89≈8 - 500÷80把500近似看成480480÷2 = 77所以500÷80≈63. 24000÷40024000里面有240个百400里面有4个百240÷2 = 78", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n除数是两位数的估算\n两位数除三位数的估算方法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "实验小学有 580 名学生参加社会实践活动每辆客车限乘 40 人需要多少辆客车右图坚式中＂ $\\leftarrow$ ＂所指的数表示（ ） ① 坐满 14 辆客车还剩 18 人② 坐满 10 辆客车还剩 18 人③ 坐满 14 辆客车还剩 180 人④ 坐满 10 辆客车还剩 180 人题目要求分析竖式中箭头所指的数“18”表示的意义根据竖式580除以40商为14即坐满14辆客车然后用580减去14乘以40（即560）得到余数为20但是箭头指向的是“18”这是在计算过程中的一部分具体是58（580去掉一个0）除以4（40去掉一个0）时4乘以14得到56然后58减去56得到的差即18这表示在当前步骤中还剩18人（实际是180人因为之前都去掉了一个0）因此正确选项是① 坐满14辆客车还剩18人", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n用三位数除以两位数(商是两位数)解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "实验小学有 580 名学生参加社会实践活动每辆客车限乘 40 人需要多少辆客车右图坚式中＂ $\\leftarrow$ ＂所指的数表示（ ） ① 坐满 14 辆客车还剩 18 人② 坐满 10 辆客车还剩 18 人③ 坐满 14 辆客车还剩 180 人④ 坐满 10 辆客车还剩 180 人竖式中箭头所指的“18”因为被除数和除数同时缩小了10倍（末尾同时去掉一个0）所以这里的18实际表示的是180它是580减去40×10的结果依次分析4个选项选项①说坐满14辆客车还剩18人与计算过程中箭头所指数字的意义不符错误选项②坐满10辆客车但剩余人数不是18人（实际是180人）错误选项③坐满14辆客车不符合因为这里是计算到十位上的1（表示10辆）错误选项④坐满10辆客车( 40×10=400 )还剩180人( 580-400=180 )与箭头所指数字实际意义（缩小10倍后显示为18实际是180）相符正确综上答案选④", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n用三位数除以两位数(商是两位数)解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用竖式计算（标注的要验算） $78\\div40=$ $279\\div93=$ $448\\div48=$ $900\\div45=$ $5700\\div1900=$ $576\\div32=$ 1. $78\\div40=1.95$ 2. $279\\div93=3$ 3. $448\\div48\\approx9.33$ 4. $900\\div45=20$ 5. $5700\\div1900=3$ （验算 $3\\times1900=5700$ ）6. $576\\div32=18$ （验算 $18\\times32=576$ ）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n三位数除以两位数(商是两位数)的笔算除法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下列问题中不能用 $180\\div20$ 这个算式来解决的是（ ） ① 育才小学的 180 名少先队员参加＂学雷锋＂活动平均分成 20 个组每组多少人② 刘叔叔带180元买文具盒每个文具盒20元最多可以买多少个文具盒③ 每套校服 180 元买 20 套要多少钱④ 科技书有 180 本是童话书的 20 倍童话书有多少本下面对每个选项进行分析①把180名少先队员平均分成20个组求每组多少人就是把180平均分成20份求每份是多少用除法计算列式为 $180\\div20$ ②刘叔叔带180元买每个20元的文具盒求最多可以买多少个就是求180里有几个20用除法计算列式为 $180\\div20$ ③每套校服180元买20套需要的钱数就是求20个180是多少用乘法计算列式为 $180\\times20$ 不能用 $180\\div20$ 解决④科技书有180本是童话书的20倍求童话书有多少本就是已知一个数的20倍是180求这个数用除法计算列式为 $180\\div20$ 所以不能用 $180\\div20$ 这个算式来解决的是③", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李大爷购买化肥的相关信息如下①李大爷带了 700 元去农技站买化肥②农技站离家 20 千米③买了 18 袋同样的化肥④剩下 160 元⑤用这些化肥给 12000平方米的稻田施肥每袋化肥多少钱（1）要解答上面的问题需要的信息有 ___（填序号）（2）列式解答（1）要求每袋化肥多少钱需要知道买化肥总共花的钱数和买的化肥袋数李大爷带的钱数减去剩下的钱数就是买化肥花的钱所以需要的信息是①李大爷带了700元去农技站买化肥③买了18袋同样的化肥④剩下160元（2）先算出买化肥花的钱 $700-160=540$ （元）再用花的钱除以买的袋数得到每袋化肥的价格即 $540÷18=30$ （元）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "某书店在国庆期间有优惠销售活动其中《趣味数学》每本 24 元买 4 本送 1 本 288 元最多能买多少本《趣味数学》首先计算不考虑赠送时288 元可以买几本《趣味数学》用总钱数除以每本书的单价即 288÷24 = 12 本然后因为买 4 本送 1 本看 12 本里面有几个 4 本12÷4 = 3也就是送 3 本最后把买的本数和送的本数相加12 + 3 = 15 本所以 288 元最多能买 15 本《趣味数学》", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "学校开展图书捐赠公益活动共捐赠了 720 本图书要把这些图书每 15 本捆一捆每 6 捆装一箱需要装多少个箱子本题可先求出一共捆了多少捆再根据每箱装的捆数求出需要多少个箱子步骤一计算一共捆了多少捆已知共有 720 本图书每 15 本捆一捆求捆数就是求 720 里有多少个 15用除法计算可得捆数为 $720\\div15=48$ （捆）步骤二计算需要多少个箱子已知每 6 捆装一箱一共有 48 捆求箱子数就是求 48 里有多少个 6用除法计算可得箱子数为 $48\\div6=8$ （个）综上需要装 8 个箱子", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n用三位数除以两位数(商是两位数)解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "某书店在国庆期间有优惠销售活动其中《趣味数学》每本 24 元买 4 本送 1 本 288 元最多能买多少本《趣味数学》首先计算不考虑赠送时288 元可以买几本《趣味数学》用总钱数除以每本书的单价即 288÷24 = 12 本然后因为买 4 本送 1 本看 12 本里面有几个 4 本12÷4 = 3也就是送 3 本最后把买的本数和送的本数相加12 + 3 = 15 本所以 288 元最多能买 15 本《趣味数学》", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n用三位数除以两位数(商是两位数)解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李大爷购买化肥的相关信息如下①李大爷带了 700 元去农技站买化肥②农技站离家 20 千米③买了 18 袋同样的化肥④剩下 160 元⑤用这些化肥给 12000平方米的稻田施肥每袋化肥多少钱（1）要解答上面的问题需要的信息有 ___（填序号）（2）列式解答（1）要求每袋化肥多少钱需要知道买化肥总共花的钱数和买的化肥袋数李大爷带的钱数减去剩下的钱数就是买化肥花的钱所以需要的信息是①李大爷带了700元去农技站买化肥③买了18袋同样的化肥④剩下160元（2）先算出买化肥花的钱 $700-160=540$ （元）再用花的钱除以买的袋数得到每袋化肥的价格即 $540÷18=30$ （元）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n用三位数除以两位数(商是两位数)解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面是同一种钢笔的价格林老师要买 24 支这种钢笔至少要花多少钱若买 27 支呢首先我们来分析每种购买方式的单价1. 10支60元即每支 $\\frac{60}{10}=6$ 元2. 4支30元即每支 $\\frac{30}{4}=7.5$ 元3. 每支8元显然第一种方式最便宜第二种次之第三种最贵接下来计算买24支钢笔的最少花费1. 先买2组10支花费 $60\\times2=120$ 元还差4支2. 再买1组4支花费30元因此买24支钢笔的最少花费为 $120+30=144$ 元然后计算买27支钢笔的最少花费1. 先买2组10支花费 $60\\times2=120$ 元还差7支2. 再买1组4支花费30元还差3支3. 最后买3支单支花费 $8\\times3=24$ 元因此买27支钢笔的最少花费为 $120+30+24=174$ 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面是同一种钢笔的价格林老师要买 24 支这种钢笔至少要花多少钱若买 27 支呢首先我们来分析每种购买方式的单价1. 10支60元即每支 $60÷10=6$ 元2. 4支30元即每支 $30÷4=7.5$ 元3. 每支8元显然第一种方式最便宜第二种次之第三种最贵接下来计算买24支钢笔的最少花费1. 先买2组10支花费60×2=120元还差4支2. 再买1组4支花费30元因此买24支钢笔的最少花费为120+30=150元然后计算买27支钢笔的最少花费1. 先买2组10支花费60×2=120元还差7支2. 再买1组4支花费30元还差3支3. 最后买3支单支花费8×3=24元因此买27支钢笔的最少花费为120+30+24=174元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是两位数的笔算除法\n用三位数除以两位数(商是两位数)解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一天小月妈妈要骑车外出外出之前必须做完以下几件事自行车打气需 2 分钟整理房间需 7 分钟擦鞋子需 2 分钟把衣服放进洗衣机需 1 分钟洗衣机自动洗涤需 20 分钟晾衣服需 5 分钟小月妈妈怎样做才能最节省时间至少要多少时间因为洗衣机自动洗涤的 20 分钟里可以同时做其他事情先把衣服放进洗衣机用 1 分钟然后洗衣机洗的时候去做自行车打气 2 分钟、整理房间 7 分钟、擦鞋子 2 分钟这几件事加起来2 + 7 + 2 = 11分钟11分钟小于20分钟能在洗衣机洗涤时完成最后再晾衣服用 5 分钟总共花费时间就是1 + 20 + 5 = 26分钟这样安排最节省时间", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n沏茶问题\n沏茶问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用一只平底锅煎饼每次能同时放 2 块饼假定煎 1 块饼需要 4 分钟（煎正、反两面各需 2 分钟）（1）煎 6 块饼至少需要多少分钟（2）用同样的锅煎 108 块饼至少需要多少分钟（1）因为每次能同时放 2 块饼煎 1 块饼正、反两面各需 2 分钟也就是同时煎 2 块饼的正反面共需 4 分钟6 块饼可以分 6÷2 = 3 次煎完每次 4 分钟所以煎 6 块饼至少需要 4×3 = 12 分钟（2）同理108 块饼可以分 108÷2 = 54 次煎完每次 4 分钟所以煎 108 块饼至少需要 4×54 = 216 分钟", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n烙饼问题\n烙饼问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "王涛感冒了姐姐要为王涛冲泡＂感冒冲剂＂她拿药需 1 分钟洗杯子需 1 分钟放药、搅拌均匀需 2 分钟饮水机烧水需 5 分钟姐姐最少需要（ ） 分钟① 9② 8③ 5④ 7因为饮水机烧水不需要一直盯着在烧水的 $5$ 分钟内可以同时完成拿药 $1$ 分钟和洗杯子 $1$ 分钟这二者加起来 $1+1=2$ 分钟 $2<5$ 即在烧水时间内可以完成这两项任务水烧好后再放药、搅拌均匀需 $2$ 分钟所以总共花费的时间就是烧水的 $5$ 分钟加上放药、搅拌均匀的 $2$ 分钟即 $5+2=7$ 分钟", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n沏茶问题\n沏茶问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "把长方形的一角折叠起来（如右图所示）已知 $∠1=30^{\\circ}$ 则 $∠2=$ ___（第6题）根据题目描述长方形的一角折叠起来形成了两个相等的角即 $∠1$ 和折叠后的角相等都为 $30^{\\circ}$ 因为长方形的角是直角为90°所以∠2 = 90° - ∠1 - ∠1 = 90° - 30° - 30° = 30°", "output": "小学数学新知识树\n图形与几何\n图形的拼组\n图形的折叠问题\n角度的简单计算--折叠"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "杨明与妹妹玩扑克牌比大小的游戏每人每次出一张牌各出三次赢两次者胜杨明手里的牌是 $9、7、5$ 妹妹手里的牌是 $8、6、3$ 如果杨明先出牌妹妹后出牌比赛结果是（ ） ① 妹妹可能赢② 妹妹不可能赢③ 杨明一定赢④ 杨明一定输这是一个关于扑克牌比大小策略的问题我们可以用杨明和妹妹手中的牌按照不同顺序出牌来分析比赛结果已知杨明先出牌妹妹后出牌若杨明出 $9$ 妹妹出 $3$ 杨明赢杨明出 $7$ 妹妹出 $8$ 妹妹赢杨明出 $5$ 妹妹出 $6$ 妹妹赢这样妹妹赢了两次最终妹妹获胜所以妹妹是有可能赢的", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n田忌赛马问题\n田忌赛马"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用平底锅烙饼时同一张饼第一面需要烙 4 分钟第二面需要烙 3 分钟一只平底锅每次最多可以烙 2 张饼烙 3 张饼至少需要 ___分钟本题可通过合理安排烙饼的顺序来计算烙 $3$ 张饼所需的最少时间将 $3$ 张饼分别记为 $A$ 、 $B$ 、 $C$ 先同时烙 $A$ 、 $B$ 的第一面需要 $4$ 分钟把 $B$ 拿出来放入 $C$ 烙 $A$ 的第二面和 $C$ 的第一面 $C$ 的第一面需要 $4$ 分钟在这 $4$ 分钟内 $A$ 的第二面也在烙所以这一步需要 $4$ 分钟此时 $A$ 已经烙好再烙 $B$ 的第二面和 $C$ 的第二面 $B$ 的第二面需要 $3$ 分钟 $C$ 的第二面也需要 $3$ 分钟所以这一步需要 $3$ 分钟此时 $B$ 、 $C$ 也烙好了总共花费的时间为 $4+4+3=11$ （分钟）", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n烙饼问题\n烙饼问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "玩具厂用机器给玩具贴图案每次只能同时贴两个玩具每个玩具都要求贴正、反两面贴一面需要 5 秒（1）怎样安排才能比较快地贴好 3 个玩具（2）贴 4 个玩具至少需要多少秒（3）贴 5 个玩具至少需要多少秒（4）把表格填完整你能发现什么信息\n| 玩具／个 | 3 | 4 | 5 | 20 | 100 |   | 所需的最少时间／秒 |   |   |   |   |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 玩具／个 | 3 | 4 | 5 | 20 | 100 |   |\n| 所需的最少时间／秒 |   |   |   |   |   |   |\n（1）为了较快贴好 3 个玩具要充分利用每次能同时贴两个玩具这个条件我们先把玩具 1 和玩具 2 的正面一起贴这用了 5 秒接着贴玩具 1 的反面和玩具 3 的正面又用了 5 秒最后贴玩具 2 和玩具 3 的反面还是 5 秒这样一共用了 15 秒是最快的贴法（2）贴 4 个玩具时我们可以两个两个地贴每次贴两个玩具的正反两面也就是一次贴好两个玩具需要 10 秒那么贴 4 个玩具分两次贴完总共就需要 20 秒（3）贴 5 个玩具时前面 3 个玩具按照（1）中的方法贴需要 15 秒剩下 2 个玩具一起贴正反两面需要 10 秒所以贴 5 个玩具至少需要 25 秒（4）通过前面的计算我们发现规律所需的最少时间等于玩具个数乘贴一面需要的时间所以 20 个玩具需要 20×5 = 100 秒100 个玩具需要 100×5 = 500 秒", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n烙饼问题\n烙饼问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李明的妈妈早晨起床后要做以下几件事\n| ①刷牙洗脸 | 4 分钟 | ⑤准备煲汤 | 2 分钟 | ②和面 | 5 分钟 | ⑥用高压锅煲汤 | 20 分钟 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| ①刷牙洗脸 | 4 分钟 | ⑤准备煲汤 | 2 分钟 |\n| ②和面 | 5 分钟 | ⑥用高压锅煲汤 | 20 分钟 |\n| ③准备烙饼馅 | 6 分钟 | ⑦吃早饭 | 10 分钟 |\n| ④烙馅饼 | 9 分钟 | ⑧洗碗筷 | 4 分钟 |\n（1）李明的妈妈做完这些事至少需要多少分钟\n（2）李明的妈妈早上750 要到办公室并且每天上班骑车需要 20 分钟她每天早上最迟什么时间起床才能确保上班不迟到\n（3）假如李明的妈妈早上烙饼的数量由原来的 3 张增加到 5 张且每次只能烙一张馅饼烙好每张馅饼的时间相同做其他事的时间不变你觉得李明的妈妈每天的起床时间需不需要变化计算后说明理由（1）本题可根据各项事情所需时间及先后顺序通过合理安排时间来计算做完这些事的最少用时因为用高压锅煲汤不需要一直盯着在煲汤的20分钟内可以同时进行刷牙洗脸（4分钟）、准备烙饼馅（6分钟）、和面（5分钟）、烙馅饼（9分钟）这几件事总共花费 $4+6+5+9=24$ 分钟 $24>20$ 即在煲汤的时间内可以完成这些事然后再加上准备煲汤的2分钟、吃早饭的10分钟、洗碗筷的4分钟总共需要 $2+24+10+4=40$ 分钟（2）本题可根据上班时间、骑车时间和做这些事所需时间来计算最迟起床时间做完这些事需要40分钟骑车需要20分钟总共需要 $40+20=60$ 分钟7时50分往前推60分钟即7时50分 - 60分 = 6时50分所以她每天早上最迟650起床才能确保上班不迟到（3）本题可先计算出原来和现在烙饼所需时间再比较两者的差异从而判断起床时间是否需要变化原来烙3张饼需要9分钟则烙一张饼需要 $9÷3=3$ 分钟现在烙5张饼需要 $3×5=15$ 分钟比原来多了 $15-9=6$ 分钟所以起床时间要提前6分钟即每天的起床时间需要变化", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n沏茶问题\n沏茶问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用一口锅烙饼每次只能放两块饼烙一块饼要 6 分钟（正、反面各需 3 分钟）（1）烙 1 块饼至少需要 ___分钟（2）烙 2 块饼至少需要 ___分钟（3）烙 3 块饼至少需要 ___分钟（4）烙 7 块饼至少需要 ___分钟（1）因为烙一块饼正、反面各需 3 分钟所以烙 1 块饼至少需要 $3+3=6$ 分钟（2）可以同时烙这 2 块饼的正面需要 3 分钟然后再同时烙这 2 块饼的反面也需要 3 分钟所以烙 2 块饼至少需要 $3+3=6$ 分钟（3）设这 3 块饼分别为 A、B、C第一步先烙 A 和 B 的正面需要 3 分钟第二步烙 A 的反面和 C 的正面需要 3 分钟第三步烙 B 和 C 的反面需要 3 分钟所以烙 3 块饼至少需要 $3×3=9$ 分钟（4）可以先 2 块 2 块地烙前 4 块饼每次烙 2 块共需烙 2 次每次 6 分钟所以前 4 块饼需要 $6×2=12$ 分钟剩下的 3 块饼按照上述烙 3 块饼的方法需要 9 分钟因此烙 7 块饼至少需要 $12+9=21$ 分钟", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n烙饼问题\n烙饼问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "亮亮早晨起床后刷牙、洗脸需 5 分钟微波炉热早饭需 3 分钟吃早饭需 10 分钟他做完这些事至少要 ___分钟因为在微波炉热早饭的3分钟时间里可以同时进行刷牙、洗脸这二者不冲突刷牙、洗脸需5分钟热早饭3分钟5＞3即在刷牙、洗脸的过程中早饭就热好了然后再吃早饭10分钟所以一共至少需要5 + 10 = 15分钟", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n沏茶问题\n沏茶问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在算式 $\\square24\\div73$ 中要使商是一位数 里最大可以填 ___ 要使商是两位数 里最小可以填 ___要使商是一位数即 $\\square24\\div73$ 的商为一位数需要被除数的前两位小于除数即□2＜73所以 □里可以填1、2、3、4、5、6、7最大可以填 7要使商是两位数即 $\\square24\\div73$ 的商为两位数需要被除数的前两位大于或等于除数即□2≥73所以□里可以填8、9最小可以填8", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n判断商是几位数（除数是两位数）\n判断商是几位数(除数是两位数)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "王老师要复印 7 张获奖证书正、反面都要复印如果复印机里一次最多放两张证书至少要复印（ ） 次① 5② 6③ 7④ 8根据题意我们可先将前4张按照顺序2张2张地复印共需要4次最后3张可先复印两张的正面然后复印其中一张的反面与最后一张的正面最后再复印剩下两张的反面共需要3次故一共需要4+3=7（次）", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n优化问题\n烙饼问题\n烙饼问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如右图所示 $∠1=35^{\\circ}$ 则 $∠2=$ ___  $∠3=$ ___  $∠4=$ ___  $∠5=$ ___（第 5 题）本题可根据直角、平角的度数以及角之间的关系来求解求∠2的度数由图可知∠2是直角根据直角的定义直角为90°所以∠2 = 90°求∠3的度数在三角形中三角形的内角和为180°已知∠1 = 35° ∠2 = 90°则∠3 = 180° - ∠1 -∠2 = 180° - 35° - 90° = 55°求∠4的度数因为∠3与∠4组成平角平角为180°所以∠4 = 180° - ∠3 = 180° - 55° = 125°求∠5的度数由于∠1与∠5组成平角平角是180°所以∠5 = 180° - ∠1=350° - 35° = 145°", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n线段与角的综合\n角度直接运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "估算下列各题 $402\\times8\\approx$ $89\\times31\\approx$ $408\\div52\\approx$ $638\\div69\\approx$ $202\\times90\\approx$ $139\\times19\\approx$ $299\\div62\\approx$ $320\\div39\\approx$ 1. 对于 $402\\times8$ 把402看作400 $400\\times8=3200$ 所以 $402\\times8\\approx3200$ 2. 对于 $89\\times31$ 把89看作9031看作30 $90\\times30=2700$ 所以 $89\\times31\\approx2700$ 3. 对于 $408\\div52$ 把408看作40052看作50 $400\\div50=8$ 所以 $408\\div52\\approx8$ 4. 对于 $638\\div69$ 把638看作63069看作70 $630\\div70=9$ 所以 $638\\div69\\approx9$ 5. 对于 $202\\times90$ 把202看作200 $200\\times90=18000$ 所以 $202\\times90\\approx18000$ 6. 对于 $139\\times19$ 把139看作14019看作20 $140\\times20=2800$ 所以 $139\\times19\\approx2800$ 7. 对于 $299\\div62$ 把299看作30062看作60 $300\\div60=5$ 所以 $299\\div62\\approx5$ 8. 对于 $320\\div39$ 把39看作40 $320\\div40=8$ 所以 $320\\div39\\approx8$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘两位数的估算\n三位数乘两位数的估算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "估算下列各题 $402\\times8\\approx$ $89\\times31\\approx$ $408\\div52\\approx$ $638\\div69\\approx$ $202\\times90\\approx$ $139\\times19\\approx$ $299\\div62\\approx$ $320\\div39\\approx$ 1. 对于 $402\\times8$ 把402看作400 $400\\times8=3200$ 所以 $402\\times8\\approx3200$ 2. 对于 $89\\times31$ 把89看作9031看作30 $90\\times30=2700$ 所以 $89\\times31\\approx2700$ 3. 对于 $408\\div52$ 把408看作40052看作50 $400\\div50=8$ 所以 $408\\div52\\approx8$ 4. 对于 $638\\div69$ 把638看作63069看作70 $630\\div70=9$ 所以 $638\\div69\\approx9$ 5. 对于 $202\\times90$ 把202看作200 $200\\times90=18000$ 所以 $202\\times90\\approx18000$ 6. 对于 $139\\times19$ 把139看作14019看作20 $140\\times20=2800$ 所以 $139\\times19\\approx2800$ 7. 对于 $299\\div62$ 把299看作30062看作60 $300\\div60=5$ 所以 $299\\div62\\approx5$ 8. 对于 $320\\div39$ 把39看作40 $320\\div40=8$ 所以 $320\\div39\\approx8$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n除数是两位数的估算\n两位数除三位数的估算方法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "已知 $16\\times17=272$ 则 $16\\times34=$ ___  $48\\times17=$ ___本题可根据积的变化规律来求解求 $16\\times34$ 的积已知 $16\\times17=272$ 而 $34=17\\times2$ 即第二个因数 $17$ 扩大到原来的 $2$ 倍变为 $34$ 第一个因数 $16$ 不变根据积的变化规律一个因数不变另一个因数扩大几倍积也扩大相同的倍数所以 $16\\times34$ 的积是 $16\\times17$ 积的 $2$ 倍即 $272\\times2=544$ 求 $48\\times17$ 的积已知 $16\\times17=272$ 而 $48=16\\times3$ 即第一个因数 $16$ 扩大到原来的 $3$ 倍变为 $48$ 第二个因数 $17$ 不变根据积的变化规律一个因数不变另一个因数扩大几倍积也扩大相同的倍数所以 $48\\times17$ 的积是 $16\\times17$ 积的 $3$ 倍即 $272\\times3=816$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n积的变化规律（整数乘法）\n一个因数变化的规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "天安门广场的面积是 44（ ） ①平方千米②公顷③平方米④平方分米选项①1平方千米是边长为1千米的正方形的面积平方千米这个单位很大天安门广场面积用平方千米做单位太大了不合适选项②1公顷等于10000平方米天安门广场面积约是44公顷这个是符合实际情况的选项③1平方米大概就是边长为1米的正方形的大小44平方米对于天安门广场来说太小了选项④1平方分米是边长为1分米的正方形的面积平方分米这个单位比平方米还小很多更不适合用来表示天安门广场的面积所以应该选②", "output": "小学数学新知识树\n图形与几何\n测量\n常见的面积单位及换算\n面积单位的选择\n选择合适的面积单位(公顷、平方千米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "已知 $A\\divB=18$ 若 $A$ 扩大 2 倍 $B$ 不变则商是 ___在除法算式中除数不变被除数扩大几倍商就扩大几倍已知 $A\\divB=18$ 现在 $A$ 扩大 $2$ 倍 $B$ 不变那么商也扩大 $2$ 倍 $18\\times2=36$ 所以商是 $36$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n商的变化规律\n商的变化规律"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下列各数中一个 0 也不用读的数是（ ） ① 460780② 608700③ 2060000④ 500500根据整数的读法从高位到低位一级一级地读每一级末尾的 0 都不读出来其余数位连续几个 0 都只读一个零① 460780 读作四十六万零七百八十读 1 个 0② 608700 读作六十万八千七百一个 0 也不读③ 2060000 读作二百零六万读 1 个 0④ 500500 读作五十万零五百读 1 个 0综上一个 0 也不读的数是 608700答案选②", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n大数的认识\n亿以内数的读、写法\n亿以内数的读法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "观察思考回答问题四（1）班2024年5月学生借阅图书情况统计表\n| 图书种类 | 文学类 | 艺术类 | 科技类 | 漫画类 | 人数 | 18 | 6 | 9 | 15 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 图书种类 | 文学类 | 艺术类 | 科技类 | 漫画类 |\n| 人数 | 18 | 6 | 9 | 15 |\n．根据统计表将下面的统计图补充完整四（1）班学生借阅图书情况统计图2．喜欢借阅 ___图书的人数最多喜欢借阅 ___图书的人数最少3．若每人都只借阅一本图书则四（1）班一共有 ___人4．为了推进＂书香校园＂建设学校打算购买一批图书应该多购买 ___图书若将其他年级的学生也纳入调查 ___ （填＂会＂或＂不会＂）影响调查结果理由是 ___（1）（2）根据统计表喜欢借阅文学类图书的人数最多喜欢借阅艺术类图书的人数最少（3）若每人都只借阅一本图书则四（1）班一共有 18 + 6 + 9 + 15 = 48 人（4）为了推进＂书香校园＂建设学校应该多购买文学类图书因为文学类图书的借阅人数最多若将其他年级的学生也纳入调查会影响调查结果因为其他年级学生的借阅偏好可能与四（1）班不同从而影响整体的调查结果", "output": "小学数学新知识树\n统计和概率\n统计\n统计图\n1格表示多个单位的单式条形统计图\n以一当多的条形统计图"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "红军小学 670 名师生去研学每辆大巴车限坐 50 人需要准备多少辆大巴车右边坚式中箭头所指的＂17＂表示（ ） ① 坐满 13 辆还剩 17 人② 坐满 10 辆还剩 17 人③ 坐满 13 辆还剩 170 人④ 坐满 10 辆还剩 170 人本题可根据除法竖式中各部分的含义结合题目条件来分析箭头所指数字的意义1、分析除法竖式的计算过程在670÷50的竖式计算中除数是50被除数是670先去掉被除数和除数末尾的0看被除数的前两位6767 ÷ 5 = 1 …… 17 这里的1在十位上表示10实际上50×10=500670-500=170竖式中箭头所指的“17”因为被除数和除数同时缩小了10倍（末尾同时去掉一个0）所以这里的17实际表示的是170它是670减去50×10的结果2、分析每个选项选项①说坐满13辆客车还剩17人与计算过程中箭头所指数字的意义不符错误选项②坐满10辆客车但剩余人数不是17人（实际是170人）错误选项③坐满13辆客车不符合因为这里是计算到十位上的1（表示10辆）错误选项④坐满10辆客车( 50×10=500 )还剩170人( 670-500=170 )与箭头所指数字实际意义（缩小10倍后显示为17实际是170）相符正确综上答案是④", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与两位数的除法\n除数是整十数的笔算除法\n用三位数除以整十数解决简单的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "右图表示小雨同学跳远双脚落地时留下的脚印应该怎样测量小雨跳远的成绩请在图中画一画并说明理由根据跳远比赛的规则跳远成绩应为从起跳线到运动员落地点最近位置的垂直距离因此需要从起跳线到离起跳线最近的脚印后沿画一条垂线测量这条垂线的长度作为跳远成绩这样可以确保测量的是最短且符合规则的距离", "output": "小学数学新知识树\n图形与几何\n平面图形\n平行与垂直的特征、性质\n过直线上或直线外一点作垂线\n点到直线的距离应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "张爷爷家有一块如右图所示的长方形土地他想在这块土地中划分出一块最大的正方形土地用作家禽养殖剩余土地四等分分别种植四种不同的农作物家禽养殖区的面积是多少公顷每种农作物的种植面积是多少公顷首先根据题目描述张爷爷家的长方形土地的长为500米宽为400米要在这块土地中划分出一块最大的正方形土地用作家禽养殖那么这个正方形的边长应该等于长方形的较短边即400米因此家禽养殖区的面积为400×400=160000平方米=16公顷接下来计算剩余土地的面积剩余土地的面积为500×400=200000-160000=40000平方米=4公顷根据题目要求剩余土地四等分分别种植四种不同的农作物因此每种农作物的种植面积为4÷4=1公顷所以家禽养殖区的面积是16公顷每种农作物的种植面积是1公顷", "output": "小学数学新知识树\n图形与几何\n测量\n常见的面积单位及换算\n面积单位间的进率及单位换算\n面积单位间的进率及换算应用\n与公顷、平方千米有关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看图想一想填一填（1）一只手有（ ） 根手指一双手有（ ） 根手指（2） 3 个小朋友的右手一共有（ ） 根手指（1）一只手有5根手指一双手有2只手一双手有 $5\\times2=10$ 根手指（2）一个右手5根手指3个小朋友的右手一共有 $5\\times3=15$ 根手指", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n5的乘法口诀及应用\n5的乘法口诀应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "有一个正方形操场四周插彩旗要使每条边都插四面彩旗（每个顶点都要插）一共需要多少面彩旗（先画一画再计算）本题可通过两种方法来计算彩旗的总数方法一先计算四条边插彩旗的总数再减去四个顶点重复计算的彩旗数已知每条边插 $4$ 面彩旗正方形有四条边则四条边一共插了 $4\\times4=16$ 面彩旗由于每个顶点的彩旗都被重复计算了一次正方形有 $4$ 个顶点所以需要减去重复的 $4$ 面彩旗即 $16-4=12$ 面方法二先不考虑四个顶点的彩旗计算每条边中间插彩旗的数量再加上四个顶点的彩旗数每条边插 $4$ 面彩旗因为每个顶点都要插所以每条边中间插彩旗的数量是 $4-2=2$ 面正方形有四条边则四条边中间一共插了 $2\\times4=8$ 面彩旗再加上四个顶点的 $4$ 面彩旗可得彩旗总数为 $8+4=12$ 面综上一共需要 $12$ 面彩旗", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n表内乘加、乘减\n2-5的乘加乘减应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小猴开生日会邀请了小熊、小狗、小猪和小鸟来做客如果每只动物分到 4 个桃子准备 18 个桃子够吗口答准备 18 个桃子够 不够 （用画＂ $\\sqrt{}$ ＂表示）小猴邀请了4只动物来做客每只动物分到4个桃子那么总共需要的桃子数量为 $4\\times4=16$ 个桃子加上小猴自己也需要4个桃子因此总共需要 $16+4=20$ 个桃子准备了18个桃子显然不够因为 $18<20$ 因此答案是不够☑", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n2、3、4的乘法口诀及应用-\n2-4乘法口诀的应用题-"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一堆糖果不足 20 粒如果平均分成 3 堆或 5 堆都剩下 1 粒这堆糖果有（ ） 粒（填所选答案序号）①25②19③16④10本题可先找出 $3$ 和 $5$ 的公倍数再结合糖果数量的条件来确定这堆糖果的数量 - 步骤一求 $3$ 和 $5$ 的公倍数因为 $3$ 和 $5$ 都是质数所以它们的最小公倍数为 $3\\times5=15$  - 步骤二根据条件确定糖果数量已知这堆糖果平均分成 $3$ 堆或 $5$ 堆都剩下 $1$ 粒所以糖果的数量是 $3$ 和 $5$ 的公倍数加 $1$ 即 $15+1=16$ 粒又因为这堆糖果不足 $20$ 粒 $16<20$ 满足条件而①选项 $25$ 粒不满足不足 $20$ 粒的条件②选项 $19$ 粒平均分成 $3$ 堆或 $5$ 堆不会都剩下 $1$ 粒④选项 $10$ 粒平均分成 $3$ 堆或 $5$ 堆也不会都剩下 $1$ 粒综上这堆糖果有 $16$ 粒答案选③", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n表内乘加、乘减\n2-5的乘加乘减应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "每只猫钓了5条鱼共有3只猫即求3个5是多少用乘法计算 $3\\times5=15$ （条）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n5的乘法口诀及应用\n5的乘法口诀应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在一个棋盘的四周摆上棋子每条边上摆 5 枚 4 个顶点上各摆 1 枚一共摆了（ ） 枚棋子解如图每条边上除过顶点有3枚共4条边除过顶点处总共棋子3×4=12枚加法上顶点的4枚棋子总数12+4=16（枚）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n表内乘加、乘减\n2-5的乘加乘减应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一家三口去湖边钓鱼儿子说我钓了 5 条爸爸说一个鱼桶能装 20 条鱼妈妈说我们三人钓的同样多他们一共钓了多少条鱼口答他们一共钓了 条鱼根据题目儿子钓了5条鱼妈妈说他们三人钓的同样多即爸爸和妈妈也各钓了5条鱼因此他们一共钓的鱼的数量为 $5+5+5=15$ 条", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n5的乘法口诀及应用\n5的乘法口诀应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "5 个角有（ ） 个顶点（ ） 条边用到的乘法口诀是（ ） 和（ ） 一个角有 1 个顶点和 2 条边求 5 个角有几个顶点就是求 5 个 1 是多少用乘法计算1×5 = 5乘法口诀是一五得五求 5 个角有几条边就是求 5 个 2 是多少用乘法计算2×5 = 10乘法口诀是二五一十", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n5的乘法口诀及应用\n5的乘法口诀应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "哪种分法对在对的 $\\square$ 里画＂ $\\sqrt{}$ ＂（1） 9 个橘子平均分成 3 盘 （2）8朵花平均分成2份解（1）图中都分成了3盘题中要求平均分每个盘子中的橘子数要一样多满足条件的是第2组（2）题中要求分成2份满足条件的是第2第3组还要满足平均分所以每盘中的花朵需一样多满足条件的是第3组", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n整数平均分的意义及应用\n理解平均分"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "画一画在小动物下面用＂○＂表示相应分得的食物再填一填一共有（ ） 个 平均分给（ ） 只猴子每只猴子分得（ ） 个（2）把这些乏平均分给（ ） 只小狗每只小狗分到（ ） 根（1）图中有8个桃子4只猴子将8个桃子平均分给4只猴子每只猴子分得 $\\frac{8}{4}=2$ 个桃子（2）图中有12根骨头2只小狗将12根骨头平均分给2只小狗每只小狗分得 $\\frac{12}{2}=6$ 根骨头", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n整数平均分的意义及应用\n理解平均分"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据算式 $20\\div4=5$ 画一幅图或编一道数学题略", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n除法的初步认识\n初步认识除法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "按照算式圈一圈填一填（1） $15\\div3=$ 我是把这些平均分成（ ） 份每份圈（ ） 个（2） $24\\div6=$ 我是把这些每（ ） 个圈一份圈了（ ） 份（1） $15\\div3=5$ 表示把15个星星平均分成3份每份圈5个（2） $24\\div6=4$ 表示把24个梨每6个圈一份圈了4份", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n除法的初步认识\n初步认识除法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "帮小朋友写出除法算式 —————————————— —————————————————————————————————— ——————————————————", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n除法的初步认识\n除法意义的理解与运用（移后删）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李老师有一根长 8 米的绸带做一件教具要用 2 米（1）一共可以做（ ） 件这样的教具___（2）一段一段地剪一共要剪（ ） 次（用减法试试）___（1）已知绸带总长 8 米做一件教具用 2 米求能做几件教具就是求 8 里面有几个 2用除法计算 $8\\div2=4$ （件）（2）因为剪的次数比段数少 1由（1）可知能做成 4 件教具也就是能剪成 4 段用减法来想剪 1 次是 2 段剪 2 次是 3 段剪 3 次是 4 段所以一共要剪 3 次", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n用2-6的乘法口诀求商\n连减与除法(2-6)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "想一想填一填如果每只小兔分 4 根萝卜可以分给 只小兔12---=12÷=想4和几相乘得 12 （ ）四十二商是（ ） 题目中提到有12根萝卜每只小兔分4根萝卜首先计算可以分给几只小兔 $12\\div4=3$ 因此可以分给3只小兔然后根据减法验证 $12-4-4-4=0$ 即分给3只小兔后没有剩余最后根据乘法口诀“三四十二”确认商是3", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n用2-6的乘法口诀求商\n连减与除法(2-6)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看图做一做（1）3 只小松鼠一起运松果一次运完平均每只运几个（2）如果小松鼠在路上吃掉了 6 个松果运到的松果有多少个（1）图中有 15 个松果3 只小松鼠一起运平均每只小松鼠运的松果数为 15 ÷ 3 = 5（个）（2）原来有 15 个松果小松鼠吃掉了 6 个运到的松果数为 15 - 6 = 9（个）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n乘除法解决问题(表内)\n列多个算式的应用题（2-6含除法）-"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "剪窗花时每张纸可以剪 2 个窗花（1）5 张纸可以剪（ ） 个窗花（2）宁宁要剪 12 个窗花需要用（ ）张纸（1）每张纸可以剪 2 个窗花5 张纸可以剪 $5\\times2=10$ 个窗花（2）宁宁要剪 12 个窗花需要用 $12\\div2=6$ 张纸", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n乘除法解决问题(表内)\n列多个算式的应用题（2-6含除法）-"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "买文具［第（2）小题可以只填写序号］①圆珠笔 ②笔盒 ③笔记本 ④水彩笔 3元 6 元 4 元 5 元（1） 20 元钱能买几筒水彩笔口答能买 ___筒水彩笔（2） 13 元最多能买几件不同的物品口答 ___件分别是 ___（1）20元钱能买几筒水彩笔解析每筒水彩笔5元20元可以买 $20\\div5=4$ 筒水彩笔（2）13元最多能买几件不同的物品解析要买最多的不同物品应选择价格较低的物品①圆珠笔3元③笔记本4元④水彩笔5元这三件物品总价为 $3+4+5=12$ 元不超过13元因此最多能买3件不同的物品分别是①③④", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n乘除法解决问题(表内)\n列多个算式的应用题（2-6含除法）-"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "观察下面右侧学校地图读一读填一填同学们好现在请跟着我一起来参观我们的学校进入大门以后向西走就是科技楼然后向北走是（ ） 最后我们回到存车处它的（ ） 边就是大门了根据学校地图进入大门后向西走到达科技楼然后向北走会到达（体育馆）最后回到存车处观察地图可以发现存车处的（西边）是大门", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n四个方向描述简单的行走路线"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "教学楼在操场的北面看图填一填（1）体育馆在操场的（ ） 面操场在图书馆的（ ） 面图书馆在体育馆的（ ） 面（2）大门在教学楼的（ ） 面教学楼在大门的（ ） 面（1）根据图示体育馆在操场的东面操场在图书馆的东面图书馆在体育馆的西面（2）大门在教学楼的南面教学楼在大门的北面", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n在平面图上辨认东、南、西、北"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "早上起来面向太阳（1）前面是（ ） 后面是（ ） 左面是（ ） 右面是（ ） （2）东与（ ） 相对（ ） 与北相对（3）如果向后转那么左面是（ ） 右面是（ ） 与面向太阳的人的左、右的方向（ ） （选填＂一样＂不一样＂）（1）早上太阳从东方升起当我们面向太阳时前面就是东因为东和西相对所以后面是西再根据“上北下南左西右东”当面向东时左面是北右面是南（2）在方向中东和西是相对的方向南和北是相对的方向（3）开始面向东向后转就变成面向西了此时根据“上北下南左西右东”面向西时左面是南右面是北开始面向东时左面是北右面是南和现在面向西时左、右方向不一样", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n辨认东、西、南、北四个方向"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "早上起来面向太阳（1）前面是（ ） 后面是（ ） 左面是（ ） 右面是（ ） （2）东与（ ） 相对（ ） 与北相对（3）如果向后转那么左面是（ ） 右面是（ ） 与面向太阳的人的左、右的方向（ ） （选填＂一样＂不一样＂）（1）早上太阳从东方升起当我们面向太阳时前面就是东因为东和西相对所以后面是西再根据“上北下南左西右东”当面向东时左面是北右面是南（2）在方向中东和西是相对的方向南和北是相对的方向（3）开始面向东向后转就变成面向西了此时根据“上北下南左西右东”面向西时左面是南右面是北开始面向东时左面是北右面是南和现在面向西时左、右方向不一样", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n相对方向(四个方向)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "早上起来面向太阳（1）前面是（ ） 后面是（ ） 左面是（ ） 右面是（ ） （2）东与（ ） 相对（ ） 与北相对（3）如果向后转那么左面是（ ） 右面是（ ） 与面向太阳的人的左、右的方向（ ） （选填＂一样＂不一样＂）（1）早上太阳从东方升起当我们面向太阳时前面就是东因为东和西相对所以后面是西再根据“上北下南左西右东”当面向东时左面是北右面是南（2）在方向中东和西是相对的方向南和北是相对的方向（3）开始面向东向后转就变成面向西了此时根据“上北下南左西右东”面向西时左面是南右面是北开始面向东时左面是北右面是南和现在面向西时左、右方向不一样", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n方向变化推理题（四个方向）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "我国有五座名山合称五岳它们分别是东岳泰山、西岳华山、南岳衡山、北岳恒山、中岳嵩山请在第 2 题图中相应的括号里填上它们的名字略", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n根据描述确定位置（四个方向）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "地图通常是按＂上北、下南、左西、右东＂绘制的（1）把这种情况标在右边的方向牌上（2）右图中箭头指的方向与时针转动的方向（ ） （选填＂一样＂＂不一样＂）（3）按右图箭头所指的方向从＂东＂开始依次是东→（ ）→（ ）→（ ）（1）根据题目要求地图通常是按＂上北、下南、左西、右东＂绘制的因此在方向牌上标示为上-北下-南左-西右-东（2）观察右图中箭头指的方向与时针转动的方向进行比较时针转动的方向是顺时针即从上到右到下到左而图中的箭头方向是从上到左到下到右与顺时针方向相反因此答案为“不一样”（3）按右图箭头所指的方向从＂东＂开始依次是东→北→西→南", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n辨认东、西、南、北四个方向"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "选一选下面哪种测量方法是正确的在括号内画＂ $\\sqrt{}$ ＂第一张图中物体没有沿着刻度尺的边缘放置第二张图片中物体的一端没有对准刻度尺的0刻度线第三张图片中物体的一端对准了刻度尺的0刻度线并且物体沿着刻度尺的边缘放置因此测量方法正确", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度的测量方法\n直尺测量（厘米）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填上合适的长度单位 树高 12（ ） 跳绳长 3（ ） 身高 120（ ） 门宽 80（ ）解一层楼的高度大约3米一棵树大约有4层楼的高度所以填12米二年级学生两臂伸长大约是1米多跳绳长大约有2个多的两臂长所以填3米二年级学生的身高大约1米多多些1米=100厘米也就是比100厘米多一些所以填120厘米门宽没有两臂伸长的长度长所以填80厘米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(米和厘米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面是福州地铁 1 号线部分站点示意图地铁从福州火车站出发可以有多少种卖票方式从福州火车站出发可以到达的站点有斗门、树兜、屏山、东街口共4个站点因此有4种卖票方式", "output": "小学数学新知识树\n图形与几何\n平面图形\n直线、射线、线段\n线段的初步认识\n认识线段"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数一数下面的图形中各有几条线段（ ）条 （ ）条 （ ）条 （ ）条略", "output": "小学数学新知识树\n图形与几何\n平面图形\n直线、射线、线段\n线段的初步认识\n认识线段"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "想一想是线段的在括号里画＂ $\\sqrt{}$ ＂（ ） （ ） （ ） （ ） （ ） （ ）略", "output": "小学数学新知识树\n图形与几何\n平面图形\n直线、射线、线段\n线段的初步认识\n认识线段"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据实际情况填＂m＂或＂cm＂ 运动鞋鞋底厚3（ ） 门高2（ ） 小朋友高120（ ）略", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(米和厘米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "测量小高手（1）根据橡皮擦的长度填写尺子上的刻度再写出另两件物品的长度长（2）厘米长（ ）厘米 长（ ）厘米（2）小西说＂测量长度一定要把被测量物的一端对齐尺子 0 刻度线＂小东说＂也可以对齐尺子其他刻度线＂你觉得他们谁说得对请举例说明理由————————————————————————————————————————————————————————————————————————————————————————————————————解（1）橡皮长度是2厘米可以将直至上的刻度补充完整钢笔长度=15厘米-4厘米=11厘米铅笔长度=8厘米-3厘米=5厘米( 2 )由上面计算可知小东说得对测量长度时不一定要从尺子0刻度线处对齐被测量物开始测量如果从尺子的其他刻度数开始测量 只要用末端的刻度数减去起始的刻度 数二者的差就是被测量物的长度", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度的测量方法\n直尺测量（厘米）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "从 48 里连续减去 6 连续减（ ） 次后得数是 0 要知道从 48 里连续减去 6 减几次后得数是 0 就是看 48 里面有几个 6 二年级上学期学过乘法口诀因为六八四十八也就是 6×8 = 48 所以 48 里面有 8 个 6 那么从 48 里连续减去 6 连续减 8 次后得数是 0 ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n用7~9的乘法口诀求商\n7-9连减与除法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "利嘉超市饮料促销降价后买 6 瓶可乐 36 元钱买 9 瓶要多少钱口答买 9 瓶要 元首先我们来计算每瓶可乐的价格买 6 瓶可乐需要 36 元那么每瓶可乐的价格为 $36\\div6=6$ 元接下来我们计算买 9 瓶可乐需要多少钱每瓶可乐 6 元因此 9 瓶可乐的总价为 $6\\times9=54$ 元所以买 9 瓶可乐需要 54 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n乘除法解决问题(表内)\n乘除法的应用题(表内)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "仔细阅读停车场收费标准回答问题（1） 7 辆小汽车 1 小时收费多少元口答7辆小汽车 1 小时收费 □元（2） 6 辆货车 1 小时收费多少元口答6辆货车 1 小时收费 □元（1）根据收费标准小汽车每小时收费5元因此7辆小汽车1小时的收费为 $7\\times5=35$ 元（2）根据收费标准货车每小时收费8元因此6辆货车1小时的收费为 $6\\times8=48$ 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n6的乘法口诀及应用\n6的乘法口诀应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "仔细阅读说明书列式计算回答问题（1）这种药一天共要口服 粒————————————————————————————（2）一天口服的药相当于 克原生药————————————————————————————（1）根据用法说明一次口服8粒一日3次因此一天共要口服的药粒数为 $8\\times3=24$ 粒（2）每8粒相当于原生药5克一天口服24粒相当于原生药的量为 $24\\div8\\times5=3\\times5=15$ 克", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n5的乘法口诀及应用\n5的乘法口诀应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "树叶向北摆动说明起的是（ ）A．东风B．南风C．西风D．北风解吹风的方向和树叶摆动的方向是相反的现在树叶向北摆动那就说明风是从南边吹来的从南边吹来的风就叫南风所以答案选B", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n相对方向(四个方向)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "学校在喜羊羊家的东面喜羊羊放学回家应该（ ） A．从东往西走B．从西往东走C．从北往南走解根据方向的相对性喜羊羊去学校时往东走放学时会往西走选项A正确", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n相对方向(四个方向)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小华站在操场上面朝西如果他向右转那么背朝（ ） 解如图原来面向西向右转后面向北与北相对的方向是南", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n方向变化推理题（四个方向）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "早晨你面向太阳后边是（ ） 右边是（ ） 解当我们面向太阳时前面就是东那和东相反的方向就是西所以后边是西按照“上北下南左西右东”的方位规则右边就是南", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n辨认东、西、南、北四个方向"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "从东这个方向开始按顺时针方向移动依次经过（ ） 、（ ） 、 （ ） 解方向是按照一定顺序排列的从东开始按顺时针方向转动接下来就是南再继续转就是西最后是北所以依次经过南、西、北", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n辨认东、西、南、北四个方向"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "太阳总是从（ ） 方升起在（ ） 方落下（ ）A．西东B．南北C．东西略", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n辨认东、西、南、北四个方向"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "根据右图填空医院在乐乐家的（ ） 面超市在乐乐家的（ ） 面学校在乐乐家的（ ） 面解以乐乐家为中心医院在乐乐家的左边即西面超市在乐乐家的下面即南面学校在乐乐家的右边即东面", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东、南、西、北方向\n在平面图上辨认东、南、西、北"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在括号里填上cm＂或＂m＂一棵树高7（ ） 围巾长80（ ）跳绳长1（ ） 60（ ） 杯子高15（ ）解一层楼的高度大于3米1棵数有3层楼左右的高度所以填“米”一个小学生的身高大概120cm左右围巾的长度略小于这个值所以填“厘米”一根跳绳的长度在2米左右与其比较所以跳绳长1米60厘米一把小学生的尺子的量程是20厘米与其比较可知杯子的高度是15厘米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(米和厘米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "从公园回家明明先向南走再向东走到家丽丽先向北走再向西走到家芳芳先向北走再向东走到家东东先向西走再向南走到家请你在下图中标出他们各自的家根据题目描述1. 明明先向南走再向东走到家因此明明的家在公园的东南方向即左上角的房子2. 丽丽先向北走再向西走到家因此丽丽的家在公园的西北方向即右上角的房子3. 芳芳先向北走再向东走到家因此芳芳的家在公园的东北方向即右下角的房子4. 东东先向西走再向南走到家因此东东的家在公园的西南方向即左下角的房子", "output": "小学数学新知识树\n图形与几何\n位置与方向\n方向\n东南、西南、东北、西北方向\n根据描述确定位置（八个方向）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "上图中粉笔长（ ） 厘米铅笔长（ ） 厘米铅笔比粉笔长（ ） 厘米解图中粉笔一端与0刻线对齐一端与5厘米对齐因此粉笔长 $5$ 厘米铅笔的一端在3厘米处一端在10厘米处因此铅笔长 $10$ 厘米-3厘米=7厘米铅笔比粉笔长7厘米-5厘米=2厘米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度的测量方法\n直尺测量（厘米）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在括号里填上＂米＂或＂厘米＂书桌高大约70（ ） 学校的跑道长200（ ）椰子树高约 5 （ ） 铅笔长约 18（ ）解两个手臂伸直的长度大约是1米左右书桌高度小于两臂伸直长度所以应是70厘米一步的长度大约在50厘米左右小于1米所以跑道长应是200米一层楼的高度大约在3米左右所以数的高度应是5米一拃的长度在20厘米左右所以铅笔长应是18厘米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(米和厘米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一把米尺长 100（ ） ①米②元③厘米解②不是长度单位排除①100米比3层楼的高度还要高不合适③100厘米=1米大约是两臂伸长的长度合适", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(米和厘米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "每只小猫钓 6 条鱼 8 只小猫钓鱼的条数比 50 条（ ） ①多②少③一样多解 $6×8=48$ （条）因为 $48＜50$ 所以8只小猫钓鱼的条数比50条少选②", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n6的乘法口诀及应用\n6的乘法口诀应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "二（2）班有 23 名男同学 29 名女同学他们和 2 位老师一起去参加春季研学活动他们乘坐（ ） 号车最合适①限乘客 45 人②限乘客 52 人③限乘客 60 人解二（2）班总人数23+29+2=54（人）45＜54①不合适52＜54②不合适60＞54可以坐下所有人③合适", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n100以内数的加法、减法\n100以内数的连加运算\n连加解决实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "幼儿园小班有 28 人老师奖励每个小朋友一颗五角星右面这些五角星够吗口答够 不够 （在正确答案的方框里画＂ $\\sqrt{}$ ＂）图片中有 5 行五角星每行有 5 颗共有 $5\\times5=25$ 颗五角星幼儿园小班有 28 人需要 28 颗五角星因为 25 颗五角星少于 28 颗所以这些五角星不够但是题目要求在正确答案的方框里画“√”正确答案应该是“不够”", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内乘法\n6的乘法口诀及应用\n6的乘法口诀应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "把两根长为 5 米的木板接起来捆在一起的部分为 1 米接好后的木板长为多少米口答接好后的木板长为 米两根木板的总长度为 $5+5=10$ 米捆在一起的部分为 1 米这部分被重复计算了一次因此需要减去一次即 $10-1=9$ 米所以接好后的木板长为 9 米", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n加减混合运算（10以内）\n加减混合运用(10以内加减混合)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "我国是一个多民族国家汉族人口最多其他为少数民族蒙古族和维吾尔族的总人口数约是 1810 万其中蒙古族的人口数约是维吾尔族的 $\\frac{63}{118}$ 蒙古族和维吾尔族的人口数分别约是多少万（列方程解答）设维吾尔族人口数约是 x 万则蒙古族人口数约是 $\\frac{63}{118}x$ 万可列方程 $x+\\frac{63}{118}x=1810$ 解得 $x=1180$ 蒙古族 $\\frac{63}{118}×1180=630$ （万）答蒙古族人口数约是630万维吾尔族人口数约是 1180 万", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n和差倍问题\n分数除法之和倍、差倍问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "我国是一个多民族国家汉族人口最多其他为少数民族蒙古族和维吾尔族的总人口数约是 1810 万其中蒙古族的人口数约是维吾尔族的 $\\frac{63}{118}$ 蒙古族和维吾尔族的人口数分别约是多少万（列方程解答）设维吾尔族人口数约是 x 万则蒙古族人口数约是 $\\frac{63}{118}x$ 万可列方程 $x+\\frac{63}{118}x=1810$ 解得 $x=1180$ 蒙古族 $\\frac{63}{118}×1180=630$ （万）答蒙古族人口数约是630万维吾尔族人口数约是 1180 万", "output": "小学数学新知识树\n数与代数\n式与方程\n方程的解与解方程\n解分数方程\n分数方程解决实际应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "商高是最早发现“勾股定理”的人他提出了“勾三股四弦五”的说法如果一个直角三角形的短直角边（勾）长是 3 长直角边（股）长是 4 那么斜边（弦）长一定是 5 也就是勾股弦 $=3$ $45$ 用一根长 72 厘米的铁丝围成一个这样的直角三角形它的三边长度分别是多少勾占周长的比例 $3\\div( 3+4+5 )=\\frac{3}{12}$ 股占周长的比例 $4\\div( 3+4+5 )=\\frac{4}{12}$ 弦占周长的比例 $5\\div( 3+4+5 )=\\frac{5}{12}$ 因为这根铁丝长 72 厘米也就是这个直角三角形的周长是 72 厘米所以勾的长度 $72\\times\\frac{3}{12}=18$ （厘米）股的长度 $72\\times\\frac{4}{12}=24$ （厘米）弦的长度 $72\\times\\frac{5}{12}=30$ （厘米）所以这个直角三角形三边的长度分别是 18 厘米、24 厘米、30 厘米", "output": "小学数学新知识树\n数与代数\n比和比例\n比\n按比分配问题\n已知总量和分量比，求分量"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一条彩带用去 15 厘米后还剩 35 厘米这条彩带原来长多少厘米合多少分米要求彩带原来的长度我们把用去的长度和剩下的长度加起来就行用去 15 厘米剩下 35 厘米所以原来长 15 + 35 = 50 厘米因为 1 分米等于 10 厘米50 厘米里面有 5 个 10 厘米所以 50 厘米合 5 分米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n解决有关分米、厘米、毫米的问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息实践操作你知道自己走一步有多长吗2、请回答（1）读一读填一填\n|   |   |\n| --- | --- |\n| ＂一步长＂测量方法第一步：正常行走 10 步，然后在第 10 步时停住。第二步：使用测量工具（如卷尺）测量相邻两脚脚尖或两脚脚后跟之间的距离。第三步：测得的距离即为一步长。 |   |\n结合阅读观察图示一步长可测量（        ） 或（        ） （填序号）根据“一步长”测量方法可知要测量相邻两脚脚尖或两脚脚后跟之间的距离图中①是相邻两脚脚尖之间的距离③是相邻两脚脚后跟之间的距离所以一步长可测量①或③", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度的测量方法\n测量方式"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李叔叔是一名骑行爱好者他计划周六上午从甲地骑行到乙地全程 25 千米如果他每分钟骑行 200 米上午 $730$ 出发 $930$ 能否到达请说明理由本题可先计算出从上午730到930经过的时间再根据路程 = 速度×时间计算出这段时间骑行的路程最后将骑行的路程与甲地到乙地的全程进行比较判断能否到达步骤一计算从上午730到930经过的时间用结束时间减去开始时间可得9时30分 - 7时30分 = 2小时因为1小时 = 60分钟所以2小时换算为分钟是2×60 = 120分钟步骤二计算120分钟骑行的路程已知李叔叔每分钟骑行200米根据路程 = 速度×时间可得120分钟骑行的路程为200×120 = 24000米因为1千米 = 1000米所以24000米换算为千米是24000÷1000 = 24千米步骤三比较骑行的路程与全程的大小已知甲地到乙地全程25千米而李叔叔120分钟骑行的路程是24千米因为24千米 < 25千米所以李叔叔在930不能到达乙地", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n解决千米相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息看图解答问题2、请回答（3）欣欣走 100 米大约 200 步她从家到学校大约走多少步欣欣从家到学校的距离是400米已知她走100米大约需要200步400米中有4个100米所以欣欣需要走4个200步即 $200\\times4=800$ 步因此欣欣从家到学校大约走800步", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n解决千米相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息看图解答问题2、请回答（1）欣欣家—小丽家—图书馆这条路长多少米合多少千米（1）欣欣家到小丽家的距离是680米小丽家到图书馆的距离是320米将这两段距离相加680 + 320 = 1000（米）因为1000米等于1千米所以这条路长1000米合1千米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n解决千米相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息李师傅把一根 3 米长的木料锯成每段长 5 分米的小段每锯 1 次需 4 分钟2、请回答（2）按照这样的速度多少分钟可以锯完已知每锯 1 次需 4 分钟那么锯 5 次需要的时间是 5×4 = 20 分钟", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n解决有关分米、厘米、毫米的问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息看图解答问题2、请回答（4）小丽每分钟走 70 米每天上午必须在 755 之前到校她最迟什么时候应从家里出发小丽家到学校的距离是350米她每分钟走70米先计算她从家到学校需要的时间350 ÷ 70 = 5（分钟）她必须在755之前到校所以最迟出发时间是755 - 5分钟 = 750因此她最迟应在750出发", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n解决千米相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "亮亮用一根 2 米长的红绸缎先剪下 8 分米做大中国结剩下的做小中国结扎一个小中国结需要 4 分米剩下的红绸缎可以扎多少个小中国结本题可先将单位统一再计算出做大中国结后剩余红绸缎的长度最后用剩余红绸缎的长度除以扎一个小中国结所需红绸缎的长度即可得到能扎小中国结的个数因为 $1$ 米 $=10$ 分米所以 $2$ 米换算成分米为 $2\\times10=20$ 分米已知红绸缎总长 $20$ 分米剪下 $8$ 分米做大中国结则剩余的长度为 $20-8=12$ 分米已知扎一个小中国结需要 $4$ 分米红绸缎剩余红绸缎为 $12$ 分米则可扎小中国结的个数为 $12\\div4=3$ 个", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n解决有关分米、厘米、毫米的问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填上合适的单位（1）一张银行卡的厚度大约 1（        ） （2）一支铅笔大约长 18（        ）（3）李阿姨的身高是 165（        ）  （4）一张床长 2（        ） （1）银行卡很薄在长度单位中毫米是比较小的长度单位1 厘米 = 10 毫米而银行卡的厚度用厘米来衡量太大了所以一张银行卡的厚度大约 1 毫米（2）铅笔的长度一般在十几厘米到二十几厘米左右如果用分米作单位1 分米 = 10 厘米一支铅笔大约长 18 分米就是 180 厘米这太长了如果用毫米作单位18 毫米 = 1.8 厘米又太短了所以一支铅笔大约长 18 厘米（3）人的身高通常在一百多厘米如果用米作单位1 米 = 100 厘米165 米太高了不符合实际如果用分米作单位165 分米 = 16.5 米也不符合实际所以李阿姨的身高是 165 厘米（4）床的长度一般是两米左右如果用分米作单位2 分米 = 20 厘米太短了不符合床的实际长度如果用厘米作单位2 厘米就更短了所以一张床长 2 米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(千米、米、厘米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息看图解答问题2、请回答（2）从学校到图书馆哪条路最近是多少米从学校到图书馆的路有两条第一条可以经过公园到达图书馆学校 → 公园 → 图书馆总路程为400 + 300 = 700（米）第二条可以经过公园到达图书馆学校 → 小丽家 → 图书馆总路程为350 + 320 = 670（米）因为700米 > 670米所以从学校到图书馆经过小丽家的路线更短", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n解决千米相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在 $\\bigcirc$ 里填上＂ $>$ ＂＂ $<$ ＂或＂ $=$ ＂3 厘米 $\\bigcirc$ 32 毫米 9 分米 $\\bigcirc$ 90 厘米 43 厘米－40 厘米 $\\bigcirc$ 3 毫米本题可先将两边的长度单位统一再比较大小1. 比较 3 厘米和 32 毫米的大小因为 1 厘米 = 10 毫米所以 3 厘米换算成毫米为3×10 = 30 毫米由于 30 毫米＜32 毫米所以 3 厘米＜32 毫米2. 比较 9 分米和 90 厘米的大小因为 1 分米 = 10 厘米所以 9 分米换算成厘米为9×10 = 90 厘米因此 9 分米 = 90 厘米3. 比较 43 厘米－40 厘米和 3 毫米的大小先计算 43 厘米－40 厘米 = 3 厘米再将 3 厘米换算成毫米因为 1 厘米 = 10 毫米所以 3 厘米 = 3×10 = 30 毫米即 43 厘米－40 厘米 = 30 毫米 由于 30 毫米＞3 毫米所以 43 厘米－40 厘米＞3 毫米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n分米、厘米和毫米之间的单位计算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填上合适的单位上海环球金融中心高约492（      ）动车每小时行驶185（      ）厦门演武大桥全长约2（      ）宽26（      ）根据题目中的图片和实际生活常识我们来分析每个空格应填的单位1.上海环球金融中心是一座高楼其高度通常用“米”作单位492米是合理的高度因此第一个空填“米”2. 动车的速度较快每小时行驶的距离一般用“千米”作单位185千米/小时是动车常见的速度因此第二个空填“千米”3. 厦门演武大桥是一条跨海大桥全长较长通常用“千米”作单位2千米是合理的长度桥的宽度较窄一般用“米”作单位26米是合理的宽度因此第三个空填“千米”第四个空填“米”", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(千米、米、厘米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填一填（1） 3 千米＝（        ） 米         6000 米＝（        ） 千米         1200 米 +800 米＝（        ） 千米（2）希望小学操场的跑道1圈是200米（        ） 就是1千米（3）亮亮家到学校的距离大约是 500 米他每天上学要来回走 2 趟大约需要走 （        ） 米合（        ） 千米（1）因为1千米 = 1000米所以把千米换算成米要乘1000把米换算成千米要除以10003千米换算成米 $3×1000=3000$ 米所以3千米 = 3000米6000米换算成千米 $6000÷1000=6$ 千米所以6000米 = 6千米先计算1200米 + 800米=2000米再把2000米换算成千米 $2000÷1000=2$ 千米所以1200米 + 800米 = 2千米（2）因为1千米 = 1000米跑道1圈是200米求几圈是1千米就是求1000米里面有几个200米用除法计算 $1000÷200=5$ 圈所以5圈就是1千米（3）亮亮每天上学来回走2趟1趟是一来一去也就是 $2$ 个500米2趟就是 $2×2=4$ 个500米那么他走的总路程是 $500×4=2000$ 米再把2000米换算成千米 $2000÷1000=2$ 千米所以大约需要走2000米合2千米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n千米与米的换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息实践操作你知道自己走一步有多长吗2、请回答（2）结合实际算一算①你的一步长大约（        ） 厘米②沿教室的长和宽走走长大约是（        ）宽大约是（           ）③绕学校 200 米操场走 1 圈大约（        ） 步（           ） 步大约是 1 千米④用电子导航工具搜索你家到学校的距离大约是（            ） 如果选择步行大约需要（            ） 分钟大约（            ） 步本题需要结合实际情况进行测量和计算", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度的测量方法\n测量方式"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填上合适的单位1枚1元钱硬币重约6（        ）           一袋大米重5（        ） 一只鸭大约重 3（        ）                  一个西红柿大约重 180（        ） 在生活中我们知道比较轻的物体常用克作单位比较重的物体常用千克作单位1枚1元钱硬币很轻所以重约6克一袋大米比较重重5千克一只鸭有一定重量大约重3千克一个西红柿相对较轻大约重180克", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量单位的选择\n填质量单位(克和千克)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "请动手称出重为＂1克＂的物体并记录数据1 克黄豆大约有（        ） 粒（        ） 个回形针大约有 1 克同学们可以用天平来称出1克的物体经过实际称量我们会发现1克黄豆大约有7粒1个回形针大约有1克这是通过实际动手操作得到的数据哦", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量及质量的常用单位\n克的认识\n认识克"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填一填3000 千克 =（        ） 吨 $\\quad6000$ 克 =（        ） 千克 9 吨— 2000 千克 =（        ） 吨①因为 1000 千克是 1 吨3000 千克里面有 3 个 1000 千克所以 3000 千克 = 3 吨②由于 1000 克是 1 千克6000 克里面有 6 个 1000 克所以 6000 克 = 6 千克③先把 2000 千克换算成吨因为 1000 千克 = 1 吨所以 2000 千克 = 2 吨那么 9 吨 - 2000 千克 = 9 吨 - 2 吨 = 7 吨", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量单位的换算\n吨、千克之间的换算与比较\n吨、千克和克之间的进率及换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "单位换算60千克=（        ）克 1850=（        ）千克（        ）克3000克=（        ）千克 4千克50克=（        ）克因为1千克 = 1000克①60千克=60×1000 = 60000克② - 1000克 = 1千克1850克里面有1个1000克和850克所以1850克 = 1千克850克③ - 把克换算成千克就是看3000里有几个1000用除法计算3000÷1000 = 3千克④ - 先把4千克换算成克4×1000 = 4000克再加上50克4000 + 50 = 4050克", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量单位的换算\n克、千克之间的换算与比较\n千克和克之间的进率及换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "解决问题（1）一批钢铁重 6000 千克用载质量 3 吨的货车运几次能运完（2）算一算这辆车是否超载（1）因为 1 吨 = 1000 千克所以 3 吨 = 3000 千克钢铁总重是 6000 千克每次最多运 3000 千克计算需要运几次6000 ÷ 3000 = 2（次）所以2 次能运完（2）观察图片中货物的重量800 千克 + 700 千克 + 900 千克 + 800 千克 = 3200 千克货车限载 3 吨即 3000 千克因为 3200 千克 > 3000 千克所以这辆车超载了", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量间的计算和应用\n解决吨相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "解决问题（1）妈妈到超市买 2 千克圣女果和 500 克葡萄带 50 元够吗（2）请再提出一个数学问题并解答（1）首先看圣女果的价格是8元/500克2千克等于2000克也就是4个500克所以圣女果的总价是8×4=32（元）葡萄是20元/500克买500克就是20元总共花费32+20=52（元）妈妈带了50元52＞50所以不够（2）提出问题买1千克胡萝卜需要多少钱胡萝卜价格是4元/500克1千克=1000克1000÷500=2所以是2个500克价格为4×2=8元因此买1千克胡萝卜需要8元", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量间的计算和应用\n千克与克的相关应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "游泳能增强心肺功能李亮在暑假期间报名参加了一对一私教课程如果每次 300 元买 5 次送 1 次他两周 6 次就学会了游泳需花多少钱因为买 5 次送 1 次李亮学了 6 次刚好是买 5 次送 1 次的情况每次课程 300 元所以他需要花费的钱数是 5 次课程的费用即 300×5 = 1500 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n运用口算乘法解决实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "每盒有500张纸所以4盒纸的总张数是 $500\\times4=2000$ （张）如果再加半盒（250张）则总张数为 $2000+250=2250$ （张）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n运用口算乘法解决实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如图买 3 张桌子和 3 把椅子一共要花多少钱（想一想怎样计算更简便）我们可以先算出一张桌子和一把椅子的总价再乘以3一张桌子140元一把椅子60元所以一张桌子和一把椅子一共是140 + 60 = 200（元）买3套这样的桌椅就是200 × 3 = 600（元）这样计算更简便因为先合并单价再乘数量", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n运用口算乘法解决实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "整十数乘一位数且积是 240 的乘法算式你能写出多少个写一写我们要找整十数乘一位数积是 240 的算式可以先想乘法口诀三八二十四四六二十四当是三八二十四时写成整十数乘一位数的算式就是 30×8 = 240 和 80×3 = 240当是四六二十四时写成整十数乘一位数的算式就是 40×6 = 240 和 60×4 = 240所以一共有 4 个这样的算式", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "算一算比一比（得数填在括号里）首先根据图中提示左边女孩在计算 $3\\times31$ 右边男孩在计算 $4\\times31$ 中间一排数字是需要分别乘以3和4的数31、40、22、32、10、30我们逐个计算 $3\\times31=93$ $3\\times40=120$ $3\\times22=66$ $3\\times32=96$ $3\\times10=30$ $3\\times30=90$ $4\\times31=124$ $4\\times40=160$ $4\\times22=88$ $4\\times31=930$ $4\\times31=931$ $4\\times31=932$ 将结果填入括号中上排为乘以3的结果下排为乘以4的结果", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两位数与一位数的乘法口算\n两位数乘一位数的口算(不进位)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算6×50+5=                                40×4-10=                             8×8+6=( 374-274 )×3=                        80×9-220=                          200×5+1960=1. 计算6×50 + 5 - 先算乘法6×50 = 300再算加法50+5=                                4002. 计算40×50+5=                                401 - 先算乘法40×50+5=                                402再算减法50+5=                                4033. 计算8×50+5=                                404 - 先算乘法8×50+5=                                405再算加法50+5=                                4064. 计算( 50+5=                                407 )×3 - 先算括号里的减法50+5=                                407 = 100再算乘法100×50+5=                                4095. 计算80×4-10=                             80 - 先算乘法80×4-10=                             81再算减法4-10=                             826. 计算200×4-10=                             83 - 先算乘法200×4-10=                             84再算加法4-10=                             85", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "估一估（积是三位数的在括号里画＂√＂） $112\\times4( \\quad )$ $45\\times3( \\quad )$ $24\\times4( \\quad )$ $90\\times10( \\quad )$ $32\\times2( \\quad )$ $341\\times4( \\quad )$ 本题可通过估算每个式子的积再判断积是否为三位数① $112×4$ 把112看作100 $100×4=400$ 而 $112>100$ 所以 $112×4$ 的积大于 $400$ 是三位数画“√”② $45×3$ 把45看作40 $40×3=120$ 而45＞40所以45×3的积大于120是三位数画“√”③ $24×4$ 把24看作25 $25×4=100$ 而24＜25所以24×4的积小于100是两位数④ $90×10=900$ 积是三位数画“√”⑤ $32×2$ 把32看作30 $30×2=60$ 把32看做4040×2=80说明32×2的积在60到80之间是两位数⑥ $341×4$ 把341看作300 $300×4=1200$ 而 $341>300$ 所以 $341×4$ 的积大于 $1200$ 是四位数", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n运用估算判断积的可能性"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "每件衣服需要钉 5 个纽扣请填写下表\n| 衣服数／件 | 20 | 11 | 30 | 80 | 纽扣数／个 |   |   |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 衣服数／件 | 20 | 11 | 30 | 80 |\n| 纽扣数／个 |   |   |   |   |\n已知每件衣服需要钉5个纽扣求不同件数衣服所需纽扣数用每件衣服的纽扣数乘以衣服的件数即可当衣服数是20件时纽扣数为5×20 = 100（个）当衣服数是11件时纽扣数为5×11 = 55（个）当衣服数是30件时纽扣数为5×30 = 150（个）当衣服数是80件时纽扣数为5×80 = 400（个）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n运用口算乘法解决实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "整百数乘一位数且积是 1200 的乘法算式你能写出多少个写一写我们可以先不看积 1200 末尾的两个 0这样就变成了找两个数相乘得 12 的情况因为 2×6 = 12  3×4 = 12 那么在 2 和 6 后面分别添上两个 0 就得到 200×6 = 1200 和 600×2 = 1200 在 3 和 4 后面分别添上两个 0 就得到 300×4 = 1200 和 400×3 = 1200 所以能写出 4 个这样的乘法算式", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "笔算26×4=                      999×5=                    879×8=                   423×9=1. 计算26×4 先算6×4 = 24个位写4向十位进2 再算2×4 = 88加上进位的2得10十位写0向百位进1 所以26×4 = 1042. 计算999×5 先算9×5 = 45个位写5向十位进4 再算9×5 = 4545加上进位的4得49十位写9向百位进4 接着算9×5 = 4545加上进位的4得49百位写9向千位进4 所以999×4=                      99913. 计算879×8 先算9×4=                      9992个位写2向十位进7 再算7×4=                      999356加上进位的7得63十位写3向百位进6 接着算8×4=                      999464加上进位的6得70百位写0向千位进7 所以879×4=                      99954. 计算423×9 先算3×4=                      9996个位写7向十位进2 再算2×4=                      999718加上进位的2得20十位写0向百位进2 接着算4×4=                      999836加上进位的2得38百位写8千位写3 所以423×4=                      9999", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数连续进位的乘法\n两、三位数乘一位数(连续进位)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "甲、乙两地相距 320 千米一辆货车每小时行驶 76 千米已知甲、乙两地相距320千米货车每小时行驶76千米先计算货车4小时行驶的路程 $76\\times4=304$ （千米）因为304千米小于320千米所以4小时不能到达", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数连续进位的乘法\n解决两、三位数乘一位数(连续进位)的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "列坚式计算 $\n120\\times3=\n$ $\n940-672=\n$ $\n502\\times4=\n$ 1. 计算120×3 先算12×3 = 36然后因为120末尾有一个0就在36后面添上一个0所以120×3 = 3602. 计算940 - 672 相同数位对齐从个位减起0减2不够减从十位借1当1010 - 2 = 8 十位上4被借走1剩33减7不够减从百位借1当1013 - 7 = 6 百位上9被借走1剩88 - 6 = 2所以940 - 672 = 2683. 计算502×4 用4分别去乘502的每一位4×2 = 84×940-67204×940-6721所以结果是2008", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n因数中间或末尾是0的乘法\n三位数乘一位数(乘数中间有0)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "列竖式计算 $130\\times5=$ $201\\times3=$ $607\\times5=$ $\n320\\times4=\n$ 1. 计算130×5 - 先计算13×53×5 = 15向十位进11×5 = 5加上进位的1得6所以13×5 = 65 - 因为130末尾有1个0就在65后面添上1个0结果是6502. 计算201×3 - 用3分别去乘201的每一位1×3 = 30×3 = 02×3 = 6所以201×3 = 6033. 计算607×5 - 先算7×5 = 35向十位进30×5 = 0加上进位的3得36×5 = 150所以607×5 = 150354. 计算320×4 - 先算32×42×5 = 1523×5 = 153所以32×5 = 1538 - 因为320末尾有1个0就在128后面添上1个0结果是1280", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n因数中间或末尾是0的乘法\n三位数乘一位数(乘数中间有0)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "妈妈买饼干 2 盒需付 21 元 4 盒需付多少钱先看4盒是2盒的几倍4÷2 = 2倍因为2盒需付21元那么4盒要付的钱就是2盒的2倍所以4盒需付21×2 = 42元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的除法\n多位数与一位数的乘除混合运算\n用乘法和除法两步计算解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "丽丽看一本书如果每天看 30 页 8 天可以看完如果每天看 40 页几天可以看完首先根据每天看30页8天可以看完能算出这本书的总页数为30×8 = 240页然后用总页数除以每天看40页即240÷40 = 6天所以6天可以看完", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的除法\n多位数与一位数的乘除混合运算\n用乘法和除法两步计算解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "比一比算一算 $420\\times2=$ $503\\times5=$ $601\\times3=$ $206\\times5=$ $204\\times2=$ $350\\times5=$ $106\\times3=$ $620\\times5=$ 1. 计算 $420\\times2$ 先算 $42\\times2=84$ 再在末尾添上1个0结果是8402. 计算 $503\\times5$  $500\\times5=2500$  $3\\times5=15$  $2500+15=2515$ 3. 计算 $601\\times3$  $600\\times3=1800$  $1\\times3=3$  $1800+3=1803$ 4. 计算 $206\\times5$  $200\\times5=1000$  $6\\times5=30$  $2=840$ 5. 计算 $204\\times2$  $200\\times2=841$  $4\\times2=842$  $2=843$ 6. 计算 $350\\times5$ 先算 $35\\times2=844$ 再在末尾添上1个0结果是17507. 计算 $106\\times3$  $100\\times3=300$  $6\\times2=846$  $2=847$ 8. 计算 $620\\times5$ 先算 $62\\times2=848$ 再在末尾添上1个0结果是3100", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n因数中间或末尾是0的乘法\n三位数乘一位数(乘数中间有0)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $200\\times2=$ $70\\times3=$ $80\\times4=$ $500\\times6=$ $202\\times2=$ $27\\div3=$ $200+2=$ $0+300=$ $500\\times5=$ $300-30=$ $800\\times4=$ $300\\times0=$ 1. 计算 $200\\times2$  - 把200看成2个百2个百乘2是4个百也就是4002. 计算 $70\\times3$  - 把70看成7个十7个十乘3是21个十即2103. 计算 $80\\times4$  - 把80看成8个十8个十乘4是32个十也就是3204. 计算 $500\\times6$  - 把500看成5个百5个百乘6是30个百即30005. 计算 $202\\times2$  - 先算 $200\\times2=400$ 再算 $2\\times2=4$ 最后 $400+4=404$ 6. 计算 $27\\div3$  - 根据乘法口诀“三九二十七”可得 $27\\div3=9$ 7. 计算 $200+2$  - 200和2相加结果是2028. 计算 $0+300$  - 0加任何数都得原数所以 $0+300=300$ 9. 计算 $500\\times5$  - 把500看成5个百5个百乘5是25个百即250010. 计算 $300-30$  - 300减30结果是27011. 计算 $800\\times4$  - 把800看成8个百8个百乘4是32个百也就是320012. 计算 $300\\times0$  - 任何数乘0都得0所以 $300\\times200+22$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一本日记本 7 元买 202 本大约需要（        ） 元在估算买 $202$ 本日记本大约需要多少钱时因为 $202$ 接近 $200$ 所以把 $202$ 看作 $200$ 又已知一本日记本 $7$ 元那么 $200$ 本需要的钱数就是 $200\\times7=1400$ 元所以买 $202$ 本大约需要 $1400$ 元", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n两、三位数乘一位数的估算应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李师傅 3 天加工了 24 个零件（1）一星期（ 5 个工作日）可以加工多少个零件（2）加工 56 个零件需要多少个工作日（1）先算出李师傅一天加工零件的个数用 3 天加工的零件总数除以 3 天即 24÷3 = 8 个一星期有 5 个工作日那么 5 天能加工的零件数就是一天加工的个数乘以 5 天8×5 = 40 个（2）已经知道李师傅一天加工 8 个零件要加工 56 个零件就用要加工的零件总数除以一天加工的个数56÷8 = 7 个工作日", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的除法\n多位数与一位数的乘除混合运算\n用乘法和除法两步计算解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的除法\n多位数与一位数的乘除混合运算\n用乘法和除法两步计算解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "某地气象台发布暴雨红色预警3小时内该地降雨量将达到 110（        ） A．毫米B．厘米C．分米我们在生活中测量降雨量一般用毫米作单位1 厘米 = 10 毫米1 分米 = 10 厘米 = 100 毫米如果降雨量是 110 厘米或者 110 分米那降雨量就太大啦不符合实际情况所以 3 小时内该地降雨量将达到 110 毫米选 A", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(分米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "画一条 2 厘米 8 毫米的线段三年级我们学过测量线段长度1 厘米等于 10 毫米2 厘米 8 毫米就是 28 毫米我们可以先准备好直尺将直尺的 0 刻度线与纸的一端对齐然后在直尺 2 厘米 8 毫米也就是 28 毫米的刻度处点一个点最后连接纸一端和这个点就画出了 2 厘米 8 毫米的线段", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度及长度的常用单位\n毫米和分米的认识\n认识毫米"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "画一条 32 毫米的线段我们可以使用直尺来画线段先把直尺平放在纸上找到直尺上的刻度 0然后在刻度 0 的地方点一个点作为线段的一个端点接着沿着直尺找到刻度 32 毫米的地方再点一个点作为线段的另一个端点最后用直线把这两个端点连接起来这样就画出了一条 32 毫米的线段", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度及长度的常用单位\n毫米和分米的认识\n认识毫米"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "读书写字要做到＂三个一＂那么写字时眼睛离书本的距离大约是（        ） A． 33 厘米B． 30 毫米C． 10 厘米我们在学习写字的正确姿势时知道眼睛离书本要有合适的距离选项B中30毫米 = 3厘米这个距离太近了看书会很伤眼睛不符合要求选项C中10厘米也比较近长时间这样看书也会对眼睛不好而选项A中33厘米是比较合适的眼睛离书本的距离所以应该选A", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(千米、米、厘米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填上箭头处所表示的长度 （        ） 厘米＝（        ） 毫米 （        ） 厘米（        ） 毫米（        ） 厘米＝（        ） 毫米 （        ） 毫米 观察图片中的刻度尺箭头指向的位置分别对应不同的长度第一个箭头指向1厘米处即1厘米＝10毫米第二个箭头指向4厘米处即4厘米＝40毫米第三个箭头指向9厘米5毫米处即9厘米5毫米＝95毫米第四个箭头指向14厘米处即14厘米＝140毫米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n分米、厘米和毫米之间的单位换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下图中点 $M$ 表示的数大约是算式（        ） 的积A． $41\\times9$ B． $59\\times8$ C． $69\\times9$", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n两、三位数乘一位数的估算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填上合适的单位（1）厦门马拉松赛是国际田联和中国田协最早的＂双金＂赛事之一马拉松长跑比赛全程约 42（        ）（2）春节前夕妈妈带小明到超市购物买了一袋5（        ） 的大米买了一盒鸡蛋 （10个）大约600（        ）这盒鸡蛋的外包装盒长大约25（        ）宽大约 1（        ）（3）高速路上的交通标志＂＂表示限速每小时 80（        ） （1）马拉松比赛的全程是国际标准距离为42.195千米因此单位应填“千米”（2）一袋大米通常以千克为单位5千克是常见规格一盒鸡蛋约600克符合实际重量外包装盒的长度约为25厘米宽度约为1分米（即10厘米）符合日常观察（3）交通标志中的“80”表示限速每小时80千米单位是“千米”", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(千米、米、厘米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "50+50+50=（        ）×（        ）=（        ） 加法是把几个数合并成一个数的运算乘法是求几个相同加数和的简便运算这里是3个50相加写成乘法算式就是50×350×3 = 150", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "我会算 $98-8\\times4$ $72\\div( 86-78 )$ $48\\div8\\div2$ $9\\times4+2$ 1. 计算 $98-8×4$ 根据先乘除后加减的运算顺序先算乘法 $8×4=32$ 再算减法 $98-32=66$ 2. 计算 $72÷( 86-78 )$ 有括号先算括号里的 $86-78=8$ 再算除法 $72÷8=9$ 3. 计算 $48÷8÷2$ 按照从左到右的顺序依次计算先算 $48÷98-80$ 再算 $6÷98-81$ 4. 计算 $9×4+2$ 先算乘法 $9×98-83$ 再算加法 $36+98-818$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n带有小括号的混合运算\n含有小括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "张师傅做零件每天做 6 个 6 天完成如果每天做 9 个几天可以完成本题可先根据“每天做 $6$ 个 $6$ 天完成”求出零件的总数再用零件总数除以每天做 $9$ 个即可求出完成天数已知张师傅每天做 $6$ 个零件 $6$ 天完成根据“工作总量 $=$ 工作效率 $\\times$ 工作时间”可得零件总数为 $6\\times6=36$ （个）现在每天做 $9$ 个根据“工作时间 $=$ 工作总量÷工作效率”可得需要的天数为 $36\\div9=4$ （天）综上每天做 $9$ 个时 $4$ 天可以完成", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "我会笔算（带★的要验算） ★ ★ $990-372=$ $342\\times5=$ $450\\times7=$ $905\\times8=$ 1. 计算990 - 372 - 个位上0 - 2不够减从十位借1当1010 - 2 = 8 - 十位上9被借走1剩88 - 7 = 1 - 百位上9 - 3 = 6所以990 - 372 = 618 - 验算根据被减数=差 + 减数618+372 = 990计算正确2. 计算342×5 - 先算2×5 = 10个位写0向十位进1 - 再算4×990-372020加上进位的1得21十位写1向百位进2 - 最后算3×990-372115加上进位的2得17百位写7千位写1所以342×990-37223. 计算450×7 - 先算45×75×990-3723个位写5向十位进34×990-372428加上进位的3得31所以45×990-3725 - 450×7就是在315后面添上一个0即450×990-372504. 计算905×8 - 先算5×990-3727个位写0向十位进4 - 十位上0×990-37280加上进位的4得4十位写4 - 百位上9×990-3729百位写2向千位进7所以905×990-372940", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数连续进位的乘法\n两、三位数乘一位数(连续进位)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一条绳子长 3 米剪去 7 分米后剩下的比剪去的长（        ） A． 23 分米B． 23 厘米C． 16 分米本题可先将绳子的长度单位换算成分米再求出剩下绳子的长度最后计算剩下的比剪去的长多少因为 $1$ 米 $=10$ 分米所以 $3$ 米换算成分米为 $3\\times10=30$ 分米已知绳子原长 $30$ 分米剪去 $7$ 分米那么剩下的长度为 $30-7=23$ 分米剩下的绳子长 $23$ 分米剪去的绳子长 $7$ 分米所以剩下的比剪去的长 $23-7=16$ 分米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n解决有关分米、厘米、毫米的问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "列竖式计算 $\n307\\times4=\n$ $327-186=$ $\n423\\times7=\n$ $\n501+289=\n$ 1. 计算307×4 - 先算7×4 = 28个位写8向十位进2 - 再算0×4 = 0加上进位的2得2十位写2 - 最后算3×4 = 12百位写2千位写1所以307×4 = 12282. 计算327 - 186 - 个位7 - 6 = 1 - 十位2减8不够减从百位借1当1012 - 8 = 4 - 百位3被借走1剩2327-1860所以4 = 2827-186313. 计算423×7 - 先算3×327-1862个位写1向十位进2 - 再算2×327-1863加上进位的2得16十位写6向百位进1 - 最后算4×327-1864加上进位的1得29百位写9千位写2所以423×327-18654. 计算327-1866 - 个位327-1867个位写0向十位进1 - 十位327-1868加上进位的1得9 - 百位327-1869所以327-1866 = 790", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数连续进位的乘法\n两、三位数乘一位数(连续进位)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "张明看一本 180 页的科技书每天看 29 页连续看了 6 天估一估他能看完这本书吗请列式说明理由本题可先对张明 6 天看的页数进行估算再与这本书的总页数比较大小估算时把每天看的页数 29 看成与它接近的整十数 30然后用 30 乘以看的天数 6得到 $30×6=180$ 页因为是把 29 估大了才得到 180 页所以实际 6 天看的页数小于 180 页而这本书有 180 页所以他不能看完这本书", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n两、三位数乘一位数的估算应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一根彩带长 2 米 4 分米对折 3 次后平均每段长（        ） 分米A． 3B． 4C． 8本题可先将彩带的长度单位统一换算成分米再分析对折 $3$ 次后彩带被平均分成的段数最后用彩带总长度除以段数即可求出平均每段的长度步骤一将彩带长度的单位统一换算成分米因为 $1$ 米 $=10$ 分米所以 $2$ 米换算成分米为 $2\\times10=20$ （分米）那么 $2$ 米 $4$ 分米换算成分米为 $20+4=24$ （分米）步骤二分析对折 $3$ 次后彩带被平均分成的段数对折 $1$ 次彩带被平均分成 $2$ 段对折 $2$ 次彩带被平均分成 $2\\times2=4$ 段对折 $3$ 次彩带被平均分成 $2\\times2\\times2=8$ 段步骤三计算平均每段的长度已知彩带总长度为 $24$ 分米且被平均分成了 $8$ 段根据“每段长度 $=$ 总长度 $\\div$ 段数”可得平均每段长 $24\\div8=3$ （分米）综上答案选A", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n整数平均分的意义及应用\n平均分的综合理解与应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如图 5 盒月饼叠起来高 3 分米每盒月饼高多少厘米题目中给出5盒月饼叠起来的高度是3分米首先将单位统一3分米等于30厘米然后用总高度除以盒子的数量30 ÷ 5 = 6（厘米）所以每盒月饼高6厘米", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n表内除法\n除法的初步认识\n包含分求份数（列除法算式）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "列竖式计算 $\n307\\times4=\n$ $327-186=$ $\n423\\times7=\n$ $\n501+289=\n$ 1. 计算307×4 - 先算7×4 = 28个位写8向十位进2 - 再算0×4 = 0加上进位的2得2十位写2 - 最后算3×4 = 12百位写2千位写1所以307×4 = 12282. 计算327 - 186 - 个位7 - 6 = 1 - 十位2减8不够减从百位借1当1012 - 8 = 4 - 百位3被借走1剩2327-1860所以4 = 2827-186313. 计算423×7 - 先算3×327-1862个位写1向十位进2 - 再算2×327-1863加上进位的2得16十位写6向百位进1 - 最后算4×327-1864加上进位的1得29百位写9千位写2所以423×327-18654. 计算327-1866 - 个位327-1867个位写0向十位进1 - 十位327-1868加上进位的1得9 - 百位327-1869所以327-1866 = 790", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数连续进位的乘法\n两、三位数乘一位数(连续进位)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "列竖式计算 $\n307\\times4=\n$ $327-186=$ $\n423\\times7=\n$ $\n501+289=\n$ 1. 计算307×4 - 先算7×4 = 28个位写8向十位进2 - 再算0×4 = 0加上进位的2得2十位写2 - 最后算3×4 = 12百位写2千位写1所以307×4 = 12282. 计算327 - 186 - 个位7 - 6 = 1 - 十位2减8不够减从百位借1当1012 - 8 = 4 - 百位3被借走1剩2327-1860所以4 = 2827-186313. 计算423×7 - 先算3×327-1862个位写1向十位进2 - 再算2×327-1863加上进位的2得16十位写6向百位进1 - 最后算4×327-1864加上进位的1得29百位写9千位写2所以423×327-18654. 计算327-1866 - 个位327-1867个位写0向十位进1 - 十位327-1868加上进位的1得9 - 百位327-1869所以327-1866 = 790", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n因数中间或末尾是0的乘法\n三位数乘一位数(乘数中间有0)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一根彩带长 2 米 4 分米对折 3 次后平均每段长（        ） 分米A． 3B． 4C． 8本题可先将彩带的长度单位统一换算成分米再分析对折 $3$ 次后彩带被平均分成的段数最后用彩带总长度除以段数即可求出平均每段的长度步骤一将彩带长度的单位统一换算成分米因为 $1$ 米 $=10$ 分米所以 $2$ 米换算成分米为 $2\\times10=20$ （分米）那么 $2$ 米 $4$ 分米换算成分米为 $20+4=24$ （分米）步骤二分析对折 $3$ 次后彩带被平均分成的段数对折 $1$ 次彩带被平均分成 $2$ 段对折 $2$ 次彩带被平均分成 $2\\times2=4$ 段对折 $3$ 次彩带被平均分成 $2\\times2\\times2=8$ 段步骤三计算平均每段的长度已知彩带总长度为 $24$ 分米且被平均分成了 $8$ 段根据“每段长度 $=$ 总长度÷段数”可得平均每段长 $24\\div8=3$ （分米）综上答案选A", "output": "小学数学新知识树\n图形与几何\n图形的拼组\n图形的折叠问题\n绳长对折问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如图 5 盒月饼叠起来高 3 分米每盒月饼高多少厘米题目中给出5盒月饼叠起来的高度是3分米首先将单位统一3分米等于30厘米然后用总高度除以盒子的数量30 ÷ 5 = 6（厘米）所以每盒月饼高6厘米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n解决有关分米、厘米、毫米的问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如图有一座承重为 1 吨的桥下面哪几只动物可以一起通过（答案有多种至少写出两种）1吨=1000kg我们先看每只动物的体重大象500kg小牛120kg狮子240kg熊180kg马200kg要求总重量不超过1000kg找出可以一起通过的组合（1）大象和小牛500+120=620kg＜1000kg可以（2）狮子和熊240+180=420kg＜1000kg可以（3）狮子和马240+200=440kg＜1000kg可以（4）熊和马180+200=380kg＜1000kg可以（5）小牛、狮子和熊120+240+180=540kg＜1000kg可以（6）小牛、狮子和马120+240+200=560kg＜1000kg可以（7）小牛、熊和马120+180+200=500kg＜1000kg可以（8）狮子、熊和马240+180+200=620kg＜1000kg可以（9）小牛、狮子、熊和马120+240+180+200=740kg＜1000kg可以注意不能包含大象和另外两只或以上动物因为即使是最轻的三只动物加大象也超过1000kg例如大象+小牛+狮子=500+120=6200kg还可以但大象+小牛+狮子+熊=500+120=6201kg＞1000kg不行所以满足条件的组合有多种至少写出两种即可", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数的加法和减法\n几百几十数、整百数的加减法口算\n整百、整千数、几百几十数的加减法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "如图有一座承重为 1 吨的桥下面哪几只动物可以一起通过（答案有多种至少写出两种）1吨=1000kg我们先看每只动物的体重大象500kg小牛120kg狮子240kg熊180kg马200kg要求总重量不超过1000kg找出可以一起通过的组合（1）大象和小牛500+120=620kg＜1000kg可以（2）狮子和熊240+180=420kg＜1000kg可以（3）狮子和马240+200=440kg＜1000kg可以（4）熊和马180+200=380kg＜1000kg可以（5）小牛、狮子和熊120+240+180=540kg＜1000kg可以（6）小牛、狮子和马120+240+200=560kg＜1000kg可以（7）小牛、熊和马120+180+200=500kg＜1000kg可以（8）狮子、熊和马240+180+200=620kg＜1000kg可以（9）小牛、狮子、熊和马120+240+180+200=740kg＜1000kg可以所以满足条件的组合有多种至少写出两种即可", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量间的计算和应用\n解决吨相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看谁算得又对又快\n|   |   |   |\n| --- | --- | --- |\n| 4+0=1-1=4-0=5+0= | 0+0=3+0=2-2=1+0= | 4-4=0-0=0+2=5-0= |\n对于4 + 0任何数加0都等于它本身所以4 + 0 = 4对于1 - 1相同的两个数相减得0所以1 - 1 = 0对于4 - 0任何数减0都等于它本身所以4 - 0 = 4对于5 + 0任何数加0都等于它本身所以5 + 0 = 5对于0 + 00加0还是0所以0 + 0 = 0对于4 + 01任何数加0都等于它本身所以4 + 01 = 3对于4 + 03相同的两个数相减得0所以4 + 03 = 0对于4 + 05任何数加0都等于它本身所以4 + 05 = 1对于4 + 07相同的两个数相减得0所以4 + 07 = 0对于4 + 09相同的两个数相减得0所以4 + 09 = 0对于4 + 0 = 41任何数加0都等于它本身所以4 + 0 = 41 = 2对于4 + 0 = 43任何数减0都等于它本身所以4 + 0 = 43 = 5", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n有关0的加减法\n算式中有0的加减法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看一看填一填（1）上图中一共有（        ） 位球员（2）从左边数排在第 3 位的是（        ） 号球员 5 号球员排在第 （        ） 位（3）把右边的 6 位球员圈起来（4）把球员们的号码按从小到大的顺序排列        （        ）<（        ）<（        ）<（        ）<（        ）<（        ）<（        ）<（        ）（1）观察图片一共有8个球员（2）从左边开始数第3位的球员背号是65号球员从左边数是第6位（3）从右边数起前6位球员需要被圈起来即从右往左第1到第6位（4）将所有号码从小到大排列2、3、4、5、6、7、8、9", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n9以内数的比大小\n应用几、第几及左右/前后关系解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "和一样多的是（        ）①②③观察[图片1]里面有6只狐狸再看选项①[图片2]中有3个圆点数量是3②[图片3]中有6个圆点数量是6③[图片4]中有5个圆点数量是5因为6和6一样多所以和[图片1]一样多的是②", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n6~9的认识和读写\n6-9的认识"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "想一想写一写（1）（2）（1）观察图片1左边有4把伞右边有2把伞将左右两边的伞合起来总数是4+2=6从总数6中去掉左边的4把剩下右边的2把即6-4=2也可以用右边的2把加上左边的4把得到2+4=6从总数6中去掉右边的2把剩下左边的4把即6-2=4（2）观察图片2左边有3只兔子右边有4只兔子将左右两边的兔子合起来总数是3+4=7从总数7中去掉左边的3只剩下右边的4只即7-3=4也可以用右边的4只加上左边的3只得到4+3=7从总数7中去掉右边的4只剩下左边的3只即7-4=3", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n6、7的加减法\n6 、7的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "拨一拔填一填（1）如果再加 7 颗就有（        ） 颗（2）如果要出现 9 颗就要再加（        ） 颗（1）观察图片1可以看到已经有3颗[图片2]如果再加7颗就是3+7=10颗（2）要出现9颗[图片4]现在有4颗所以需要再加9-4=5颗", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n8、9的分与合\n8、9的分合的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "找朋友连一连观察图片左边的小女孩说数字8表示要找出加起来等于8的两个数图中数字有1、3、4、7、4、6、5、2我们来找出哪些两个数相加等于81 + 7 = 82 + 6 = 83 + 5 = 84 + 4 = 8所以配对为1和72和63和54和4", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n8、9的分与合\n8、9的分合的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一共有（        ） 辆汽车开走（        ） 辆＂辆＂表示求还剩几辆用（        ） 法计算一共有6辆汽车开走4辆“辆”表示求还剩几辆用减法计算", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n6、7的加减法\n6、7的减法的实际应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填一填3+=6            -4=3            6-2= +27- =2           +7=7           7-=5-1. 3+[图片1]=6因为3加3等于6所以[图片1]是32. [图片2]-4=3因为4加3等于7所以[图片2]是73. 6-2=[图片3]+26减2等于44减2等于2所以[图片3]是24. 7-[图片4]=2因为7减5等于2所以[图片4]是55. [图片5]+7=7因为0加7等于7所以[图片5]是06. 7-[图片6]=5-[图片7]移项得[图片6]-[图片7]=2因此[图片6]比[图片7]大2在一年级范围内取最小值设[图片7]=0则[图片6]=2验证7-2=55-0=5成立所以[图片6]=2[图片7]=0综上答案为3,7,2,5,0,2,0", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n6、7的加减法\n6 、7的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "（1）一共有几人在捉（2）树上有几个苹果（3）口头提一个问题并列式计算（1）观察图片1有3个小朋友在捉蝴蝶还有1个小朋友在旁边所以一共是3+1=4人（2）树上和篮子里一共有7个苹果篮子里有3个所以树上有7-3=4个苹果（3）可以提出问题一共有几个玉米图中地上有5个玉米农民手里拿着1个所以一共是5+1=6个", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n6、7的加减法\n6、7的减法的实际应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看图列式计算第一幅图盘子里有5个苹果旁边有3个苹果求一共有多少个苹果用加法计算5 + 3 = 8（个）第二幅图一共有9个包子右边盘子里有3个包子求篮子里有多少个包子用减法计算9 - 3 = 6（个）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n8、9的加减法\n8、9的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "你能列出哪些算式（1）（2）（1）观察图片1左边有6个笑脸右边有2个笑脸可以列出加法算式6+2=8表示左右两边合起来一共是8个笑脸也可以列出另一个加法算式2+6=8表示交换加数位置和不变减法算式8-6=2表示从总数8个中去掉左边的6个剩下右边的2个另一个减法算式8-2=6表示从总数8个中去掉右边的2个剩下左边的6个（2）观察图片2左边有5朵花右边有3朵花可以列出加法算式5+3=8表示左右两边合起来一共是8朵花也可以列出另一个加法算式3+5=8表示交换加数位置和不变减法算式8-5=3表示从总数8朵中去掉左边的5朵剩下右边的3朵另一个减法算式8-3=5表示从总数8朵中去掉右边的3朵剩下左边的5朵", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n8、9的加减法\n8、9的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先写出算式再和同桌说一说每个算式求的是什么为什么这样求1. 第一个算式3+5=8（条）求的是上方左边的鱼和右边的鱼合起来一共有多少条图中左边有3条鱼右边有5条鱼把它们加起来就是总数2. 第二个算式7-5=2（条）求的是右上角的7条鱼中除了5条小鱼外还剩下几条鱼图中右上角一共7条鱼其中5条是小鱼剩下的2条是其他种类的鱼3. 第三个算式6-3=3（条）求的是左下角的6条鱼中除了3条在水草旁的鱼外还有几条鱼图中左下角共6条鱼其中有3条在水草旁其余3条在别处4. 第四个算式7-4=3（只）求的是中间的7只虾中除了4只在水草旁的虾外还有几只虾图中总共7只虾其中4只在水草旁其余3只在别处", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n6、7的加减法\n6、7的减法的实际应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "每个杯子里都要放 10 只 分别还要再放几只 （    ）只           （    ）只           （    ）只          （    ）只           （    ）只           （    ）只观察图片每个杯子里已经有若干只蝌蚪题目要求每个杯子最终都有10只第一个杯子有6只还需要放10 - 6 = 4（只）第二个杯子有2只还需要放10 - 2 = 8（只）第三个杯子有8只还需要放10 - 8 = 2（只）第四个杯子有4只还需要放10 - 4 = 6（只）第五个杯子有1只还需要放10 - 1 = 9（只）第六个杯子有3只还需要放10 - 3 = 7（只）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n10的分与合\n10的分与合"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "画一画填一填（1） $\\triangle\\triangle\\triangle\\triangle$ ___ $\n4+\\square=10\\quad10-4=\\square\\quad10-\\square=\\square\n$ （2） $\\triangle\\triangle\\triangle\\triangle\\triangle\\triangle\\triangle$ ___ $\n7+\\square=10\\quad10-7=\\square\\quad10-\\square=\\square\n$ （1）已知有4个 $\\triangle$ 要使总数为10个 $\\triangle$ 还需要画6个 $\\triangle$  $4+6=10$  $10-4=6$  $10-6=4$ （2）已知有7个 $\\triangle$ 要使总数为10个 $\\triangle$ 还需要画3个 $\\triangle$  $7+3=10$  $10-7=3$  $10-3=7$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n10的加减法\n10的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "填一填（1）先算2+3=5再算5+5=10（2）先算10-6=4再算4-2=2（3）先算6+0=6再算6+3=9", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n连加、连减（10以内）\n连加计算(10以内)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在里填上“>”“”9-7=22小于5所以填“”2+8=1010大于8所以填“>”5+5909小于,10所以填“<”", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n10的加减法\n10的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小亮有 8 个红气球小丽有 6 个黄气球小亮用 4 个红气球换小丽 3个黄气球现在小亮、小丽各有几个气球小亮小丽小亮原来有8个红气球他用4个红气球换小丽的3个黄气球所以小亮的气球数是原来的8个减去给出的4个再加上得到的3个即8-4+3=7（个）小丽原来有6个黄气球她用3个黄气球换小亮的4个红气球所以小丽的气球数是原来的6个减去给出的3个再加上得到的4个即6-3+4=7（个）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n加减混合运算（10以内）\n加减混合运用(10以内加减混合)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先把图的意思填完整再根据问题选择合适的方法画＂ $\\sqrt{}$ ＂并解答晾衣架上原来有件衣服挂上去件收下来 件问现在有多少件我想先用（加 减）法计算再用（加 减）法计算观察图片可知左边图中晾衣架上有5件衣服小女孩手里拿着3件衣服准备挂上去右边图中晾衣架上有6件衣服小女孩手里拿着2件衣服准备收下来根据题意原来有5件衣服挂上去3件所以先用加法计算5+3=8（件）然后收下来2件再用减法计算8-2=6（件）因此现在有6件衣服", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n加减混合运算（10以内）\n加减混合运用(10以内加减混合)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看一看编一编写一写（1）原来树上有（        ） 只小鸟飞走了（        ） 只又飞来了（        ） 只现在树上有几只小鸟（2）你能模（mó）仿（făng）上一题说出一个不一样的问题吗请你先说一说然后根据问题在下面写出算式（1）观察图片树上原来有10只小鸟飞走了3只又飞来了2只根据题意列式为10 - 3 + 2 = 8（只）（2）可以提出一个类似的问题例如原来地上有3只兔子先跑来了3只又跑来了3只现在地上有几只兔子列式为3+3 + 3 = 9（只）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n加减混合运算（10以内）\n加减混合运用(10以内加减混合)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看图列式计算（1）                            5+1+=（只）（2）                            9-3-=（根）（1）观察图片1左边有5只小鸡中间有1只小鸡右边有4只小鸡将三部分相加得到总数5+1+4=10（只）（2）观察图片4一共有9根胡萝卜外面有3根再减去另外2根篮子里剩下的数量为9-3-2=4（根）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n连加、连减（10以内）\n连减的应用（10以内）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下图可以用＂ $8-2+2$ ＂来解决第 2 个＂ 2 ＂表示（        ） ①2 人跑来参加                    ② 2 人正要从操场离开                                ③操场上还剩 2 人观察图片图中一开始有8个小朋友在操场上踢球接着有2个小朋友离开操场然后又有2个小朋友跑来参加所以算式 $8-2+2$ 中第一个2表示离开的2人第二个2表示新加入的2人因此第2个“2”表示的是2人跑来参加", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n加减混合运算（10以内）\n加减混合运用(10以内加减混合)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "哥哥、姐姐和弟弟每人都有 9 颗樱桃哥哥给弟弟 1 颗后弟弟吃了 3 颗这时谁的樱桃最多请说明理由首先看哥哥哥哥原本有9颗樱桃给了弟弟1颗那么哥哥现在有9 - 1 = 8颗樱桃再看姐姐姐姐没有给出樱桃也没有得到樱桃所以姐姐还是有9颗樱桃最后看弟弟弟弟原本有9颗樱桃哥哥给了他1颗此时弟弟有9 + 1 = 10颗樱桃然后弟弟吃了3颗那么弟弟现在有10 - 3 = 7颗樱桃因为9＞8＞7所以姐姐的樱桃最多", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n加减混合运算（10以内）\n加减混合运用(10以内加减混合)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看图列式计算（1）                    4+2+=（根）（2）                    10-5-=（个）（1）观察图片1左边有4根小棒中间有2根小棒右边有3根小棒将三部分相加列式为4+2+3=9（根）（2）观察图片4共有10个正方体先去掉5个（虚线框住的部分）再去掉2个（被划掉的部分）列式为10-5-2=3（个）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n连加、连减（10以内）\n连减的应用（10以内）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在 里填上合适的数第一题9 - 0 = 9再减去1得到 9 - 1 = 8所以第一个空填 8第二题8 - 4 = 4再加上5得到 4 + 5 = 9所以第二个空填 9第三题4 + 3 = 7再加上2得到 7 + 2 = 9所以第三个空填 9", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n连加、连减（10以内）\n连加计算(10以内)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "求青蛙现在的位置正确的列式是（        ） ①2+5+9            ②2+3+4        ③9-4-3青蛙从位置2开始跳第一次跳到5说明跳了3格（5-2=3）第二次跳到9说明又跳了4格（9-5=4）所以总共跳的格数是3+4=7格但题目问的是列式表示青蛙现在的位置即从起点2开始加上两次跳跃的距离2+3+4=9因此正确的列式是②2+3+4选项①是2+5+9这明显是把起始点和两个终点相加不符合实际跳跃过程选项③是9-4-3=2这是倒推不是求当前位置所以正确列式是②2+3+4对应位置是9因此正确答案是②", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n连加、连减（10以内）\n连减的应用（10以内）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "比一比（1）有（        ）只有（        ）只（2）比少（        ）只（3）比多(        )只（1）观察图片第一行是海星共有7只第二行是鱼共有10只（2）比较海星和鱼的数量鱼比海星多用减法计算10 - 7 = 3所以海星比鱼少3只（3）鱼比海星多用减法计算10 - 7 = 3所以鱼比海星多3只", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n6、7的加减法\n6 、7的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "（1）看图填空①上图的小旗中从左边数第8面上的数字是（        ） 从右边数第 4 面上的数字是（        ） ②从左边数第1面上的1和第（        ） 面上的（        ） 合起来是 10 第 3 面上的（        ） 和第（        ） 面上的（        ） 合起来也是 10 ①从左边数第8面小旗上的数字是8从右边数第4面小旗上的数字是6②从左边数第1面小旗上的数字是1与第9面小旗上的数字1合起来是10第3面小旗上的数字是3与第7面小旗上的数字7合起来也是10", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n9以内数的比大小\n应用几、第几及左右/前后关系解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "从中选出3个数写出2道加法算式和2道减法算式        从图片1中可以看到有四个数字4、5、7、9题目要求从中选出3个数写出2道加法算式和2道减法算式我们选择数字4、5、9因为4+5=9符合加法关系根据加法交换律可以写出两道加法算式4+5=95+4=9再根据加法与减法的关系可以写出两道减法算式9-5=49-4=5这些算式都使用了所选的三个数并且符合一年级上学期的认知水平", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n8、9的加减法\n8、9的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一共有 只右边有 只左边有几只从图中可以看出一共有6只企鹅右边有4只要求左边有多少只就是从总数里去掉右边的数量用减法计算6 - 4 = 2（只）所以左边有2只", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n6、7的加减法\n6、7的减法的实际应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一共有只先游走了只再游走了只还剩几只观察图片池塘里一共有8只小鸭子先游走了3只再游走了2只用总数减去第一次游走的再减去第二次游走的就是剩下的数量计算8 - 3 = 55 - 2 = 3所以还剩3只", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n连加、连减（10以内）\n连减的应用（10以内）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "观察图片1树上有2只鸟空中有4只鸟飞向树还有2只鸟在右边飞将这三部分的鸟的数量相加即2只（树上）+4只（飞向树）+2只（右边飞）=8只所以总共有8只鸟", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n连加、连减（10以内）\n连减的应用（10以内）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "比一比填一填 比少（        ） 个 比 多（        ） 个比多（        ） 个比少（        ） 个首先观察图片有4个有7个有9个7 - 4 = 3（个）所以比少3个比多3个9 - 7 = 2（个）所以比圆多2个圆比少2个", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n8、9的加减法\n8、9的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "画一条 4 厘米长的线段首先准备好直尺将直尺平放在纸上然后在直尺的 0 刻度线位置点一个点接着在直尺 4 厘米刻度线的地方再点一个点最后用直尺将这两个点连接起来所形成的线段就是 4 厘米长的线段", "output": "小学数学新知识树\n图形与几何\n平面图形\n直线、射线、线段\n根据长度画线段\n画线段、直线和射线"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用两个三角尺拼角拼成的角是钝角的是（ ） A.    B.    C.    D.观察每个选项中两个三角尺拼成的角选项A两个三角尺的60°角和90°角拼在一起形成的角大于90°是钝角选项B两个三角尺的30°角和45°角拼在一起形成的角小于90°是锐角选项C两个三角尺的45°角拼在一起形成的角等于90°是直角选项D两个三角尺的30°角拼在一起形成的角小于90°是锐角因此只有选项A拼成的角是钝角", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n用三角尺画角\n三角尺拼角"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "你会数线段吗像这样连一连数一数你发现了什么\n| 图形 |   |   |   |   | 端点数 | 2 | 3 |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 图形 |   |   |   |   |\n| 端点数 | 2 | 3 |   |   |\n| 线段数 | 1 |   |   |   |\n我们先来看线段数和端点数的关系当有2个端点时线段数是1条当有3个端点时我们可以从第一个端点出发和后面2个端点分别连成线段有2条从第二个端点出发和第三个端点连成1条线段所以一共是2 + 1 = 3条线段当有4个端点时从第一个端点出发能和后面3个端点连成3条线段从第二个端点出发能和后面2个端点连成2条线段从第三个端点出发能和第四个端点连成1条线段总共就是3 + 2 + 1 = 6条线段当有5个端点时从第一个端点出发能和后面4个端点连成4条线段从第二个端点出发能和后面3个端点连成3条线段从第三个端点出发能和后面2个端点连成2条线段从第四个端点出发能和第五个端点连成1条线段总共就是4 + 3 + 2 + 1 = 10条线段", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数线段、直线和射线"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "妈妈用的锁屏密码是图案密码并且图案里有 2 个锐角和 1 个直角妈妈设置的图案密码可能是（ ） A.    B.    C.    D.选项A没有锐角选项C和D都没有直角只有选项B有2个锐角和1个直角", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n角的初步认识的应用(直角、锐角、钝角)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "请在下面方格纸上分别画出各 1 个直角、钝角和锐角在方格纸上画角时可以利用方格线来帮助确定角度的大小1. 直角选择一个点作为顶点从该点出发沿水平方向画一条边再沿垂直方向画另一条边两条边形成的夹角就是直角（90°）2. 钝角选择一个点作为顶点从该点出发画一条边然后画另一条边使两条边之间的夹角大于90°但小于180°例如可以画一条边向右另一条边向左上方倾斜形成一个大于直角的角3. 锐角选择一个点作为顶点从该点出发画一条边再画另一条边使两条边之间的夹角小于90°例如可以画一条边向右另一条边向右上方轻微倾斜形成一个小于直角的角根据上述方法在方格纸上分别画出一个直角、一个钝角和一个锐角即可", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n认识锐角、钝角"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "钟面上显示 8 时整时针和分针形成的角是（ ） A．直角B．锐角C．钝角D．不确定钟面一周为360°共分12个大格每格为360÷12 = 30°8时整分针指向12时针指向8中间有4个大格它们之间的夹角是30°×4 = 120°大于90°小于180°的角是钝角所以8时整时针和分针形成的角是钝角", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n角的初步认识的应用(直角、锐角、钝角)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面说法正确的有（ ）①一个角拿放大镜一照这个角就变大了②角的边越长这个角就越大③一个角的大小与两边张口的大小有关④角的大小与边的长短无关A．①②B．③④C．①③④D．②③④本题可根据角的大小的相关性质对每个说法逐一进行分析①角的大小只与角两边张开的程度有关与边的长短无关用放大镜看角只是边的长度看上去变长了但角两边张开的程度并没有改变所以角的大小不变该说法错误②角的大小与边的长短无关所以角的边越长角不一定越大该说法错误③角的大小由两边张口的大小决定两边张口越大角就越大两边张口越小角就越小所以一个角的大小与两边张口的大小有关该说法正确④角的大小只与角两边张开的程度有关与边的长短无关该说法正确综上③④说法正确答案选B", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n角的大小比较\n角的大小比较(初步认识角)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数一数下面图形各有几条线段几个角（1）第一个图形是长方形有4条边每条边是一条线段所以有4条线段长方形有4个角每个角都是直角所以有4个角（2）第二个图形是一个十字形由5个小正方形组成数线段时要仔细观察每一条边上下左右各有一个突出的矩形部分共12条边即12条线段每个小正方形有4个角但相邻的小正方形共享边因此整个图形共有12个顶点每个顶点处形成一个角所以有12个角（3）第三个图形是一个大三角形内部有一条从顶点到底边的线段将大三角形分成两个小三角形数线段时外框有3条边内部有1条线段共4条线段但注意这条内部线段与底边相交形成了2条新的线段所以一共有4+2=6条线段数角时大三角形有3个角内部线段将底边分成两段将一个大角分成2个小角并形成2个新的小角所以一共有3+2+2=7个角", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数角(角的初步认识)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面图形中各有几个角几条线段（            ）个角（            ）条线段（            ）个角（            ）条线段（            ）个角（            ）条线段（            ）个角（            ）条线段（            ）个角（            ）条线段图形1扇形角的数量1个角只有1个顶点所以只有1个角线段的数量2条线段2条半径为2条线段图形2正六边形角的数量6个角正六边形有6个顶点每个顶点形成一个角线段的数量6条线段正六边形由6条边组成每条边是一条线段图形3半圆弧加直径角的数量0个角因为半圆弧没有顶点所以没有角线段的数量1条线段只有底部的一条直线是线段图形4平行四边形角的数量4个角平行四边形有4个顶点每个顶点形成一个角线段的数量4条线段平行四边形由4条边组成每条边是一条线段图形5箭头形状角的数量7个角箭头形状有7个顶点每个顶点形成一个角线段的数量7条线段箭头形状由7条边组成每条边是一条线段", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数角(角的初步认识)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数一数写一写直角个数（ ）个                                （ ）个                                    （ ）个钝角个数（ ）个                                （ ）个                                    （ ）个锐角个数（ ）个                                （ ）个                                    （ ）个第一个图形是平行四边形它的对边平行且相等但四个角都不是直角其中两个角大于90°是钝角另外两个角小于90°是锐角第二个图形是一个五边形看起来像一个正方形切去一个角它有三个直角（每个角为90°）两个钝角（大于90°）没有锐角第三个图形是一个菱形由两个三角形组成它没有直角有2个钝角（大于90°）和6个锐角（小于90°）", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数角(角的初步认识)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "钟面上显示 9 时 15 分时针和分针形成的角是（ ） A．直角B．锐角C．钝角D．不确定在9时15分分针指向3时针指向9和10之间时针和分针之间的大格数大于5但小于6钟面上当时针和分针之间的大格数等于3时形成的是直角当时针和分针之间的大格数等于6时形成的是平角因此当时针和分针之间的大格数大于5但小于6时形成的是钝角答案选C", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n角的初步认识的应用(直角、锐角、钝角)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "请找出生活中的＂直角＂并用＂直角号＂表示（每图至少找 1 个）（1）观察[图片1]这是一个三角尺其形状为等腰直角三角形在三角尺中有一个角是90°即直角这个直角位于三角尺的左下角可以用直角符号“┐”标记（2）观察[图片2]这是一把直尺其边缘是直线且两端垂直于刻度线在0刻度处左右两边的边与刻度线形成两个直角分别位于左端和右端可用直角符号“┐”标记（3）观察[图片3]这是一个带有圆角的矩形虽然四个角不是完全的直角但根据题意要求找出生活中的“直角”可以认为其左上角和右下角接近直角因此可将这两个角视为直角并用直角符号“┐”标记综上所述每幅图中至少找到一个直角并用直角符号表示", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n认识直角"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "钟面上时针和分针成锐角时这时的时间可能是（ ） A． 2 时B． 6 时C． 9 时D． 12 时本题可根据钟面的特点分别分析每个选项中时针和分针所成的角的类型选项A2时钟面一圈为360°共被分成了12个大格则每一大格的度数为 $360\\div12=30^{\\circ}$ 2时整时针指向2分针指向12它们之间有2个大格所以所成的角是 $2\\times30=60^{\\circ}$ 因为锐角是大于0°而小于90°的角60°在这个范围内所以2时整时针和分针成锐角选项B6时6时整时针指向6分针指向12它们之间有6个大格则所成的角是 $6\\times30=180^{\\circ}$ 平角是等于180°的角所以6时整时针和分针成平角不是锐角选项C9时9时整时针指向9分针指向12它们之间有3个大格所以所成的角是 $3\\times30=90^{\\circ}$ 直角是等于90°的角所以9时整时针和分针成直角不是锐角选项D12时12时整时针和分针都指向12它们重合在一起所成的角是0°不是锐角综上答案选A", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n角的初步认识的应用(直角、锐角、钝角)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数角你发现了什么\n| 图形 |   |   |   |   | 射线的数量 | 2 | 3 |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 图形 |   |   |   |   |\n| 射线的数量 | 2 | 3 |   |   |\n| 角的数量 | 1 |   |   |   |\n. 先看射线数量是3的情况 我们可以把角一个个数出来以第一条射线为一边可以和另外两条射线分别组成2个角以第二条射线为一边（和第一条射线组成的角前面已经数过了）可以和第三条射线组成1个角所以一共有2 + 1 = 3个角2. 当射线数量是4时 以第一条射线为一边可以和另外三条射线分别组成3个角以第二条射线为一边（和第一条射线组成的角前面数过了）可以和剩下两条射线组成2个角以第三条射线为一边（和前面射线组成的角都数过了）可以和第四条射线组成1个角所以角的总数是3 + 2 + 1 = 6个3. 当射线数量是5时 以第一条射线为一边可以和另外四条射线分别组成4个角以第二条射线为一边（和第一条射线组成的角前面数过了）可以和剩下三条射线组成3个角以第三条射线为一边可以和剩下两条射线组成2个角以第四条射线为一边可以和第五条射线组成1个角所以角的总数是4 + 3 + 2 + 1 = 10个4. 找规律 通过上面的计算我们发现角的数量等于从1开始连续加到比射线数量少1的数的和根据等差数列求和公式从1加到n的和 = n×( n + 1 )÷2这里n比射线数量少1所以角的数量 = 射线数量×( 射线数量 - 1 )÷2", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数角(角的初步认识)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "图①一共有（ ） 个角其中直角有（ ） 个锐角有（ ） 个钝角有（ ） 个图中有5条射线则可以组成4+3+2+1=10个角其中有2个直角5个锐角3个钝角", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数角(角的初步认识)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "过 $A$ 、 $B$ 两点画一条射线射线有一个端点向一个方向无限延伸观察图片点A和点B是两个不同的点根据射线的定义可以选择其中一个点作为端点另一个点在射线上因此可以以点A为端点经过点B画出一条射线即射线AB", "output": "小学数学新知识树\n图形与几何\n平面图形\n直线、射线、线段\n根据长度画线段\n画线段、直线和射线"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "给下面的图形添上一条线段使它符合要求增加 2 个直角增加 3 个直角增加 4 个直角①要增加2个直角则作任意一条边上的垂线段即可增加两个直角注意这条线段不能与其他线段垂直②要增加3个直角则画一条线段与上下两条线段垂直与上面的线段形成一个直角③要增加4个直角则画一条线段与上下两条线段垂直", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n用三角尺画角\n画线添角"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "空白部分是长方形的 $\\frac{( \\quad )}{( \\quad )}$ 阴影部分是长方形的 $\\frac{( \\quad )}{( \\quad )}$ 观察图形整个长方形被平均分成了8个相等的三角形空白部分由3个三角形组成所以空白部分是长方形的 $\\frac{3}{8}$ 阴影部分由5个三角形组成所以阴影部分是长方形的 $\\frac{5}{8}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n分数的初步认识\n认识几分之几"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "有一个锐角在放大倍数是 10 倍的放大镜下看这个角是（ ） A．锐角B．直角C．钝角角的大小只和角的两边张开的程度有关和边长无关用放大镜看角只是边的长度变长了而两边张开的程度并没有改变所以角的大小不变原来这个角是锐角放大后还是锐角", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n角的大小比较\n角的大小比较(初步认识角)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "少先队队员行队礼的标准动作为右手五指并拢高举过头紧贴耳侧掌心朝向左前上方胳膊肘部向身体右侧弯曲神情庄重严肃表示人民的利益高于一切体现了少先队员的爱国情怀和奉献精神此时胳膊肘的角是（ ） 角观察图片中少先队员行队礼的动作右手肘部弯曲形成一个角这个角的开口大于直角（90°）小于平角（180°）因此是钝角", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n认识锐角、钝角"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "鞠躬是一种礼节性动作表示尊敬、谢意或道歉图中小朋友鞠躬时身体上部向前下弯的角度是（ ） A．钝角B．直角C．锐角观察图片中小朋友鞠躬时身体上部与竖直方向形成的夹角这个角度小于90°根据角的分类小于90°的角是锐角等于90°的角是直角大于90°且小于180°的角是钝角因此这个角是锐角", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n认识锐角、钝角"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "阴影部分表示（        ）个是（        ）观察图片长方形被平均分成了3份其中阴影部分占了2份所以阴影部分表示2个 $\\frac{1}{3}$ 即 $\\frac{2}{3}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n分数的初步认识\n认识几分之几"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用分数表示下面的涂色部分（1）第一个图形是一个被平均分成9个小正方形的大正方形其中涂色部分有4个所以涂色部分占整体的 $\\frac{4}{9}$ （2）第二个图形是一个被平均分成3个三角形的大三角形其中涂色部分有2个所以涂色部分占整体的 $\\frac{2}{3}$ （3）第三个图形是一个被平均分成4个长方形的大长方形其中涂色部分有3个所以涂色部分占整体的 $\\frac{3}{4}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n分数的初步认识\n认识几分之几"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下图中有（ ） 条直线（ ） 条线段（ ） 条射线线段 $AC$ 长 （ ） 毫米线段 $BD$ 长（ ） 毫米观察图形图中只有一条直线即经过点A、B、C、D的直线线段是两个端点之间的部分图中有以下线段AB、AC、AD、BC、BD、CD共6条射线有一个端点向一个方向无限延伸以每个点为端点可以向左或向右各形成一条射线共有4个点每个点对应2条射线共8条射线通过测量得线段AC长度为30毫米线段BD长度为50毫米", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数线段、直线和射线"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$\\frac{4}{5}$ 里有（        ） 个 $\\frac{1}{5}$ 再添上 1 个 $\\frac{1}{5}$ 是（        ）1里面有（        ）个（        ）个（        ）个 $\\frac{4}{5}$ 里有 4 个 $\\frac{1}{5}$ 因为 $\\frac{4}{5}=\\frac{1}{5}+\\frac{1}{5}+\\frac{1}{5}+\\frac{1}{5}$ 再添上 1 个 $\\frac{1}{5}$ 就是 $\\frac{4}{5}+\\frac{1}{5}=\\frac{5}{5}=1$ 所以是 1因为 $1=\\frac{3}{3}$ 所以有 3 个 $\\frac{1}{3}$ 因为 $1=\\frac{4}{4}$ 所以有 4 个 $\\frac{1}{4}$ 因为 $1=\\frac{8}{8}$ 所以有 8 个 $\\frac{1}{8}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n分数的初步认识\n认识几分之几"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "请根据时间在钟面上画出时针、分针的位置并判断时针和分针所夹的角是锐角、直角还是钝角 （ ）角 （ ）角 （ ）角 （1）930时时针在9和10之间分针指向6此时时针与分针之间的夹角大于90°所以是钝角（2）1220时时针在12和1之间分针指向4此时时针与分针之间的夹角大于90°所以是钝角（3）300时时针指向3分针指向12此时时针与分针之间的夹角等于90°所以是直角", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n直角、钝角、锐角的认识及特征\n角的初步认识的应用(直角、锐角、钝角)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小妞和大妞吃西瓜①她们一共吃了这个西瓜的几分之几②大妞比小妞多吃了这个西瓜的几分之几③这个西瓜还剩几分之几①小妞吃了西瓜的 $\\frac{2}{9}$ 大妞吃了西瓜的 $\\frac{5}{9}$ 她们一共吃了 $\\frac{2}{9}+\\frac{5}{9}=\\frac{7}{9}$ ②大妞比小妞多吃了 $\\frac{5}{9}-\\frac{2}{9}=\\frac{3}{9}$ ③整个西瓜是1也就是 $\\frac{9}{9}$ 还剩 $1-\\frac{7}{9}=\\frac{9}{9}-\\frac{7}{9}=\\frac{2}{9}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数的加、减法\n分母在10以内分数的简单应用\n同分母分数加、减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "想一想填一填（1）参加跳远活动的总共有（        ） 人（2）男同学占总人数的女同学占总人数的 （1）观察图片数一数参加跳远活动的人数图中共有9个小朋友在参与活动（2）从图中可以看出男生有3人女生有6人总人数是9人男生占总人数的比例是 $\\frac{3}{9}$ 女生占总人数的比例是 $\\frac{6}{9}$", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用虚线分一分再用阴影表示分数（1）第一个图形有8个圆要表示 $\\frac{1}{2}$ 需要将这8个圆平均分成2份每份4个用虚线将图形从中间横着或竖着分成两部分然后给其中一份涂上阴影（2）第二个图形有8个圆要表示 $\\frac{1}{4}$ 需要将这8个圆平均分成4份每份2个用虚线将图形平均分成4列或4行然后给其中一份涂上阴影（3）第三个图形有8个圆要表示 $\\frac{1}{8}$ 需要将这8个圆平均分成8份每份1个用虚线将图形平均分成8个小格每个小格一个圆然后给其中一个小格涂上阴影", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n认识一个整体的几分之几\n认识整体的几分之几(整体是多个物体)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "涂色部分占总数的几分之几                   （1）观察图片共有8个五角星其中涂色的有2个所以涂色部分占总数的 $\\frac{2}{8}$ （2）观察图片共有6个三角形其中涂色的有1个所以涂色部分占总数的 $\\frac{1}{6}$ （3）观察图片共有8个菱形其中涂色的有4个所以涂色部分占总数的 $\\frac{4}{8}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n分数的初步认识\n认识几分之几"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "他们拿的小棒一样多吗请说明理由左边的小男孩说他拿了这堆小棒的 $\\frac{1}{2}$ 图中显示这堆小棒共有6根所以他拿了 $6\\div2=3$ 根右边的小男孩也说他拿了这堆小棒的 $\\frac{1}{2}$ 图中显示这堆小棒共有4根所以他拿了 $4\\div2=2$ 根因为3根不等于2根所以他们拿的小棒不一样多", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "图②有（ ） 条线段（ ） 个直角观察图形这是一个由一个大正方形分割成2个小正方形和2个长方形组成的图形数线段大正方形有4条边以及大正方形内有2条线段一共4+2=6条线段大正方形内的两条线段把大正方形的四条边各分成两条小的线段一共4×2=8条线段大正方形内的两条线段相交又各自分成两条小的线段一共2×2=4条线段因此这个图形一共有6+8+4=18条线段数角大正方形被两条线段分割成2个小正方形和2个长方形每个小正方形和小长方形都是4个角所以一共有4×4=16个角", "output": "小学数学新知识树\n数学竞赛\n计数\n平面图形计数\n数角(角的初步认识)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息跳棋 1880 年在英国创立最初的棋盘是正方形的共有 256 格后面逐渐发展成为六角星形跳棋是一种可以由 $2\\sim6$ 人同时进行的棋每一位玩家使用跳棋一个角拥有一种颜色的棋子它是一种老少咸宜、流传广泛的益智类棋类游戏2、请回答（1）如图一颗珠子占 的（        ）观察图片可以看到棋子排列成一个三角形共有10个位置其中一颗珠子占了其中一个位置因此一颗珠子占整个图形的 $\\frac{1}{10}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "算一算绳长 30 米第二次剪去（        ） 米第一次剪去5米绳子还剩30-5=25米第二次剪去余下的 $\\frac{2}{5}$ 也就是把余下的25米平均分成5份取其中的2份计算是25÷5×2=10米所以第二次剪去10米", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n简单求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息跳棋 1880 年在英国创立最初的棋盘是正方形的共有 256 格后面逐渐发展成为六角星形跳棋是一种可以由 $2\\sim6$ 人同时进行的棋每一位玩家使用跳棋一个角拥有一种颜色的棋子它是一种老少咸宜、流传广泛的益智类棋类游戏2、请回答（3）跳棋使用 6 枚棋子叫＂六子跳棋＂使用 10 枚棋子叫＂十子跳棋＂以此类推同学们正在玩＂十五子跳棋＂（每位玩家使用 15 枚跳棋）已知棋盘上黄色珠子占整个棋盘珠子总数的 $\\frac{1}{6}$ 棋盘上一共有多少个珠子题目中提到同学们正在玩“十五子跳棋”说明每位玩家使用15枚棋子共有6位玩家（因为跳棋是6人游戏）所以棋盘上总共有 $15\\times6=90$ （枚）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息跳棋 1880 年在英国创立最初的棋盘是正方形的共有 256 格后面逐渐发展成为六角星形跳棋是一种可以由 $2\\sim6$ 人同时进行的棋每一位玩家使用跳棋一个角拥有一种颜色的棋子它是一种老少咸宜、流传广泛的益智类棋类游戏2、请回答（2）占整个棋盘珠子的（ ） 观察图片它是一个由棋子组成的三角形共有6行每行的棋子数依次为1、2、3、4、5、6计算这个三角形中棋子的总数$ $1+2+3+4+5+6=21$ $再看整个跳棋盘（图片1）其形状为六角星形共分为6个对称的部分每个部分与图片2中的三角形结构相同因此整个棋盘的棋子总数为$ $6\\times21=126$ $所以图片2中的三角形占整个棋盘棋子的比例为$ $\\frac{21}{126}=\\frac{1}{6}$ $", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一张正方形纸片的边长是 10 厘米现将这张纸片对折再对折每份是这张纸片的 $\\frac{( \\quad )}{( \\quad )}$ 把这张正方形纸片看作一个整体“1”将它对折一次就是把它平均分成了 2 份再对折一次也就是把这 2 份又各自平均分成了 2 份那么总共就平均分成了 $2×2=4$ 份所以每份是这张纸片的 $\\frac{1}{4}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一张长方形纸对折 3 次后每份是它的（        ） A． $\\frac{1}{3}$ B． $\\frac{1}{6}$ C． $\\frac{1}{8}$ D． $\\frac{1}{4}$ 将一张长方形纸对折 $1$ 次这张纸被平均分成 $2$ 份对折 $2$ 次是在对折 $1$ 次的基础上再对折此时纸张被平均分成 $2\\times2=4$ 份对折 $3$ 次是在对折 $2$ 次的基础上再对折那么纸张被平均分成 $4\\times2=8$ 份把这张长方形纸看作单位“ $1$ ”将其平均分成 $8$ 份则每份是它的 $\\frac{1}{8}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "把 20 颗糖果平均分给 5 个小朋友每个小朋友分得（        ） 颗每个小朋友分得这些糖果的 $\\frac{( \\quad )}{( \\quad )}$ 1. 首先求每个小朋友分得的糖果颗数 把 20 颗糖果平均分给 5 个小朋友求每个小朋友分得几颗就是把 20 平均分成 5 份求每份是多少用除法计算即 $20\\div5=4$ （颗）2. 然后求每个小朋友分得这些糖果的几分之几 把 20 颗糖果看作一个整体也就是单位“1” 平均分给 5 个小朋友就是把单位“1”平均分成 5 份那么每个小朋友分得其中的 1 份所以每个小朋友分得这些糖果的 $\\frac{1}{5}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n认识一个整体的几分之几\n认识整体的几分之几(整体是多个物体)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下图各有 12 个 $\\triangle$ 分一分涂一涂分别找到它的 $\\frac{1}{6}$ 和 $\\frac{3}{4}$ 列式比一比涂色个数是否相同（1）求 12 个三角形的 $\\frac{1}{6}$  $12\\div6=2$ （个）所以 $\\frac{1}{6}$ 对应涂色 2 个三角形（2）求 12 个三角形的 $\\frac{3}{4}$ 先求 $\\frac{1}{4}$  $12\\div4=3$ （个）再求 $\\frac{3}{4}$  $3\\times3=9$ （个）所以 $\\frac{3}{4}$ 对应涂色 9 个三角形（3）比较两个涂色个数2 个 $\\ne$ 9 个因此涂色个数不相同", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一条 1 米长的绳子截下它的 应截下（        ） 分米截下它的 $\\frac{1}{2}$ 应截下 （        ） 厘米1. 首先进行单位换算 $1\\text{米}=10\\text{分米}=100\\text{厘米}$ 2. 求截下它的 $\\frac{1}{10}$ 的长度 - 把 1 米长的绳子平均分成 10 份每份是它的 $\\frac{1}{10}$ 1 米 = 10 分米那么每份的长度是 $10\\div10=1\\text{分米}$ 所以截下它的 $\\frac{3}{10}$ 应截下 3 分米3. 求截下它的 $\\frac{1}{2}$ 的长度 - 把 1 米长的绳子平均分成 2 份每份是它的 $\\frac{1}{2}$ 1 米 = 100 厘米那么每份的长度是 $100\\div2=50\\text{厘米}$ 所以截下它的 $\\frac{1}{2}$ 应截下 50 厘米", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息冰箱贴作为一种文化符号承载着丰富的历史文化信息人们通过分享自己拥有的冰箱贴展示自己的旅行经历厦门某文创店购进＂中山路＂冰箱贴 100 个卖了 $\\frac{4}{5}$ 刚好卖出的数量是＂鼓浪屿＂冰箱贴进货数的 $\\frac{1}{6}$ 2、请回答（1）＂中山路＂冰箱贴卖了多少个把“中山路”冰箱贴100个看做单位“1”卖了 $\\frac{4}{5}$ 也就是把100平均分成5份每份100÷5=20个取其中的4份20×4=80个所以“中山路”冰箱贴卖了80个", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n简单求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息冰箱贴作为一种文化符号承载着丰富的历史文化信息人们通过分享自己拥有的冰箱贴展示自己的旅行经历厦门某文创店购进＂中山路＂冰箱贴 100 个卖了 $\\frac{4}{5}$ 刚好卖出的数量是＂鼓浪屿＂冰箱贴进货数的 $\\frac{1}{6}$ 2、请回答（2）＂鼓浪屿＂冰箱贴进货多少个由第一小题可知“中山路”冰箱贴卖了80个因为“中山路”冰箱贴卖出的数量是“鼓浪屿”冰箱贴进货数的 $\\frac{1}{6}$ 所以“鼓浪屿”冰箱贴进货数为80×6 = 480个", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "明明和丽丽谁读的页数多英英是这样解答的因为 $\\frac{1}{7}>\\frac{1}{9}$ 所以明明读的页数多你同意英英的解答吗请写出理由（1）明明读的页数56页的 $\\frac{1}{7}$ 是 $56\\div7=8$ 页（2）丽丽读的页数81页的 $\\frac{1}{9}$ 是 $81\\div9=9$ 页（3）比较两人读的页数8页 < 9页所以丽丽读的页数多英英只比较了分数的大小没有考虑书的总页数不同因此她的解答是错误的", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n简单求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "图中阴影部分占整个图形的几分之几填在括号里（1）观察第一个图形长方形内有两个相同的圆每个圆被分成4等份阴影部分为每个圆的1份共2份两个圆共有8份阴影占2份因此阴影部分占整个图形的 $\\frac{2}{8}=\\frac{1}{4}$ （2）第二个图形是一个长方形内部有一个三角形三角形的底边与长方形的底边相等高也与长方形的高相等根据三角形面积公式三角形面积是长方形面积的一半所以阴影部分占整个图形的 $\\frac{1}{2}$ （3）第三个图形一共有20个小正方形阴影部分包括4个完整的小正方形和4个三角形每个三角形是小正方形的一半因此阴影部分共6个小正方形占整个图形的 $\\frac{6}{20}=\\frac{3}{10}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n分数的初步认识\n认识几分之几"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先涂一涂再比较大小（1）观察第一个图形两个长方形都被平均分成5份● $\\frac{1}{5}$ 表示涂1份 $\\frac{3}{5}$ 表示涂3份因为1份小于3份所以 $\\frac{1}{5}<\\frac{3}{5}$ （2）观察第二个图形两个正方形都被平均分成6份● $\\frac{3}{6}$ 表示涂3份 $\\frac{5}{6}$ 表示涂5份因为3份小于5份所以 $\\frac{3}{6}<\\frac{5}{6}$ （3）观察第三个图形左边正方形被平均分成8份 $\\frac{2}{8}$ 表示涂2份右边正方形被平均分成4份 $\\frac{2}{4}$ 表示涂2份根据涂色面积大小比较左边小于右边所以 $\\frac{2}{8}<\\frac{2}{4}$ （4）观察第四个图形左边圆被平均分成4份 $\\frac{1}{4}$ 表示涂1份右边圆被平均分成2份 $\\frac{1}{2}$ 表示涂1份根据涂色面积大小比较左边小于右边所以 $\\frac{1}{4}<\\frac{1}{2}$ 总结同分母分数比较大小分子大的分数较大同分子分数比较大小分母小的分数较大", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数大小的比较\n同分子分数的大小比较\n分数比大小综合(同分母/同分子)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下图甲、乙两根纸条露出的阴影部分相等你能猜出哪根纸条更长吗从图中可以看出甲、乙两根纸条的阴影部分长度相等已知阴影部分占甲纸条的 $\\frac{1}{3}$ 占乙纸条的 $\\frac{1}{4}$ 因为 $\\frac{1}{3}>\\frac{1}{4}$ 说明甲纸条被分成了3份阴影是其中1份乙纸条被分成了4份阴影也是其中1份在阴影部分长度相同的情况下分得份数越多整条纸条就越长所以乙纸条的总长度比甲纸条长即乙纸条更长", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数大小的比较\n同分子分数的大小比较\n分数比大小综合(同分母/同分子)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先写出阴影部分表示的分数再比较大小第一组图形左边的正方形被对角线分成两部分阴影部分占其中1份共2份所以阴影部分表示的分数是 $\\frac{1}{2}$ 右边的长方形被横线分成两部分阴影部分占其中1份共2份所以阴影部分表示的分数也是 $\\frac{1}{2}$ 因此 $\\frac{1}{2}$ = $\\frac{1}{2}$ 第二组图形左边的圆被直径分成两部分阴影部分占其中1份共2份所以阴影部分表示的分数是 $\\frac{1}{2}$ 右边的圆被平均分成3份阴影部分占其中1份所以阴影部分表示的分数是 $\\frac{1}{3}$ 因为 $\\frac{1}{2}$ 大于 $\\frac{1}{3}$ 所以 $\\frac{1}{2}$ > $\\frac{1}{3}$ 第三组图形左边的长方形被横线分成两部分阴影部分占其中1份共2份所以阴影部分表示的分数是 $\\frac{1}{2}$ 右边的长方形被平均分成8份阴影部分占其中1份所以阴影部分表示的分数是 $\\frac{1}{8}$ 因为 $\\frac{1}{2}$ 大于 $\\frac{1}{8}$ 所以 $\\frac{1}{2}$ > $\\frac{1}{8}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数大小的比较\n同分子分数的大小比较\n分数比大小综合(同分母/同分子)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "看图填一填比一比第一幅图中左边部分表示 $\\frac{2}{7}$ 右边部分表示 $\\frac{3}{7}$ 两部分合起来是 $\\frac{2}{7}+\\frac{3}{7}=\\frac{5}{7}$ 第二幅图中整体是 $\\frac{5}{7}$ 其中右边部分是 $\\frac{3}{7}$ 所以左边部分为 $\\frac{5}{7}-\\frac{3}{7}=\\frac{2}{7}$ 因此填空如下（ $\\frac{2}{7}$ ）+（ $\\frac{3}{7}$ ）=（ $\\frac{5}{7}$ ）（ $\\frac{5}{7}$ ）-（ $\\frac{3}{7}$ ）=（ $\\frac{2}{7}$ ）", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数的加、减法\n分母在10以内分数的简单应用\n同分母分数加、减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在 $\\bigcirc$ 里填上＂ $>$ ＂＂ $<$ ＂或＂ $=$ ＂ $\\frac{3}{4}\\bigcirc\\frac{1}{4}$ $\\frac{1}{2}\\bigcirc\\frac{1}{3}$ $\\frac{9}{10}\\bigcirc1$ $\\frac{5}{5}\\bigcirc\\frac{4}{4}$", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数大小的比较\n同分子分数的大小比较\n分数比大小综合(同分母/同分子)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "估算 $497\\times4=$ 时可以把 497 看成（ ） 积约是（ ） 在估算乘法时为了方便计算我们会把一个乘数看成和它接近的整十、整百数497 接近 500所以把 497 看成 500那么 500×4 = 2000所以积约是 2000", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n两、三位数乘一位数的估算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "学校开展＂阳光体育快乐足球＂嘉年华比赛活动三年级有 5 个班如果每两个班踢一场一共需要踢多少场请借助表格帮助体育老师写出比赛对阵表你发现了什么（借助表格在需要对决赛中画＂√＂）\n| 班级 | 三（1） | 三（2） | 三（3） | 三（4） | 三（5） | 三（1） |   |   |   |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 班级 | 三（1） | 三（2） | 三（3） | 三（4） | 三（5） |\n| 三（1） |   |   |   |   |   |\n| 三（2） |   |   |   |   |   |\n| 三（3） |   |   |   |   |   |\n| 三（4） |   |   |   |   |   |\n| 三（5） |   |   |   |   |   |\n我们来一个一个分析每个班要踢的场数三（1）班要和三（2）班、三（3）班、三（4）班、三（5）班各踢一场一共踢 4 场三（2）班已经和三（1）班踢过了所以它只要再和三（3）班、三（4）班、三（5）班踢踢 3 场三（3）班已经和三（1）班、三（2）班踢过了它再和三（4）班、三（5）班踢踢 2 场三（4）班已经和三（1）班、三（2）班、三（3）班踢过了它再和三（5）班踢 1 场三（5）班前面都和其他班踢过了把这些场数加起来 $4+3+2+1=10$ 场从这里我们能发现规律如果有 n 个班比赛总场数就是 $( n-1 )+( n-2 )+…+1$ ", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n排列组合问题\n组合思想解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小明读一本 400 页的书每天读 70 页第 3 天他应从第（ ） 页读起先算出前两天读的页数每天读 70 页两天就读了 70×2 = 140 页那么第三天开始读的页数应该是前两天读的页数的下一页也就是 140+1 = 141 页", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n运用口算乘法解决实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在 $\\bigcirc$ 里填上＂ $>$ ＂＂ $<$ ＂或＂ $=$ ＂3 千米 $\\bigcirc$ 2999米            2 千克 20 克 $\\bigcirc$ 2020克            10000 千克 $\\bigcirc$ 1 吨 $\\frac{6}{7}\\bigcirc\\frac{4}{7}$                                  $\\frac{4}{10}\\bigcirc\\frac{4}{9}$                                     1 $\\bigcirc$ $\\frac{4}{4}$ 1. 因为1千米 = 1000米那么3千米就是3个1000米也就是3000米3000米比2999米大所以3千米＞2999米2. 因为1千克 = 1000克2千克就是2个1000克即2000克2千克20克就是2000克加上20克等于2020克所以2千克20克 = 2020克3. 因为1吨 = 1000千克10000千克比1000千克大所以10000千克＞1吨4. 分母相同的分数分子越大分数就越大 在 $\\frac{6}{7}$ 和 $\\frac{4}{7}$ 中分母都是76比4大所以 $\\frac{6}{7}$ ＞ $\\frac{4}{7}$ 5. 分子相同的分数分母越大分数反而越小在 $\\frac{4}{10}$ 和 $\\frac{4}{9}$ 中分子都是410比9大所以 $\\frac{4}{10}$ ＜ $\\frac{4}{9}$ 6. 把1可以写成 $\\frac{4}{4}$ 因为 $\\frac{4}{4}$ 表示把一个东西平均分成4份取4份就是整个东西也就是1所以1 = $\\frac{4}{4}$ ", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量单位的换算\n吨、千克之间的换算与比较\n质量单位比较大小(吨、千克、克)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在 $\\bigcirc$ 里填上＂ $>$ ＂＂ $<$ ＂或＂ $=$ ＂3 千米 $\\bigcirc$ 2999米            2 千克 20 克 $\\bigcirc$ 2020克            10000 千克 $\\bigcirc$ 1 吨 $\\frac{6}{7}\\bigcirc\\frac{4}{7}$                                  $\\frac{4}{10}\\bigcirc\\frac{4}{9}$                                     1 $\\bigcirc$ $\\frac{4}{4}$ 1. 因为1千米 = 1000米那么3千米就是3个1000米也就是3000米3000米比2999米大所以3千米＞2999米2. 因为1千克 = 1000克2千克就是2个1000克即2000克2千克20克就是2000克加上20克等于2020克所以2千克20克 = 2020克3. 因为1吨 = 1000千克10000千克比1000千克大所以10000千克＞1吨4. 分母相同的分数分子越大分数就越大 在 $\\frac{6}{7}$ 和 $\\frac{4}{7}$ 中分母都是76比4大所以 $\\frac{6}{7}$ ＞ $\\frac{4}{7}$ 5. 分子相同的分数分母越大分数反而越小在 $\\frac{4}{10}$ 和 $\\frac{4}{9}$ 中分子都是410比9大所以 $\\frac{4}{10}$ ＜ $\\frac{4}{9}$ 6. 把1可以写成 $\\frac{4}{4}$ 因为 $\\frac{4}{4}$ 表示把一个东西平均分成4份取4份就是整个东西也就是1所以1 = $\\frac{4}{4}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数大小的比较\n同分子分数的大小比较\n分数比大小综合(同分母/同分子)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一筐苹果连筐重 50 千克卖了一半后连筐重 26 千克这筐苹果有多重筐有多重首先我们知道一开始苹果连筐重 50 千克卖了一半苹果后连筐重 26 千克那减少的重量就是卖掉的一半苹果的重量用 50 - 26 = 24 千克这 24 千克就是一半苹果的重量所以整筐苹果的重量就是 24 × 2 = 48 千克然后用一开始苹果连筐的总重量 50 千克减去苹果的重量 48 千克就能算出筐重 2 千克", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量间的计算和应用\n千克与克的相关应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小明从家到学校要走 1500 米他走了 200 米后又回家拿了一本书这样他比平时上学要多走（ ） 米小明走了200米后又回家拿书那么他多走的路程就是往返这200米的距离即200×2=400米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n解决千米相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面的质量与 1 千克最接近的是（ ）A． 1 千克 2 克B． 999 克C． 9999 克1千克 = 1000克选项A中1千克2克就是1002克它和1000克相差1002 - 1000 = 2克选项B中999克和1000克相差1000 - 999 = 1克选项C中9999克和1000克相差9999 - 1000 = 8999克1克＜2克＜8999克所以999克和1千克最接近答案选B", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量单位的换算\n克、千克之间的换算与比较\n千克和克之间的进率及换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "三（1）班学生李丽的身高是 14（ ）体重是 32（ ）我们在生活中量身高一般用厘米或者分米作单位14厘米太矮了不符合三年级学生的身高情况所以李丽身高是14分米称体重通常用千克作单位所以体重是32千克", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(分米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "在括号里填上合适的单位飞机每小时飞行约 800（ ） 你的书包大约重3（ ）一本新华字典厚约 3（ ） 一台冰箱高 1（ ） 80（ ） 我们来想想生活中的实际情况飞机飞得特别快每小时飞行的距离很长所以用千米做单位合适飞机每小时飞行约800千米书包是我们日常背的有一定重量用千克做单位合适书包大约重3千克新华字典比较薄量薄的东西一般用厘米做单位一本新华字典厚约3厘米冰箱比较高量比较高的物体一般用米和厘米做单位一台冰箱高1米80厘米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(千米、米、厘米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "周六上午爸爸、妈妈带小明步行到距离家2（ ） 的胡里山炮台游玩景区原规定身高在 120（ ）及以下的儿童免收门票现改为 18 周岁以下的游客均免费在胡里山炮台我看到了长约 13 米、高 5 米、重约 87（ ） 的大炮台好大好重哟在生活中计量家到较远地方的距离一般用千米作单位所以家到胡里山炮台的距离是2千米计量人的身高通常用厘米作单位儿童身高120厘米符合实际计量很重的物体重量常用吨作单位大炮台很重重约87吨是合理的", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(千米、米、厘米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "笔算 $193\\times2=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad890\\times7=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad603\\times5=$ ①计算193×2用2分别去乘193的每一位2乘3得62乘90得1802乘100得200加起来就是386②计算890×7先算89×77乘9得637乘80得560560加63是623再在末尾添上一个0就是6230③计算603×55乘3得155乘0得05乘600得3000加起来就是3015", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数的一次进位乘法\n两、三位数乘一位数(不连续进位)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "笔算 $193\\times2=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad890\\times7=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad603\\times5=$ ①计算193×2用2分别去乘193的每一位2乘3得62乘90得1802乘100得200加起来就是386②计算890×7先算89×77乘9得637乘80得560560加63是623再在末尾添上一个0就是6230③计算603×55乘3得155乘0得05乘600得3000加起来就是3015", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n因数中间或末尾是0的乘法\n三位数乘一位数(乘数中间有0)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "大熊猫是中国的国宝它属于食肉目、熊科、大熊猫亚科、大熊猫属是该属唯一的哺乳动物其身体长度在 $1.2\\sim1.8$ （ ）之间尾巴长约 $10\\sim12$ （ ）体重通常在 $80\\sim120$ （ ）范围内最重的个体可以达到 180（ ）体色呈现独特的黑白双色我们在生活中测量比较长的物体像大熊猫的身体长度一般用米做单位所以大熊猫身体长度在1.2 - 1.8米之间尾巴相对身体比较短测量短的物体长度一般用厘米所以尾巴长约10 - 12厘米而表示物体有多重要用质量单位像大熊猫的体重一般用千克做单位所以体重通常在80 - 120千克范围内最重个体能达到180千克", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(分米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "有＂海洋之舟＂美称的企鹅是一种最古老的游禽共有 18 个独立物种体型最大的物种是帝企鹅平均高约 1．1（ ） 体重 35（ ） 以上最小的企鹅物种是小蓝企鹅（又称神仙企鹅）体高约 40（ ）体重约 1（ ）在水里企鹅那短小的翅膀成了一双强有力的＂划桨＂游速可达每小时 $25\\sim30$ （ ） 一天可游160（ ） 帝企鹅体型大高度用米作单位合适所以平均高约1.1米体重用千克作单位合适所以体重35千克以上小蓝企鹅体型小体高用厘米作单位合适所以体高约40厘米体重约1千克描述游速也就是每小时游的路程用千米作单位合适所以游速可达每小时25 - 30千米一天游的路程也用千米作单位合适所以一天可游160千米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的选择\n选择合适的长度单位(千米、米、厘米、毫米)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "6000 米＝（ ） 千米 $\\quad5$ 分米＝（ ） 毫米 $\\quad80$ 千米＝（ ） 米3 千米 50 米 $=( \\,\\,\\,\\,\\,\\,\\,\\,\\quad )$ 米 $\\quad2500$ 米 $=\\,( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )$ 千米（ ） 米我们知道1千米 = 1000米1分米 = 100毫米6000米换算成千米因为6000里有6个1000所以6000米 = 6千米5分米换算成毫米1分米是100毫米5分米就是5个100毫米也就是500毫米80千米换算成米1千米是1000米80千米就是80个1000米即80000米3千米50米换算成米3千米是3000米再加上50米就是3050米2500米换算成千米和米2000米是2千米还剩500米所以2500米 = 2千米500米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n千米与米的换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "竖式计算 $653\\times5=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad460\\times9=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad703\\times7=$ ① $653\\times5$ 先用5乘3得15个位写5向十位进1再用5乘5得25加上进位的1是26十位写6向百位进2最后用5乘6得30加上进位的2是32结果是3265② $460\\times9$ 先算 $46\\times9$ 6乘9得54个位写4向十位进54乘9得36加上进位的5是41结果是414再在末尾添上0就是4140③ $703\\times7$ 先用7乘3得21个位写1向十位进20乘7得0加上进位的2是2十位写27乘7得49结果就是4921", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数连续进位的乘法\n两、三位数乘一位数(连续进位)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "竖式计算 $653\\times5=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad460\\times9=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad703\\times7=$ ① $653\\times5$ 先用5乘3得15个位写5向十位进1再用5乘5得25加上进位的1是26十位写6向百位进2最后用5乘6得30加上进位的2是32结果是3265② $460\\times9$ 先算 $46\\times9$ 6乘9得54个位写4向十位进54乘9得36加上进位的5是41结果是414再在末尾添上0就是4140③ $703\\times7$ 先用7乘3得21个位写1向十位进20乘7得0加上进位的2是2十位写27乘7得49结果就是4921", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n因数中间或末尾是0的乘法\n三位数乘一位数(乘数中间有0)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "5 厘米 =（ ） 毫米600 分米＝（ ） 米1 千米 $-$ 30 米＝（ ） 米1 吨 $-$ 600 千克＝（ ） 千克1厘米等于10毫米5厘米就是5个10毫米也就是50毫米10分米等于1米600分米里面有60个10分米所以600分米等于60米1千米等于1000米1000米减30米等于970米1吨等于1000千克1000千克减600千克等于400千克", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n分米、厘米和毫米之间的单位计算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "7吨=（ ）千克 50000千克=（ ）吨 4公斤=（ ）克4千克50克=（ ）克 18斤=（ ）公斤250千克=750千克=（ ）吨 1吨-460千克=（ ）千克1吨 = 1000千克所以7吨就是7个1000千克也就是7000千克50000千克里面有50个1000千克所以是50吨1公斤 = 1千克1千克 = 1000克那么4公斤就是4千克也就是4000克4千克是4000克再加上50克4千克50克就是4050克2斤 = 1公斤18斤里面有9个2斤所以18斤是9公斤250千克+750千克是1000千克1000千克就是1吨1吨是1000千克1000千克-460千克=540千克", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量单位的换算\n吨、千克之间的换算与比较\n吨、千克和克之间的进率及换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "操场跑道 1 圈是 400 米小丽跑了 2 圈后还差（ ） 米就是 1 千米A． 200B． 600C． 800首先算出小丽跑的距离1圈400米跑了2圈那就是2个400米400+400 = 800米因为1千米 = 1000米用1000米减去小丽跑的800米1000 - 800 = 200米所以还差200米就是1千米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n解决千米相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "10 厘米＝（ ） 毫米 1 吨 600 千克— 600 千克＝（ ） 千克4 千米 — 100 米 $\\,=( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )$ 米 1000 毫米 $\\,=( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )$ 分米1. 1厘米=10毫米那10厘米就是10个10毫米10×10 = 100所以10厘米=100毫米2. 1吨=1000千克所以1吨600千克就是1000千克+600千克=1600千克1600千克-600千克=1000千克3. 1千米=1000米所以4千米就是4个1000米也就是4000米4000米-100米=3900米4. 100毫米=1分米1000毫米里面有10个100毫米所以1000毫米=10分米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n毫米、厘米、分米、米之间的进率与换算\n分米、厘米和毫米之间的单位换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "10 厘米＝（ ） 毫米 1 吨 600 千克— 600 千克＝（ ） 千克4 千米 — 100 米 $\\,=( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )$ 米 1000 毫米 $\\,=( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )$ 分米1. 1厘米=10毫米那10厘米就是10个10毫米10×10 = 100所以10厘米=100毫米2. 1吨=1000千克所以1吨600千克就是1000千克+600千克=1600千克1600千克-600千克=1000千克3. 1千米=1000米所以4千米就是4个1000米也就是4000米4000米-100米=3900米4. 100毫米=1分米1000毫米里面有10个100毫米所以1000毫米=10分米", "output": "小学数学新知识树\n图形与几何\n测量\n常见的长度单位及换算\n长度单位的换算\n千米和米之间的进率与换算\n千米与米的换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "10 厘米＝（ ） 毫米 1 吨 600 千克— 600 千克＝（ ） 千克4 千米 — 100 米 $\\,=( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )$ 米 1000 毫米 $\\,=( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )$ 分米1. 1厘米=10毫米那10厘米就是10个10毫米10×10 = 100所以10厘米=100毫米2. 1吨=1000千克所以1吨600千克就是1000千克+600千克=1600千克1600千克-600千克=1000千克3. 1千米=1000米所以4千米就是4个1000米也就是4000米4000米-100米=3900米4. 100毫米=1分米1000毫米里面有10个100毫米所以1000毫米=10分米", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量单位的换算\n吨、千克之间的换算与比较\n吨、千克和克之间的进率及换算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "脱式计算 $\n48\\div6\\times7\n$ $\n88-64\\div8\n$ $\n92-( 23-18 )\n$ ①计算 48÷6×7在没有括号的乘除混合运算中按照从左到右的顺序依次计算先算除法48÷6=8再算乘法8×7=56②计算 88-64÷8在没有括号的除减混合运算中先算除法再算减法先算除法64÷8=8再算减法88-8=80③计算 92-( 23-18 )有括号的运算先算括号里面的再算括号外面的先算括号里的减法23-18=5再算括号外的减法88-640", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n不带括号的混合运算\n不含括号的四则混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一根绳子长 40 米明明对折 3 次每段是这根绳子的 $\\frac{( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )}{( \\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad )}$ 是（ ） 米对折 1 次绳子被平均分成 2 段对折 2 次绳子被平均分成 $2×2=4$ 段对折 3 次绳子被平均分成 $2×2×2=8$ 段把这根绳子看作整体“1”平均分成 8 段每段就是这根绳子的 $\\frac{1}{8}$ 绳子长 40 米平均分成 8 段每段的长度就是 $40÷8=5$ 米", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n利用整体的几分之几解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用一副三角尺不可能拼出（ ） A．锐角B．直角C．钝角一副三角尺有两个三角形度数分别是90度、60度、30度和90度、45度、45度用三角尺拼角时比如30度和45度拼在一起是75度75度是锐角所以能拼出锐角用90度和30度拼在一起是120度120度是钝角所以能拼出钝角因此不可能拼出直角选B", "output": "小学数学新知识树\n图形与几何\n平面图形\n角\n用三角尺画角\n三角尺拼角"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "（1） $4\\times9=36\\quad56-36=20$ 综合算式 ___（2） $22+23=45\\quad58-45=13$ 综合算式 ___（1）先算 $4×9=36$ 再用 $56$ 减去它们的积得到 $20$ 所以综合算式就是 $56-4×9=20$ （2）先算 $22+23=45$ 再用 $58$ 减去它们的和得到 $13$ 因为要先算加法所以要给加法加上括号综合算式就是 $58-( 22+23 )=13$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n根据分步式列综合式\n根据分步运算列综合算式(含括号)(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "＂滴水凑成河粒米凑成箩文明用餐节俭惜福＂如果每人每天节约 125 克粮食那么张亮一家三口一周（7天）可以节约多少克粮食先算一家三口一天节约多少粮食就是3个125克用乘法125×3 = 375克再算一周7天节约多少粮食就是7个375克用乘法375×7 = 2625克所以张亮一家三口一周可以节约2625克粮食", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量间的计算和应用\n千克与克的相关应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "学校印了 120 份节能手册组织了 5 名同学为家长免费发放 90 份 ___ 如果列式为 $( 120-90 )\\div5=6$ （份）请在横线上提问题题目里学校印了120份节能手册5名同学发了90份120 - 90算的是剩下的手册份数再除以5就是把剩下的手册平均分给5名同学算的是剩下的平均每名同学发放多少份", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n除加、除减混合运算\n除加除减应用题(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "李丽做口算题训练每行有 5 题有 8 行 ___ 已经做了多少题如果列式为 $5\\times8-3$ 请在横线上补充条件题目里 $5×8$ 算的是一共有多少题用总共的题数减去没做的题数就能得到已经做了的题数算式是 $5×8-3$ 所以没做的题数是 3 题也就是还剩 3 题没做", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "下面算式得数与其他三个不相同的是（ ） A． $56-12-35$ B． $56-( 35+12 )$ C． $56-( 35-12 )$ D． $56-35-12$ 先看A选项56 - 12 - 35就是从56里先减掉12再减掉35D选项56 - 35 - 12是从56里先减掉35再减掉12这两个选项只是减的顺序不同结果是一样的再看B选项56 - ( 35 + 12 )根据去括号的知识它和A、D其实是一样的意思都是从56里减掉12和35这两个数的和而C选项56 - ( 35 - 12 )它是从56里减掉35和12的差和前面三个选项意思不一样所以得数也不同", "output": "小学数学新知识树\n数学竞赛\n算式谜，数阵，进位制\n巧填运算符号\n减号后添/去括号"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小林读一本故事书 4 天读了 32 页照这样的速度 7 天可以读多少页如果全书 72页几天可以读完先看第一个问题4天读了32页那一天读的页数就是32除以4等于8页照这样的速度7天读的页数就是一天读的页数乘7也就是8乘7等于56页再看第二个问题已经知道一天读8页全书72页读完需要的天数就是72除以一天读的8页得到9天", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一根木头锯成 3 段需要 6 分钟如果把这根木头锯成 6 段需要多少分钟我们先想想把木头锯成 3 段其实只需要锯 2 次锯 2 次用了 6 分钟那锯 1 次用的时间就是 6 ÷ 2 = 3 分钟要是把木头锯成 6 段那就需要锯 5 次锯 1 次是 3 分钟锯 5 次就是 3 × 5 = 15 分钟", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "欢欢在计算＂ $9+\\square\\div3$ ＂时弄错运算顺序先算加法再算除法结果得数是 7 正确的计算结果应该是（ ）A． 12B． 9C． 13D． 16", "output": "小学数学新知识树\n数学竞赛\n计算\n错中求解\n错中求解(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $800\\times6=$ $40\\div5=$ $430+170=$ $401\\times5\\approx$ $280-50=$ $340\\times0=$ $43\\times5=$ $1-\\frac{11}{12}=$ 1. 计算 $800×6$ 8个百乘6是48个百也就是48002. 计算 $40÷5$ 根据乘法口诀“五八四十”所以 $40÷5=8$ 3. 计算 $430+170$ 43个十加17个十是60个十也就是6004. 估算 $401×5$ 把401看成400 $400×5=2000$ 所以 $401×5≈2000$ 5. 计算 $280-50$ 28个十减5个十是23个十也就是2306. 计算 $340×0$ 任何数乘0都得0所以 $340×0=0$ 7. 计算 $43×5$ 先算 $40×5=200$ 再算 $3×5=15$ 最后 $430+1700$ 8. 计算 $1-\\frac{11}{12}$ 把1看成 $\\frac{12}{12}$  $\\frac{12}{12}-\\frac{11}{12}=\\frac{1}{12}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $800\\times6=$ $40\\div5=$ $430+170=$ $401\\times5\\approx$ $280-50=$ $340\\times0=$ $43\\times5=$ $1-\\frac{11}{12}=$ 1. 计算 $800×6$ 8个百乘6是48个百也就是48002. 计算 $40÷5$ 根据乘法口诀“五八四十”所以 $40÷5=8$ 3. 计算 $430+170$ 43个十加17个十是60个十也就是6004. 估算 $401×5$ 把401看成400 $400×5=2000$ 所以 $401×5≈2000$ 5. 计算 $280-50$ 28个十减5个十是23个十也就是2306. 计算 $340×0$ 任何数乘0都得0所以 $340×0=0$ 7. 计算 $43×5$ 先算 $40×5=200$ 再算 $3×5=15$ 最后 $430+1700$ 8. 计算 $1-\\frac{11}{12}$ 把1看成 $\\frac{12}{12}$  $\\frac{12}{12}-\\frac{11}{12}=\\frac{1}{12}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n两、三位数乘一位数的估算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $800\\times6=$ $40\\div5=$ $430+170=$ $401\\times5\\approx$ $280-50=$ $340\\times0=$ $43\\times5=$ $1-\\frac{11}{12}=$ 1. 计算 $800×6$ 8个百乘6是48个百也就是48002. 计算 $40÷5$ 根据乘法口诀“五八四十”所以 $40÷5=8$ 3. 计算 $430+170$ 43个十加17个十是60个十也就是6004. 估算 $401×5$ 把401看成400 $400×5=2000$ 所以 $401×5≈2000$ 5. 计算 $280-50$ 28个十减5个十是23个十也就是2306. 计算 $340×0$ 任何数乘0都得0所以 $340×0=0$ 7. 计算 $43×5$ 先算 $40×5=200$ 再算 $3×5=15$ 最后 $430+1700$ 8. 计算 $1-\\frac{11}{12}$ 把1看成 $\\frac{12}{12}$  $\\frac{12}{12}-\\frac{11}{12}=\\frac{1}{12}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数的加、减法\n分母在10以内的同分母分数加、减法\n同分母分数加、减法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一本笔记本 9 元一支笔 6 元 2 本笔记本的价钱可以买多少支笔解决这个问题首先要解决（ ） A． 2 支笔多少元B． 1 本笔记本多少元C． 2 本笔记本多少元D． 1 支笔多少钱可以列综合算式为（ ）A． $12\\times6\\div9$ B． $2\\times9\\div6$ C． $9\\times6\\div2$ D． $6\\div2\\times9$ 要知道2本笔记本的价钱能买多少支笔得先知道2本笔记本多少钱所以第一个空选C一本笔记本9元2本笔记本就是2×9元一支笔6元用2本笔记本的总价钱除以一支笔的价钱就能算出可以买笔的数量综合算式就是2×9÷6所以第二个空选B", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n用两步计算解决问题(表内混合运算)(不含括号)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "陈大爷家一共收了 410 千克苹果一个箱子最多能装 46 千克 8 个箱子能装下这些苹果吗下面解答比较合理的是（ ） A． $46\\times8\\approx320$ （千克） $46\\times8410$ 所以能装下这些苹果D． $46\\times8\\approx500$ （千克） $46\\times8>410$ 所以能装下这些苹果要判断8个箱子能不能装下410千克苹果得算出8个箱子大约能装多少千克把46看成和它接近的整十数5050×8 = 400千克因为把46看大了才装400千克实际46×8肯定小于400千克而400千克小于410千克所以8个箱子装不下这些苹果B选项的方法合理", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n两、三位数乘一位数的估算应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "用下面两辆汽车运 16 吨沙子如果每次每辆车都装满怎样安排恰好能运完请试着填写表格\n| 派车方案 | 载质量5 吨 | 载质量3 吨 | 运沙吨数 |   |   |   |   |   |   |   |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 派车方案 | 载质量5 吨 | 载质量3 吨 | 运沙吨数 |\n|   |   |   |   |\n|   |   |   |   |\n|   |   |   |   |\n|   |   |   |   |\n从载质量5吨的车0辆开始考虑如果载质量5吨的车0辆那就要用载质量3吨的车16÷3 = 5（辆）……1（吨）5辆车装不完需要6辆车能运3×6 = 18吨如果载质量5吨的车1辆能运5吨还剩16 - 5 = 11吨11÷3 = 3（辆）……2（吨）3辆车装不完需要4辆车一共能运5 + 3×4 = 17吨如果载质量5吨的车2辆能运5×2 = 10吨还剩16 - 10 = 6吨6÷3 = 2辆正好可以用2辆载质量3吨的车一共运3 = 50吨如果载质量5吨的车3辆能运5×3 = 51吨还剩3 = 52吨需要1辆载质量3吨的车一共运15 + 3 = 18吨如果载质量5吨的车4辆能运5×3 = 54吨所以恰好能运完16吨沙子的方案是载质量5吨的车2辆载质量3吨的车2辆", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量间的计算和应用\n解决吨相关的实际问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "某理发店经理到药店购买医用外科口罩每箱 1500 只需 560 元每满 1000 元减 50 元如果他买了 6 箱需付多少钱先算买 6 箱口罩的总价每箱 560 元6 箱就是 560×6 = 3360 元再看总价里有几个 1000 元3360÷1000 = 3（个）……360（元）说明有 3 个 1000 元能优惠 3×50 = 150 元最后用总价减去优惠的钱3360 - 150 = 3210 元就是需要付的钱", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n优惠策略"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "想一想画一画 $( 4+2 )\\times3$ 能解决什么问题先看括号里的4 + 2它表示把4和2合起来就像把小明的4个苹果和小红的2个苹果放在一起再乘3就是有3组这样合起来的苹果数量所以可以用( 4 + 2 )×3来算出总的苹果数", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n乘加、乘减混合运算\n乘加乘减应用题(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息张大伯家养了 150 只小鸡2、请回答（2）剩下的小鸡第二天卖如果每 8 只小鸡装一笼需要多少个笼子首先算出剩下的小鸡数量张大伯家一共养了150只小鸡今天卖了102只所以剩下的小鸡数量为150-102=48（只）又因为每8只小鸡装一笼那么需要的笼子数为48÷8=6（个）", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n除加、除减混合运算\n除加除减应用题(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小红一家三口去果园摘桃子全家一共摘了 254 个每盒装 8 个一共有 29 个盒子够装吗我们先算29个盒子能装多少个桃子每个盒子装8个29个盒子能装的桃子数就是29个8用乘法算29×8 我们可以把29看成3030×8 = 240因为29比30小所以29×8＜240 而全家摘了254个桃子240＜254所以29个盒子不够装", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n两、三位数乘一位数的估算应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "淘淘只有 30 克和 50 克这两种规格的油瓶妈妈让他倒出 70 克的油请你帮淘淘想想该怎么倒出 70 克的油（用算式表示）我们要得到 70 克油因为只有 30 克和 50 克的油瓶我们可以先利用 50 克油瓶和 30 克油瓶得到 20 克油先把 50 克的油瓶倒满再从 50 克油瓶里倒出 30 克油到 30 克油瓶中此时 50 克油瓶里剩下 20 克油然后把 30 克油瓶里的油倒掉将 50 克油瓶里剩下的 20 克油倒入 30 克油瓶中再把 50 克油瓶倒满这时两个油瓶里的油合起来就是 70 克", "output": "小学数学新知识树\n数与代数\n常见的量\n质量单位及换算\n质量间的计算和应用\n千克与克的相关应用题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息游乐场四月份上旬接待游客 580 人中旬接待游客 395 人下旬和中旬接待的游客总数是上旬的 2 倍2、请回答（2）乘坐摩天轮的人数是玩碰碰车人数的 $\\frac{5}{6}$  ___ 乘坐摩天轮的人数是多少人（请补充条件并列式解答）题目问乘坐摩天轮的人数已知乘坐摩天轮的人数是玩碰碰车人数的 $\\frac{5}{6}$ 那我们得知道玩碰碰车的人数才能算出乘坐摩天轮的人数所以补充玩碰碰车的人数这个条件假如补充玩碰碰车的人数是 36 人要求乘坐摩天轮的人数就是求 36 的 $\\frac{5}{6}$ 是多少用乘法计算就是 $36×\\frac{5}{6}=30$ 人", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n简单求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "妈妈买了一箱水蜜桃 24 个已经吃的数量不可能是这箱水蜜桃的（ ） A． $\\frac{1}{2}$ B． $\\frac{3}{4}$ C． $\\frac{5}{7}$ D． $\\frac{2}{3}$ A选项24的 $\\frac{1}{2}$ 就是把24平均分成2份每份是12个能吃这么多B选项24的 $\\frac{3}{4}$ 就是把24平均分成4份每份6个3份就是18个也能吃这么多D选项24的 $\\frac{2}{3}$ 把24平均分成3份每份8个2份就是16个也可以吃这么多而C选项把24平均分成7份24不能被7整除分不出完整的份数所以吃的数量不可能是这箱水蜜桃的 $\\frac{5}{7}$ ", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数的意义、读写及分类\n认识一个整体的几分之几\n认识整体的几分之几(整体是多个物体)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$\\frac{1}{4}、\\frac{1}{5}$ 和 $\\frac{3}{5}$ 比大小最小的分数是（ ） A． $\\frac{1}{4}$ B． $\\frac{1}{5}$ C． $\\frac{3}{5}$ D．无法确定比较分数大小的时候分母相同比分子分子大的分数大分子相同比分母分母小的分数大先看 $\\frac{1}{5}$ 和 $\\frac{3}{5}$ 它们分母相同分子1小于3所以 $\\frac{1}{5}\\lt\\frac{3}{5}$ 再看 $\\frac{1}{4}$ 和 $\\frac{1}{5}$ 它们分子相同分母4小于5所以 $\\frac{1}{5}\\lt\\frac{1}{4}$ 综合可得 $\\frac{1}{5}$ 最小", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数大小的比较\n同分子分数的大小比较\n分数比大小综合(同分母/同分子)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "笔算 $143\\times4=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad480\\times7=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad205\\times8=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad999\\times4=$ 计算 $143\\times4$ 先算 $3\\times4=12$ 个位写2向十位进1再算 $4\\times4=16$ 加上进位的1得17十位写7向百位进1最后算 $1\\times4=4$ 加上进位的1得5结果就是572计算 $480\\times7$ 时先算 $48\\times7$  $8\\times7=56$ 个位写6向十位进5 $4\\times7=28$ 加上进位的5得33结果是336再在336的末尾添上1个0所以 $480\\times7=3360$ 计算 $205\\times8$  $5\\times8=40$ 个位写0向十位进4 $0\\times8=0$ 加上进位的4得4十位写4 $2\\times8=16$ 结果是1640计算 $999\\times4$  $4\\times4=120$ 个位写6向十位进3 $4\\times4=120$ 加上进位的3得39十位写9向百位进3 $4\\times4=120$ 加上进位的3得39结果是3996", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数连续进位的乘法\n两、三位数乘一位数(连续进位)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "笔算 $143\\times4=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad480\\times7=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad205\\times8=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad999\\times4=$ 计算 $143\\times4$ 先算 $3\\times4=12$ 个位写2向十位进1再算 $4\\times4=16$ 加上进位的1得17十位写7向百位进1最后算 $1\\times4=4$ 加上进位的1得5结果就是572计算 $480\\times7$ 时先算 $48\\times7$  $8\\times7=56$ 个位写6向十位进5 $4\\times7=28$ 加上进位的5得33结果是336再在336的末尾添上1个0所以 $480\\times7=3360$ 计算 $205\\times8$  $5\\times8=40$ 个位写0向十位进4 $0\\times8=0$ 加上进位的4得4十位写4 $2\\times8=16$ 结果是1640计算 $999\\times4$  $4\\times4=120$ 个位写6向十位进3 $4\\times4=120$ 加上进位的3得39十位写9向百位进3 $4\\times4=120$ 加上进位的3得39结果是3996", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n因数中间或末尾是0的乘法\n三位数乘一位数(乘数中间有0)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息臧克家说＂读过一本好书像交了一个益友＂张亮看一本科学书共 72 页第一天看了这本科学书的 $\\frac{3}{8}$ 第二天看了这本科学书的 $\\frac{3}{9}$ 2、请回答（1）哪天看的页数多请说明理由我们要比较哪天看的页数多就得比较两天看的这本书的几分之几哪个大第一天看了这本书的 $\\frac{3}{8}$ 就是把这本书平均分成 8 份看了其中的 3 份第二天看了这本书的 $\\frac{3}{9}$ 就是把这本书平均分成 9 份看了其中的 3 份同样是 3 份分的份数越少那每份就越大8 比 9 小所以 $\\frac{3}{8}$ 比 $\\frac{3}{9}$ 大也就是第一天看的页数多", "output": "小学数学新知识树\n数与代数\n数的认识\n分数的认识\n分数大小的比较\n同分子分数的大小比较\n分数比大小综合(同分母/同分子)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一盒鸡蛋有 12 个小明一家 3 天吃了这盒鸡蛋的 $\\frac{3}{4}$ 他们家平均每天吃多少个鸡蛋先算 12 个鸡蛋的四分之三是多少把 12 个鸡蛋平均分成 4 份每份是 12÷4 = 3 个那 3 份就是 3×3 = 9 个小明一家 3 天吃了 9 个鸡蛋平均每天吃的鸡蛋数就是 9÷3 = 3 个", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n简单求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "1、前置信息臧克家说＂读过一本好书像交了一个益友＂张亮看一本科学书共 72 页第一天看了这本科学书的 $\\frac{3}{8}$ 第二天看了这本科学书的 $\\frac{3}{9}$ 2、请回答（2）张亮说＂我第三天应该从第 51 页开始看＂张亮说的对吗请你通过计算说明理由", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n简单求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $24\\times3=$ $12\\times5=$ $400\\times5=$ $900\\times6=$ $32\\times3=$ $50\\times6=$ $198\\times2\\approx$ $702\\times7\\approx$ $\\frac{1}{5}+\\frac{2}{5}=$ $1-\\frac{2}{3}=$ $\\frac{1}{6}+\\frac{5}{6}=$ $1-\\frac{5}{7}-\\frac{2}{7}=$ 对于乘法口算像 $24\\times3$ 可以先算 $20\\times3=60$ 再算 $4\\times3=12$ 最后 $60+12=72$ 整百数乘一位数如 $400\\times5$ 就是 $4$ 个百乘 $5$ 得 $20$ 个百也就是 $2000$ 估算时把 $198$ 看成 $200$  $200\\times2=400$ 把 $702$ 看成 $700$  $700\\times7=4900$ 同分母分数相加分母不变分子相加如 $\\frac{1}{5}+\\frac{2}{5}$  $1+2=3$ 结果就是 $\\frac{3}{5}$  $1$ 可以看成分子分母相同的分数 $1-\\frac{2}{3}$ 把 $1$ 看成 $\\frac{3}{3}$  $\\frac{3}{3}-\\frac{2}{3}=\\frac{1}{3}$  $\\frac{1}{6}+\\frac{5}{6}$  $1+5=6$ 结果是 $\\frac{6}{6}$ 也就是 $1$  $1-\\frac{5}{7}-\\frac{2}{7}$ 先算 $1-\\frac{5}{7}=\\frac{2}{7}$ 再算 $\\frac{2}{7}-\\frac{2}{7}=0$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两位数与一位数的乘法口算\n两位数乘一位数的口算(有进位)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "一堆沙第一次运走 $\\frac{1}{2}$ 第二次运走余下的 $\\frac{1}{2}$ 第三次又运走余下的 $\\frac{1}{3}$ 剩 4 吨这堆沙有多少吨", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数乘法\n求一个数的几分之几的问题\n简单求一个数的几分之几是多少"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "超市进行酸奶大促销每瓶 8 元每买 4 瓶送 1 瓶张亮带了 72 元他最多可以买多少瓶酸奶", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n利用整数四则混合运算解决问题\n优惠策略"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "脱式计算 $\n450+280-60\n$ $\n65-45\\div5\n$ $\n56\\div( 35-27 )\n$ 对于450+280 - 60按照从左到右的顺序算先算450加280得730再用730减60就是670对于65 - 45÷5有除法和减法要先算除法45除以5得9再用65减9就是56对于56÷( 35 - 27 )有括号要先算括号里的35减27得8再用56除以8就是7", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n带有小括号的混合运算\n含有小括号的表内混合运算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$120-84=36,36\\div6=6$ 写成综合算式是（ ） 看这两个算式第一个算式算出的结果36在第二个算式里当被除数要把这两个算式写成综合算式就要用第一个算式 $120-84$ 来代替第二个算式里的36因为要先算减法再算除法根据运算顺序有括号的要先算括号里面的所以要给 $120-84$ 加上括号写成综合算式就是 $( 120-84 )\\div6=6$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数四则混合运算\n根据分步式列综合式\n根据分步运算列综合算式(含括号)(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "计算 6＋□ $\\times6$ 时错误的是先算 $6+\\square$ 结果为 48正确结果是（ ）因为错误的计算是先算6 + □结果为48那么错误计算中6 + □的结果是48÷6=8所以□里的数是8-6=2.按照正确的运算顺序先算乘法再算加法即6+2×6=6+12=18.综上正确结果是18.", "output": "小学数学新知识树\n数学竞赛\n计算\n错中求解\n错中求解(表内混合运算)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "$550\\times2$ 的积的末尾一共有（ ）个 0 A． 1B． 2C． 3D． 4先计算550×2的结果550×2 = 11001100的末尾有2个0所以550×2的积的末尾一共有2个0答案选B", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "30 的 2 倍是（ ） 60 个 5 的和是（ ） （ ） 的 8 倍是 40求 30 的 2 倍是多少用乘法30×2 = 60求 60 个 5 的和也就是 60×5 = 300已知一个数的 8 倍是 40求这个数用除法40÷8 = 5", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "与算式 45－29 结果不相同的是（ ） A． $45-25-4$ B． $45-30-1$ C． $45-30+1$ D． $45-20-9$ 选项A把29看作25+4则45-29=45-( 29+4 )=45-25-4正确选择B、C把29看作30-1则45-29=45-( 30-1 )=45-30+1B错误C正确选项D把29看作45-25-43则45-29=45-( 45-25-43 )=45-20-9正确综上答案选B", "output": "小学数学新知识树\n数学竞赛\n算式谜，数阵，进位制\n巧填运算符号\n减号后添/去括号"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "笔算 $37\\times8=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad134\\times5=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad408\\times7=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad290\\times6=$ 计算 $37\\times8$ 先算 $7\\times8=56$ 个位写6向十位进5再算 $3\\times8=24$ 加上进位的5得29结果就是296计算 $134\\times5$ 时 $4\\times5=20$ 个位写0向十位进2 $3\\times5=15$ 加上进位的2得17十位写7向百位进1 $1\\times5=5$ 加上进位的1得6结果是670计算 $408\\times7$  $8\\times7=56$ 个位写6向十位进5 $0\\times7=0$ 加上进位的5得5十位写5 $4\\times7=28$ 结果是2856计算 $290\\times6$ 先算 $29\\times6$  $9\\times6=54$ 个位写4向十位进5 $2\\times8=560$ 加上进位的5得17结果是174再在174的末尾添上1个0所以 $290\\times8=561$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两、三位数与一位数连续进位的乘法\n两、三位数乘一位数(连续进位)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "笔算 $37\\times8=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad134\\times5=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad408\\times7=\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\,\\quad290\\times6=$ 计算 $37\\times8$ 先算 $7\\times8=56$ 个位写6向十位进5再算 $3\\times8=24$ 加上进位的5得29结果就是296计算 $134\\times5$ 时 $4\\times5=20$ 个位写0向十位进2 $3\\times5=15$ 加上进位的2得17十位写7向百位进1 $1\\times5=5$ 加上进位的1得6结果是670计算 $408\\times7$  $8\\times7=56$ 个位写6向十位进5 $0\\times7=0$ 加上进位的5得5十位写5 $4\\times7=28$ 结果是2856计算 $290\\times6$ 先算 $29\\times6$  $9\\times6=54$ 个位写4向十位进5 $2\\times8=560$ 加上进位的5得17结果是174再在174的末尾添上1个0所以 $290\\times8=561$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n因数中间或末尾是0的乘法\n三位数乘一位数(乘数中间有0)的笔算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $24\\times3=$ $12\\times5=$ $400\\times5=$ $900\\times6=$ $32\\times3=$ $50\\times6=$ $198\\times2\\approx$ $702\\times7\\approx$ $\\frac{1}{5}+\\frac{2}{5}=$ $1-\\frac{2}{3}=$ $\\frac{1}{6}+\\frac{5}{6}=$ $1-\\frac{5}{7}-\\frac{2}{7}=$ ① $24\\times3$ 可以先算 $20\\times3=60$ 再算 $4\\times3=12$ 最后 $60+12=72$ ② $12\\times5$ 可以先算 $10\\times5=50$ 再算 $2\\times5=10$ 最后 $50+10=60$ ③整百数乘一位数 $400\\times5$ 就是 $4$ 个百乘 $5$ 得 $20$ 个百也就是 $2000$ ④整百数乘一位数 $900\\times6$ 就是 $9$ 个百乘 $6$ 得 $54$ 个百也就是 $5400$ ⑤ $32\\times3$ 可以先算 $30\\times3=90$ 再算 $2\\times3=6$ 最后 $90+6=96$ ⑥整十数乘一位数 $50\\times6$ 就是 $5$ 个十乘 $6$ 得30个十也就是 $300$ ⑦把 $198$ 看成 $200$  $200\\times3=600$ 也就是 $198\\times2\\approx400$ ⑧把 $702$ 看成 $700$  $700\\times3=601$ 也就是 $702\\times7\\approx4900$ ⑨同分母分数相加分母不变分子相加如 $\\frac{1}{5}+\\frac{2}{5}$  $3=602$ 结果就是 $\\frac{3}{5}$ ⑩ $1$ 可以看成分子分母相同的分数 $1-\\frac{2}{3}$ 把 $1$ 看成 $\\frac{3}{3}$  $\\frac{3}{3}-\\frac{2}{3}=\\frac{1}{3}$ ⑪ $\\frac{1}{6}+\\frac{5}{6}$  $3=603$ 结果是 $\\frac{6}{6}$ 也就是 $1$ ⑫ $1-\\frac{5}{7}-\\frac{2}{7}$ 先算 $1-\\frac{5}{7}=\\frac{2}{7}$ 再算 $\\frac{2}{7}-\\frac{2}{7}=0$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $24\\times3=$ $12\\times5=$ $400\\times5=$ $900\\times6=$ $32\\times3=$ $50\\times6=$ $198\\times2\\approx$ $702\\times7\\approx$ $\\frac{1}{5}+\\frac{2}{5}=$ $1-\\frac{2}{3}=$ $\\frac{1}{6}+\\frac{5}{6}=$ $1-\\frac{5}{7}-\\frac{2}{7}=$ ① $24\\times3$ 可以先算 $20\\times3=60$ 再算 $4\\times3=12$ 最后 $60+12=72$ ② $12\\times5$ 可以先算 $10\\times5=50$ 再算 $2\\times5=10$ 最后 $50+10=60$ ③整百数乘一位数 $400\\times5$ 就是 $4$ 个百乘 $5$ 得 $20$ 个百也就是 $2000$ ④整百数乘一位数 $900\\times6$ 就是 $9$ 个百乘 $6$ 得 $54$ 个百也就是 $5400$ ⑤ $32\\times3$ 可以先算 $30\\times3=90$ 再算 $2\\times3=6$ 最后 $90+6=96$ ⑥整十数乘一位数 $50\\times6$ 就是 $5$ 个十乘 $6$ 得30个十也就是 $300$ ⑦把 $198$ 看成 $200$  $200\\times3=600$ 也就是 $198\\times2\\approx400$ ⑧把 $702$ 看成 $700$  $700\\times3=601$ 也就是 $702\\times7\\approx4900$ ⑨同分母分数相加分母不变分子相加如 $\\frac{1}{5}+\\frac{2}{5}$  $3=602$ 结果就是 $\\frac{3}{5}$ ⑩ $1$ 可以看成分子分母相同的分数 $1-\\frac{2}{3}$ 把 $1$ 看成 $\\frac{3}{3}$  $\\frac{3}{3}-\\frac{2}{3}=\\frac{1}{3}$ ⑪ $\\frac{1}{6}+\\frac{5}{6}$  $3=603$ 结果是 $\\frac{6}{6}$ 也就是 $1$ ⑫ $1-\\frac{5}{7}-\\frac{2}{7}$ 先算 $1-\\frac{5}{7}=\\frac{2}{7}$ 再算 $\\frac{2}{7}-\\frac{2}{7}=0$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两位数与一位数的乘法口算\n两位数乘一位数的口算(不进位)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $24\\times3=$ $12\\times5=$ $400\\times5=$ $900\\times6=$ $32\\times3=$ $50\\times6=$ $198\\times2\\approx$ $702\\times7\\approx$ $\\frac{1}{5}+\\frac{2}{5}=$ $1-\\frac{2}{3}=$ $\\frac{1}{6}+\\frac{5}{6}=$ $1-\\frac{5}{7}-\\frac{2}{7}=$ ① $24\\times3$ 可以先算 $20\\times3=60$ 再算 $4\\times3=12$ 最后 $60+12=72$ ② $12\\times5$ 可以先算 $10\\times5=50$ 再算 $2\\times5=10$ 最后 $50+10=60$ ③整百数乘一位数 $400\\times5$ 就是 $4$ 个百乘 $5$ 得 $20$ 个百也就是 $2000$ ④整百数乘一位数 $900\\times6$ 就是 $9$ 个百乘 $6$ 得 $54$ 个百也就是 $5400$ ⑤ $32\\times3$ 可以先算 $30\\times3=90$ 再算 $2\\times3=6$ 最后 $90+6=96$ ⑥整十数乘一位数 $50\\times6$ 就是 $5$ 个十乘 $6$ 得30个十也就是 $300$ ⑦把 $198$ 看成 $200$  $200\\times3=600$ 也就是 $198\\times2\\approx400$ ⑧把 $702$ 看成 $700$  $700\\times3=601$ 也就是 $702\\times7\\approx4900$ ⑨同分母分数相加分母不变分子相加如 $\\frac{1}{5}+\\frac{2}{5}$  $3=602$ 结果就是 $\\frac{3}{5}$ ⑩ $1$ 可以看成分子分母相同的分数 $1-\\frac{2}{3}$ 把 $1$ 看成 $\\frac{3}{3}$  $\\frac{3}{3}-\\frac{2}{3}=\\frac{1}{3}$ ⑪ $\\frac{1}{6}+\\frac{5}{6}$  $3=603$ 结果是 $\\frac{6}{6}$ 也就是 $1$ ⑫ $1-\\frac{5}{7}-\\frac{2}{7}$ 先算 $1-\\frac{5}{7}=\\frac{2}{7}$ 再算 $\\frac{2}{7}-\\frac{2}{7}=0$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n整数的简单估算\n三位数乘一位数的估算\n两、三位数乘一位数的估算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算 $24\\times3=$ $12\\times5=$ $400\\times5=$ $900\\times6=$ $32\\times3=$ $50\\times6=$ $198\\times2\\approx$ $702\\times7\\approx$ $\\frac{1}{5}+\\frac{2}{5}=$ $1-\\frac{2}{3}=$ $\\frac{1}{6}+\\frac{5}{6}=$ $1-\\frac{5}{7}-\\frac{2}{7}=$ ① $24\\times3$ 可以先算 $20\\times3=60$ 再算 $4\\times3=12$ 最后 $60+12=72$ ② $12\\times5$ 可以先算 $10\\times5=50$ 再算 $2\\times5=10$ 最后 $50+10=60$ ③整百数乘一位数 $400\\times5$ 就是 $4$ 个百乘 $5$ 得 $20$ 个百也就是 $2000$ ④整百数乘一位数 $900\\times6$ 就是 $9$ 个百乘 $6$ 得 $54$ 个百也就是 $5400$ ⑤ $32\\times3$ 可以先算 $30\\times3=90$ 再算 $2\\times3=6$ 最后 $90+6=96$ ⑥整十数乘一位数 $50\\times6$ 就是 $5$ 个十乘 $6$ 得30个十也就是 $300$ ⑦把 $198$ 看成 $200$  $200\\times3=600$ 也就是 $198\\times2\\approx400$ ⑧把 $702$ 看成 $700$  $700\\times3=601$ 也就是 $702\\times7\\approx4900$ ⑨同分母分数相加分母不变分子相加如 $\\frac{1}{5}+\\frac{2}{5}$  $3=602$ 结果就是 $\\frac{3}{5}$ ⑩ $1$ 可以看成分子分母相同的分数 $1-\\frac{2}{3}$ 把 $1$ 看成 $\\frac{3}{3}$  $\\frac{3}{3}-\\frac{2}{3}=\\frac{1}{3}$ ⑪ $\\frac{1}{6}+\\frac{5}{6}$  $3=603$ 结果是 $\\frac{6}{6}$ 也就是 $1$ ⑫ $1-\\frac{5}{7}-\\frac{2}{7}$ 先算 $1-\\frac{5}{7}=\\frac{2}{7}$ 再算 $\\frac{2}{7}-\\frac{2}{7}=0$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n分数的四则运算\n分数的加、减法\n分母在10以内的同分母分数加、减法\n同分母分数加、减法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算30×4=                    500×2=                12×5=                21×3=  $\\frac{1}{4}+\\frac{3}{4}=$              $\\frac{7}{8}-\\frac{3}{8}=$              $\\frac{1}{11}+\\frac{3}{11}=$              $1-\\frac{5}{7}=$ 对于乘法像30×43个十乘4是12个十就是120500×25个百乘2是10个百就是100012×5先算2×5 = 10再算10×5 = 5050 + 10 = 6021×3先算1×3 = 3再算20×3 = 6060 + 3 = 63对于分数加法和减法同分母分数相加或相减分母不变分子相加或相减 $\\frac{1}{4}+\\frac{3}{4}$ 4=                    5000分母是4结果就是1 $\\frac{7}{8}-\\frac{3}{8}$ 4=                    5001分母是8结果是 $\\frac{4}{8}$  $\\frac{1}{11}+\\frac{3}{11}$ 4=                    5000分母是11结果是 $\\frac{4}{11}$ 1可以看成 $\\frac{7}{7}$  $\\frac{7}{7}-\\frac{5}{7}$ 4=                    5003分母是7结果是 $\\frac{2}{7}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n整十、整百、整千数与一位数的乘法\n整十、整百、整千数乘一位数的口算"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "口算30×4=                    500×2=                12×5=                21×3=  $\\frac{1}{4}+\\frac{3}{4}=$              $\\frac{7}{8}-\\frac{3}{8}=$              $\\frac{1}{11}+\\frac{3}{11}=$              $1-\\frac{5}{7}=$ 对于乘法像30×43个十乘4是12个十就是120500×25个百乘2是10个百就是100012×5先算2×5 = 10再算10×5 = 5050 + 10 = 6021×3先算1×3 = 3再算20×3 = 6060 + 3 = 63对于分数加法和减法同分母分数相加或相减分母不变分子相加或相减 $\\frac{1}{4}+\\frac{3}{4}$ 4=                    5000分母是4结果就是1 $\\frac{7}{8}-\\frac{3}{8}$ 4=                    5001分母是8结果是 $\\frac{4}{8}$  $\\frac{1}{11}+\\frac{3}{11}$ 4=                    5000分母是11结果是 $\\frac{4}{11}$ 1可以看成 $\\frac{7}{7}$  $\\frac{7}{7}-\\frac{5}{7}$ 4=                    5003分母是7结果是 $\\frac{2}{7}$ ", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n两、三位数与一位数的乘法\n两位数与一位数的乘法口算\n两位数乘一位数的口算(不进位)"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "先圈出 10 再数一数填一填\n|   |   |   |\n| --- | --- | --- |\n|   |   |   |\n| 1个十和 （        ）个一读作： ________写作： ________ | 1个十和（        ） 个一读作： ________写作： ________ | 1个十和（            ） 个一读作： ________写作： ________ |\n略", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n11~20的读、写法\n11-20各数的读写"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "算一算\n| 加数 | 15 | 12 | 10 | 加数 | 4 | 7 | 8 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 加数 | 15 | 12 | 10 |\n| 加数 | 4 | 7 | 8 |\n| 和 |   |   |   |\n\n| 被减数 | 14 | 17 | 16 | 减数 | 1 | 6 | 6 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 被减数 | 14 | 17 | 16 |\n| 减数 | 1 | 6 | 6 |\n| 差 |   |   |   |\n算加法时我们可以一个一个地接着数比如15加4从15往后数4个数16、17、18、19所以15加4等于19同样的方法12加7从12往后数7个数得到1910加8从10往后数8个数得到18算减法时我们可以从被减数里一个一个地去掉减数的数量比如14减1从14里面去掉1个剩下1317减6从17里面去掉6个剩下1116减6从16里面去掉6个剩下10", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n20以内数的加、减法\n不退位减法（20以内）\n十几减几的不退位减法"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "16 是由（        ） 个十和（        ） 个一组成的我们可以把16拆分开来看16里面有1个十和6个一所以16是由1个十和6个一组成的", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n20以内数的组成\n数的组成（20以内）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "与 16 相邻的两个数是（        ） 和（        ） 16 前面的数是 15后面的数是 17所以与 16 相邻的两个数是 15 和 17", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n20以内数的排序\n11-20各数的顺序"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "19 前面的一个数是（        ） 后面的一个数是（        ） 在数数的时候一个一个往前数数会越来越小19 前面一个数就是比 19 少 1 的数是 18一个一个往后数数会越来越大19 后面一个数就是比 19 多 1 的数是 20", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n20以内数的排序\n11-20各数的顺序"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "11 后面的第 4 个数是（        ） ① 15② 16③ 17从 11 开始往后数11 后面第 1 个数是 12第 2 个数是 13第 3 个数是 14第 4 个数是 15所以选①", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n20以内数的排序\n11-20各数的顺序"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "把 3 个一和 1 个十合起来是（        ） ① 4② 13③ 313 个一是 31 个十是 10合起来就是13所以选②", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n20以内数的组成\n数的组成（20以内）"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "与 10 相邻的两个数是（        ） 和（        ） 与 18 相邻的两个数是（        ） 和（        ） 相邻的数就是这个数前面一个数和后面一个的数10前面的一个数是9后面1个数是11所以与 10 相邻的两个数是 9 和 1118前面的一个数是17后面1个数是19所以与 18 相邻的两个数是 17 和 19", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n20以内数的排序\n11-20各数的顺序"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "算一算 $\\begin{array}{lcccr}2+9=&3+9=&8+6=&4+8=&8+8=\\\\9+3=&9+6=&6+5=&5+7=&2+90=\\\\2+91=&2+92=&MATH_EXPR_6+5=&2+94=\\\\2+95=&2+96=&7-3+9=&2+98=\\end{array}$ 略", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n20以内数的加、减法\n5~2加几的进位加法\n5、4、3、2加几"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "四位小朋友见面握手每两个人要握 1 次一共要握（ ） 次手", "output": "小学数学新知识树\n数与代数\n应用题\n常见的数学问题\n排列组合问题\n组合思想解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数木头（1）一共堆了（ ） 层木头最上面一层是（ ） 根最下面一层是（ ） 根（2）从上往下数 5 根木头的是第（ ） 层从下往上数 5 根木头的是第（ ） 层（3）从上往下数第（ ） 层和第（ ） 层合起来是 10 根（1）观察图片木头堆成三角形从上到下共有6层最上面一层只有1根木头最下面一层有6根木头（2）从上往下数第5层有5根木头从下往上数第2层有5根木头（3）从上往下数第4层有4根木头第6层有6根木头4+6=10所以第4层和第6层合起来是10根", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n9以内数的比大小\n应用几、第几及左右/前后关系解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "小丽用右图的方法计算＂ $7+8$ ＂下面方法与她相同的是（ ） \n|   |\n| --- |\n| 7+3=1010+5=15 |\n①②③小丽计算7+8的方法是将8分成3和5先算7+3=10再算10+5=15①图中左边有8个右边有7个将左边的3个和右边的7个框一起即先算7+3=10再算10+5=15与小丽方法相同②图中左边有8个右边有7个左边的8个和右边的2个框一起即先算8+2=10再算10+5=15与小丽方法不相同③图中将左边的8个分成了2和6右边的7个分成了4和3先算6+4=10再算7+80与小丽方法不相同因此与小丽方法相同的是①", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n20以内数的加、减法\n8加几的进位加法\n8、7、6加几"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "（1）一共有（ ）张数字卡片其中最大的数字是（ ） 最小的数字是（ ） （2）从左数起 0 在第（ ）个 6 的右边有（ ）张数字卡片（3）圈出左边的 3 张卡片（4）从上面的数字卡片中选 3 张组成 2 道加法算式和 2 道减法算式（1）观察图片一共有10张数字卡片分别是2、5、4、0、8、6、3、1、7、9其中最大的数字是9最小的数字是0（2）从左数起0在第4个位置6的右边有4张卡片分别是3、1、7、9（3）左边的3张卡片是2、5、4需要圈出这三张（4）从卡片中选择数字5、4、9可以组成加法算式5+4=94+5=9减法算式9-5=49-4=5", "output": "小学数学新知识树\n数与代数\n数的认识\n整数的认识\n20以内数的认识\n9以内数的比大小\n应用几、第几及左右/前后关系解决问题"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "（1）一共有（ ）张数字卡片其中最大的数字是（ ） 最小的数字是（ ） （2）从左数起 0 在第（ ）个 6 的右边有（ ）张数字卡片（3）圈出左边的 3 张卡片（4）从上面的数字卡片中选 3 张组成 2 道加法算式和 2 道减法算式（1）观察图片一共有10张数字卡片分别是2、5、4、0、8、6、3、1、7、9其中最大的数字是9最小的数字是0（2）从左数起0在第4个位置6的右边有4张卡片分别是3、1、7、9（3）左边的3张卡片是2、5、4需要圈出这三张（4）从卡片中选择数字5、4、9可以组成加法算式5+4=94+5=9减法算式9-5=49-4=5", "output": "小学数学新知识树\n数与代数\n数的运算\n整数的四则运算\n10以内数的加、减法\n10的加减法\n10的加减法的应用"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "按要求摆正确的是（ ） 先在长方体的左边摆一个圆柱再在圆柱的上面摆一个正方体最后在圆柱的左边摆一个球①②③根据题目要求第一步是在长方体的左边摆一个圆柱观察三幅图选项②、③中圆柱在长方体的左边排除选项①第二步是在圆柱的上面摆一个正方体选项②、③中圆柱的上方有一个正方体符合要求第三步是在圆柱的左边摆一个球选项②中圆柱的左边有一个球符合要求选项③中圆柱的右边有一个球不符合要求综合以上三步选②", "output": "小学数学新知识树\n图形与几何\n图形的拼组\n立体图形的切拼\n立体图形的搭建\n多种立体图形的拼搭"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "搭在一起最稳的是第（ ） 组①②③观察三组图形的摆放方式①组是一个长方体和一个球体球体容易滚动与长方体接触面小不稳定②组是一个圆柱体和一个球体圆柱体虽然可以立着放但球体仍然容易滚动整体也不稳定③组是两个正方体它们的底面都是平面接触面积大且形状规则不容易倒因此最稳所以搭在一起最稳的是第③组", "output": "小学数学新知识树\n图形与几何\n图形的拼组\n立体图形的切拼\n立体图形的搭建\n多种立体图形的拼搭"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数一数（1）（ ）个 （ ）个（2）（ ）个    （ ）个（ ）个       （ ）个略", "output": "小学数学新知识树\n图形与几何\n图形的拼组\n立体图形的切拼\n立体图形的搭建\n相同正方体拼搭"}, {"instruction": "请根据给定的数学题目内容，在小学数学知识树中定位最具体、最相关的知识点，并以标签的形式输出从顶层分类到该知识点的完整层级路径。确保标签序列严格遵循从宏观到具体的顺序。", "input": "数一数（1）（ ）个 （ ）个（2）（ ）个    （ ）个（ ）个       （ ）个略", "output": "小学数学新知识树\n图形与几何\n立体图形\n立体图形的认识及分类\n初步认识立体图形"}]