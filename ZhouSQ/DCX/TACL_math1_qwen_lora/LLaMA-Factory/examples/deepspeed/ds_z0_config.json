{"train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "gradient_accumulation_steps": "auto", "gradient_clipping": "auto", "zero_allow_untested_optimizer": true, "fp16": {"enabled": "auto", "loss_scale": 0, "loss_scale_window": 1000, "initial_scale_power": 16, "hysteresis": 2, "min_loss_scale": 1}, "bf16": {"enabled": "auto"}, "zero_optimization": {"stage": 0, "allgather_partitions": true, "allgather_bucket_size": 500000000.0, "overlap_comm": false, "reduce_scatter": true, "reduce_bucket_size": 500000000.0, "contiguous_gradients": true, "round_robin_gradients": true}}