#!/usr/bin/env python3  
# -*- coding: utf-8 -*-
"""
置信度阈值分析脚本
根据不同的置信度阈值计算F1分数，步长为0.05
"""

import json
import numpy as np
from typing import List, Dict, Tuple

import sys
from datetime import datetime
import os
import logging  # 导入logging模块

# 设置日志记录配置
logging.basicConfig(
    filename='/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/result/confidence_threshold_analysis.log', 
    level=logging.INFO, 
    format='%(message)s'  # 格式化输出，仅显示日志内容，不带INFO:root
)

def load_prediction_results(file_path: str) -> List[Dict]:
    """加载预测结果文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def calculate_metrics_at_threshold(data: List[Dict], confidence_threshold: float) -> Dict[str, float]:
    """
    在给定置信度阈值下计算评估指标
    
    Args:
        data: 预测结果数据
        confidence_threshold: 置信度阈值
    
    Returns:
        包含precision, recall, f1的字典
    """
    total_true_positives = 0
    total_false_positives = 0
    total_false_negatives = 0
    
    for item in data:
        true_knowledge_paths = set(item['true_lable_paths'])
        
        # 获取满足置信度阈值的预测结果
        predicted_paths = set()
        for prediction in item['predictions']:
            if prediction['confidence'] >= confidence_threshold:
                predicted_paths.add(prediction['full_path'])
        
        # 计算TP, FP, FN
        true_positives = len(true_knowledge_paths.intersection(predicted_paths))
        false_positives = len(predicted_paths - true_knowledge_paths)
        false_negatives = len(true_knowledge_paths - predicted_paths)
        
        total_true_positives += true_positives
        total_false_positives += false_positives
        total_false_negatives += false_negatives
    
    # 计算precision, recall, f1
    precision = total_true_positives / (total_true_positives + total_false_positives) if (total_true_positives + total_false_positives) > 0 else 0
    recall = total_true_positives / (total_true_positives + total_false_negatives) if (total_true_positives + total_false_negatives) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'true_positives': total_true_positives,
        'false_positives': total_false_positives,
        'false_negatives': total_false_negatives
    }


def analyze_confidence_thresholds(data: List[Dict], step: float = 0.05) -> List[Dict]:
    """
    分析不同置信度阈值下的性能
    
    Args:
        data: 预测结果数据
        step: 置信度阈值步长
    
    Returns:
        包含各阈值下指标的列表
    """
    results = []
    
    # 生成置信度阈值范围：0.0 到 1.0，步长为0.05
    thresholds = np.arange(0.0, 1.05, step)
    
    for threshold in thresholds:
        threshold = round(threshold, 2)  # 避免浮点数精度问题
        metrics = calculate_metrics_at_threshold(data, threshold)
        
        result = {
            'confidence_threshold': threshold,
            'precision': metrics['precision'],
            'recall': metrics['recall'],
            'f1': metrics['f1'],
            'true_positives': metrics['true_positives'],
            'false_positives': metrics['false_positives'],
            'false_negatives': metrics['false_negatives']
        }
        
        results.append(result)
        
        # 使用logging记录每个阈值的分析结果
        # logging.info(f"阈值 {threshold:.2f}: Precision={metrics['precision']:.4f}, "
        #              f"Recall={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
    
    return results


def find_best_threshold(results: List[Dict]) -> Dict:
    """找到F1分数最高的阈值"""
    best_result = max(results, key=lambda x: x['f1'])
    return best_result


def save_results(results: List[Dict], output_file: str):
    """保存结果到文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

# 定义函数来生成唯一的文件名
def get_unique_filename(base_filename):
    counter = 1
    new_filename = base_filename
    while os.path.exists(new_filename):  # 如果文件已存在，则生成新的文件名
        base, ext = os.path.splitext(base_filename)
        new_filename = f"{base}-{counter}{ext}"
        counter += 1
    return new_filename

def main():
    """主函数"""
    input_file = "/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/result/multi_knowledge_prediction_results_old_test.json"
    # 获取唯一文件名
    output_file = get_unique_filename("/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/result/confidence_threshold_analysis_results_old_test.json")

    # 写入日志的开始
    logging.info(f"--- Log Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---")
    
    data = load_prediction_results(input_file)
    logging.info(f"加载了 {len(data)} 个问题的预测结果")
    
    results = analyze_confidence_thresholds(data, step=0.01)
    
    best_result = find_best_threshold(results)
    logging.info(f"\n最佳置信度阈值: {best_result['confidence_threshold']:.2f}")
    logging.info(f"最佳F1分数: {best_result['f1']:.4f}")
    logging.info(f"对应的Precision: {best_result['precision']:.4f}")
    logging.info(f"对应的Recall: {best_result['recall']:.4f}")
    
    logging.info(f"\n保存结果到 {output_file}...")
    save_results(results, output_file)
    
    # 生成简要统计报告
    logging.info("\n=== 置信度阈值分析报告 ===")
    logging.info(f"分析的阈值范围: 0.00 - 1.00 (步长: 0.05)")
    logging.info(f"总共分析了 {len(results)} 个阈值点")
    logging.info(f"最佳F1分数: {best_result['f1']:.4f} (阈值: {best_result['confidence_threshold']:.2f})")
    
    # 显示前5个最佳阈值
    sorted_results = sorted(results, key=lambda x: x['f1'], reverse=True)
    logging.info("\n前5个最佳F1分数的阈值:")
    for i, result in enumerate(sorted_results[:5]):
        logging.info(f"{i+1}. 阈值 {result['confidence_threshold']:.2f}: F1={result['f1']:.4f}, "
                     f"Precision={result['precision']:.4f}, Recall={result['recall']:.4f}")
    
    # 写入日志的结束
    logging.info(f"--- Log End: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---")

# 打开日志文件，使用 'a' 模式追加日志
log_file = open('/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/result/confidence_threshold_analysis.log', 'a', encoding='utf-8')

# 设置标准输出和错误输出到日志文件
sys.stdout = log_file
sys.stderr = log_file

if __name__ == "__main__":
    main()
