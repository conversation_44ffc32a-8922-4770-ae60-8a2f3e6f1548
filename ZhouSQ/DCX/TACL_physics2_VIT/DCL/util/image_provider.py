import os
import io
import re
import json
import hashlib
from typing import List, Optional

import torch
from PIL import Image
import requests
from torchvision import transforms
try:
    from transformers import AutoImageProcessor
except Exception:
    AutoImageProcessor = None


class HTMLImageExtractor:
    """
    Extracts <img src="..."> URLs from an HTML-like text.
    """
    IMG_RE = re.compile(r"<img[^>]*src=\"([^\"]+)\"", re.IGNORECASE)

    @staticmethod
    def extract_first_url(text: str) -> Optional[str]:
        if not text:
            return None
        m = HTMLImageExtractor.IMG_RE.search(text)
        if m:
            return m.group(1)
        return None

    @staticmethod
    def extract_all_urls(text: str) -> list[Optional[str]]:
        if not text:
            return []
        return HTMLImageExtractor.IMG_RE.findall(text)


# 默认 torchvision transforms（如未安装 transformers 或处理器不可用时兜底）
essential_transforms = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
])


class ImageCacheProvider:
    """
    Map batch guid tensor -> image batch tensor by reading the original JSONs and extracting <img src>.
    Caches downloaded images under dataset/WebOfScience/Images/.

    Usage:
        provider = ImageCacheProvider(dataset_root="dataset/WebOfScience", split_files=["wos_train.json","wos_val.json","wos_test.json"])
        provider.set_split("train")
        imgs = provider(guid_tensor)  # returns torch.FloatTensor [B,3,224,224] or None
    """
    def __init__(self,
                 dataset_root: str,
                 split_files: List[str],
                 image_cache_dir: Optional[str] = None,
                 timeout: int = 10,
                 transform=essential_transforms,
                 user_agent: str = "Mozilla/5.0"):
        self.dataset_root = dataset_root
        # If a HuggingFace image processor is available, initialize for ViT normalization
        self.hf_processor = None
        if AutoImageProcessor is not None:
            try:
                # 使用与 hierVerb 中相同的 vit 模型名，保持规范一致
                vit_name = "/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/VIT_model/vit-base-patch16-224-in21k"
                self.hf_processor = AutoImageProcessor.from_pretrained(vit_name)
            except Exception:
                self.hf_processor = None
        self.split_files = [os.path.join(dataset_root, f) for f in split_files]
        self.transform = transform
        self.timeout = timeout
        self.headers = {"User-Agent": user_agent}
        self.image_cache_dir = image_cache_dir or os.path.join(dataset_root, "Images")
        os.makedirs(self.image_cache_dir, exist_ok=True)
        # 预计算特征缓存目录
        self.feature_cache_dir = os.path.join(dataset_root, "ImageFeats")
        os.makedirs(self.feature_cache_dir, exist_ok=True)

        # load per-split lists to map guid->doc_token
        self.split_names = [
            os.path.splitext(os.path.basename(f))[0].split('_')[-1] if isinstance(f, str) else str(i)
            for i, f in enumerate(split_files)
        ]
        self.samples_by_split = {}
        for name, f in zip(self.split_names, self.split_files):
            if not os.path.exists(f):
                self.samples_by_split[name] = []
                continue
            with open(f, "r", encoding="utf-8") as fp:
                data = json.load(fp)
                self.samples_by_split[name] = [d.get("doc_token", "") for d in data]

        self.current_split = self.split_names[0] if self.split_names else "train"

    def set_split(self, split: str):
        if split not in self.samples_by_split:
            # attempt to map aliases
            if split in ("train", "val", "test"):
                self.current_split = split
            else:
                # fallback to first
                self.current_split = list(self.samples_by_split.keys())[0]
        else:
            self.current_split = split

    def _get_samples(self):
        return self.samples_by_split.get(self.current_split, [])

    def _url_to_cache_path(self, url: str) -> str:
        h = hashlib.md5(url.encode("utf-8")).hexdigest()
        ext = os.path.splitext(url)[1]
        if len(ext) > 10 or not ext:
            ext = ".jpg"
        return os.path.join(self.image_cache_dir, f"{h}{ext}")

    def _download(self, url: str) -> Optional[Image.Image]:
        try:
            r = requests.get(url, headers=self.headers, timeout=self.timeout)
            r.raise_for_status()
            img = Image.open(io.BytesIO(r.content)).convert("RGB")
            return img
        except Exception:
            return None

    def _load_cached(self, path: str) -> Optional[Image.Image]:
        try:
            if os.path.exists(path):
                return Image.open(path).convert("RGB")
        except Exception:
            return None
        return None

    def _save_cached(self, img: Image.Image, path: str):
        try:
            img.save(path)
        except Exception:
            pass

    def __call__(self, guid_tensor: torch.Tensor) -> Optional[List[torch.FloatTensor]]:
        """
        返回 List[torch.FloatTensor]，每个元素对应一个样本的所有图片张量列表
        - 若样本无图，返回空列表 []
        - 若样本有N张图，返回长度为N的列表，每个元素为 [3, 224, 224] 的图片张量
        - 若某张图下载失败，该位置为 None（保持位置对应关系）
        """
        if guid_tensor is None:
            return None
        guid_list = guid_tensor.detach().cpu().tolist()
        batch_images = []
        has_any = False
        samples = self._get_samples()

        # 调试信息
        debug_info = {
            'total_samples': len(guid_list),
            'samples_with_doc': 0,
            'samples_with_urls': 0,
            'total_urls': 0,
            'successful_loads': 0
        }

        for gid in guid_list:
            doc = samples[gid] if 0 <= gid < len(samples) else None
            if doc:
                debug_info['samples_with_doc'] += 1

            urls = HTMLImageExtractor.extract_all_urls(doc) if doc else []
            if urls:
                debug_info['samples_with_urls'] += 1
                debug_info['total_urls'] += len(urls)

            sample_images = []

            # 对该样本的每张图片按顺序处理
            for url in urls:
                cache_path = self._url_to_cache_path(url)
                img = self._load_cached(cache_path)
                if img is None:
                    img = self._download(url)
                    if img is not None:
                        self._save_cached(img, cache_path)

                if img is not None:
                    if self.hf_processor is not None:
                        # 使用 HF 处理器得到 pixel_values: [1,3,224,224]，取 0
                        pixel = self.hf_processor(images=img, return_tensors="pt").pixel_values[0]
                    else:
                        pixel = self.transform(img)
                    # 为特征缓存提供稳定键（基于URL的md5）
                    # 统一返回 (pixel, cache_key) 形式，训练侧兼容处理
                    cache_key = hashlib.md5(url.encode("utf-8")).hexdigest()
                    sample_images.append((pixel, cache_key))
                    has_any = True
                    debug_info['successful_loads'] += 1
                else:
                    # 保持位置对应，失败的图片用 None 占位
                    sample_images.append(None)

            batch_images.append(sample_images)

        # 打印调试信息（仅第一次）
        if not hasattr(self, '_debug_stats_printed'):
            self._debug_stats_printed = True
            print(f"[IMAGE_DEBUG] Batch stats: {debug_info}")
            print(f"[IMAGE_DEBUG] Current split: {self.current_split}")
            print(f"[IMAGE_DEBUG] Samples length: {len(samples)}")
            print(f"[IMAGE_DEBUG] First 5 guids: {guid_list[:5]}")
            print(f"[IMAGE_DEBUG] Max guid: {max(guid_list) if guid_list else 'None'}")
            if debug_info['samples_with_doc'] > 0:
                sample_doc = samples[guid_list[0]] if guid_list else None
                print(f"[IMAGE_DEBUG] First sample doc preview: {sample_doc[:200] if sample_doc else 'None'}...")
            else:
                # 调试：为什么没有找到 doc
                if guid_list:
                    first_guid = guid_list[0]
                    print(f"[IMAGE_DEBUG] Trying to access samples[{first_guid}], samples length: {len(samples)}")
                    if first_guid < len(samples):
                        sample_doc = samples[first_guid]
                        print(f"[IMAGE_DEBUG] Found doc at index {first_guid}: {sample_doc[:100] if sample_doc else 'Empty'}...")

        return batch_images if has_any else None

