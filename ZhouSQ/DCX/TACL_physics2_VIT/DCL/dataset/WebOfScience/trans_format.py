# -*- coding:utf-8 -*-
import json
import os
from tqdm import tqdm
import torch

# root_path = "dataset/WebOfScience/"
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))

root_path = os.path.join(project_root, "dataset", "WebOfScience")

def to_serializable(obj):
    if isinstance(obj, torch.Tensor):
        return obj.tolist()
    if isinstance(obj, set):
        return list(obj)
    if isinstance(obj, dict):
        return {k: to_serializable(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [to_serializable(i) for i in obj]
    return obj


def get_mapping1(flag=False):
    # flag 各级标签是否平移至从0开始index， True即平移
    # slot2value = torch.load(os.path.join(root_path, 'slot.pt'))
    # slot2value = torch.load(os.path.join(root_path, 'slot.pt'), weights_only=False)

    slot2value1 = torch.load(os.path.join(root_path, 'level0_to_level1.pt'), weights_only=False)
    slot2value2 = torch.load(os.path.join(root_path, 'level1_to_level2.pt'), weights_only=False)
    slot2value3 = torch.load(os.path.join(root_path, 'level2_to_level3.pt'), weights_only=False)
    slot2value4 = torch.load(os.path.join(root_path, 'level3_to_level4.pt'), weights_only=False)
    slot2value5 = torch.load(os.path.join(root_path, 'level4_to_level5.pt'), weights_only=False)
    slot2value6 = torch.load(os.path.join(root_path, 'level5_to_level6.pt'), weights_only=False)
    # slot2value7 = torch.load(os.path.join(root_path, 'level6_to_level7.pt'), weights_only=False)
    # slot2value8 = torch.load(os.path.join(root_path, 'level7_to_level8.pt'), weights_only=False)
    # slot2value9 = torch.load(os.path.join(root_path, 'level8_to_level9.pt'), weights_only=False)
    # slot2value10 = torch.load(os.path.join(root_path, 'level9_to_level10.pt'), weights_only=False)

    # with open("slot1.json", "w", encoding='utf-8') as f:
    #     json.dump(to_serializable(slot2value1), f, indent=2, ensure_ascii=False)
    # with open("slot2.json", "w", encoding='utf-8') as f:
    #     json.dump(to_serializable(slot2value2), f, indent=2, ensure_ascii=False)
    # with open("slot3.json", "w", encoding='utf-8') as f:
    #     json.dump(to_serializable(slot2value3), f, indent=2, ensure_ascii=False)
    # with open("slot4.json", "w", encoding='utf-8') as f:
    #     json.dump(to_serializable(slot2value4), f, indent=2, ensure_ascii=False)

        
    with open(os.path.join(root_path, "formatted_data", "label0.txt"), encoding='utf-8') as fp:
        label0_list = [line.strip() for line in list(fp)]
    with open(os.path.join(root_path, "formatted_data", "label1.txt"), encoding='utf-8') as fp:
        label1_list = [line.strip() for line in list(fp)]
    with open(os.path.join(root_path, "formatted_data", "label2.txt"), encoding='utf-8') as fp:
        label2_list = [line.strip() for line in list(fp)]
    with open(os.path.join(root_path, "formatted_data", "label3.txt"), encoding='utf-8') as fp:
        label3_list = [line.strip() for line in list(fp)]
    with open(os.path.join(root_path, "formatted_data", "label4.txt"), encoding='utf-8') as fp:
        label4_list = [line.strip() for line in list(fp)]
    with open(os.path.join(root_path, "formatted_data", "label5.txt"), encoding='utf-8') as fp:
        label5_list = [line.strip() for line in list(fp)]
    with open(os.path.join(root_path, "formatted_data", "label6.txt"), encoding='utf-8') as fp:
        label6_list = [line.strip() for line in list(fp)]
    # with open(os.path.join(root_path, "formatted_data", "label7.txt"), encoding='utf-8') as fp:
    #     label7_list = [line.strip() for line in list(fp)]
    # with open(os.path.join(root_path, "formatted_data", "label8.txt"), encoding='utf-8') as fp:
    #     label8_list = [line.strip() for line in list(fp)]



    # with open(os.path.join(root_path, "formatted_data", "label_leaf.txt"), encoding='utf-8') as fp:
    #     label_leaf_list = [line.strip() for line in list(fp)]

    label0_label2id = dict({label: idx for idx, label in enumerate(label0_list)})
    label1_label2id = dict({label: idx for idx, label in enumerate(label1_list)})
    # label2_label2id = dict({label: idx for idx, label in enumerate(label2_list)})
    # label3_label2id = dict({label: idx for idx, label in enumerate(label3_list)})
    # label4_label2id = dict({label: idx for idx, label in enumerate(label4_list)})

    label0_to_label1_mapping = dict()
    for k, v in slot2value1.items():
        if flag and k < len(label0_list):
            v = [i-len(label0_list) for i in v]
        label0_to_label1_mapping[k] = list(v)

    label1_to_label2_mapping = dict()
    for k, v in slot2value2.items():
        if flag and k >= len(label0_list):
            v = [i-len(label0_list)-len(label1_list) for i in v]
        # label1_to_label2_mapping[k-len(label0_list)] = list(v)
        label1_to_label2_mapping[k] = list(v)

    label2_to_label3_mapping = dict()
    for k, v in slot2value3.items():
        if flag and k >= (len(label0_list)+len(label1_list)):
            v = [i-len(label0_list)-len(label1_list)-len(label2_list) for i in v]
        # label2_to_label3_mapping[k-len(label0_list)-len(label1_list)] = list(v) 
        label2_to_label3_mapping[k] = list(v)

    label3_to_label4_mapping = dict()
    for k, v in slot2value4.items():
        if flag and k >= (len(label0_list)+len(label1_list)+len(label2_list)):
            v = [i-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list) for i in v]
        # label3_to_label4_mapping[k-len(label0_list)-len(label1_list)-len(label2_list)] = list(v)
        label3_to_label4_mapping[k] = list(v)

    label4_to_label5_mapping = dict()
    for k, v in slot2value5.items():
        if flag and k >= (len(label0_list)+len(label1_list)+len(label2_list)+len(label3_list)):
            v = [i-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list)-len(label4_list) for i in v]
        # label4_to_label5_mapping[k-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list)] = list(v)
        label4_to_label5_mapping[k] = list(v)
    
    label5_to_label6_mapping = dict()
    for k, v in slot2value6.items():
        if flag and k >= (len(label0_list)+len(label1_list)+len(label2_list)+len(label3_list)+len(label4_list)):
            v = [i-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list)-len(label4_list)-len(label5_list) for i in v]
        # label5_to_label6_mapping[k-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list)-len(label4_list)] = list(v)
        label5_to_label6_mapping[k] = list(v)
    
    # label6_to_label7_mapping = dict()
    # for k, v in slot2value7.items():
    #     if flag and k >= (len(label0_list)+len(label1_list)+len(label2_list)+len(label3_list)+len(label4_list)+len(label5_list)):
    #         v = [i-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list)-len(label4_list)-len(label5_list)-len(label6_list) for i in v]
    #     # label6_to_label7_mapping[k-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list)-len(label4_list)-len(label5_list)] = list(v)
    #     label6_to_label7_mapping[k] = list(v)
    # # print(label6_to_label7_mapping[14911])
    
    # label7_to_label8_mapping = dict()
    # for k, v in slot2value8.items():
    #     if flag and k >= (len(label0_list)+len(label1_list)+len(label2_list)+len(label3_list)+len(label4_list)+len(label5_list)+len(label6_list)):
    #         v = [i-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list)-len(label4_list)-len(label5_list)-len(label6_list)-len(label7_list) for i in v]
    #     # label7_to_label8_mapping[k-len(label0_list)-len(label1_list)-len(label2_list)-len(label3_list)-len(label4_list)-len(label5_list)-len(label6_list)] = list(v)
    #     label7_to_label8_mapping[k] = list(v)
    

    label1_to_label0_mapping = {}
    for key, value in label0_to_label1_mapping.items():
        for v in value:
            label1_to_label0_mapping[v] = key

    label2_to_label1_mapping = {}
    for key, value in label1_to_label2_mapping.items():
        for v in value:
            label2_to_label1_mapping[v] = key

    label3_to_label2_mapping = {}
    for key, value in label2_to_label3_mapping.items():
        for v in value:
            label3_to_label2_mapping[v] = key

    label4_to_label3_mapping = {}
    for key, value in label3_to_label4_mapping.items():
        for v in value:
            label4_to_label3_mapping[v] = key

    label5_to_label4_mapping = {}
    for key, value in label4_to_label5_mapping.items():
        for v in value:
            label5_to_label4_mapping[v] = key
    label6_to_label5_mapping = {}
    for key, value in label5_to_label6_mapping.items():
        for v in value:
            label6_to_label5_mapping[v] = key
    # label7_to_label6_mapping = {}
    # for key, value in label6_to_label7_mapping.items():
    #     for v in value:
    #         label7_to_label6_mapping[v] = key
    # label8_to_label7_mapping = {}
    # for key, value in label7_to_label8_mapping.items():
    #     for v in value:
    #         label8_to_label7_mapping[v] = key

    # return label0_list, label1_list, label2_list, label3_list, label4_list, label5_list, label6_list, label7_list, label8_list, \
    #        label0_label2id, label1_label2id, \
    #        label0_to_label1_mapping, label1_to_label0_mapping, \
    #        label1_to_label2_mapping, label2_to_label1_mapping, \
    #        label2_to_label3_mapping, label3_to_label2_mapping, \
    #        label3_to_label4_mapping, label4_to_label3_mapping, \
    #        label4_to_label5_mapping, label5_to_label4_mapping, \
    #        label5_to_label6_mapping, label6_to_label5_mapping, \
    #        label6_to_label7_mapping, label7_to_label6_mapping, \
    #        label7_to_label8_mapping, label8_to_label7_mapping
    return label0_list, label1_list, label2_list, label3_list, label4_list, label5_list, label6_list, \
           label0_label2id, label1_label2id, \
           label0_to_label1_mapping, label1_to_label0_mapping, \
           label1_to_label2_mapping, label2_to_label1_mapping, \
           label2_to_label3_mapping, label3_to_label2_mapping, \
           label3_to_label4_mapping, label4_to_label3_mapping, \
           label4_to_label5_mapping, label5_to_label4_mapping, \
           label5_to_label6_mapping, label6_to_label5_mapping


def get_mapping(flag=True):
    # flag 各级标签是否平移至从0开始index， True即平移
    slot2value = torch.load(os.path.join(root_path, 'slot.pt'))
    with open(os.path.join(root_path, "formatted_data", "label0.txt"), encoding='utf-8') as fp:
        label0_list = [line.strip() for line in list(fp)]
    with open(os.path.join(root_path, "formatted_data", "label1.txt"), encoding='utf-8') as fp:
        label1_list = [line.strip() for line in list(fp)]

    label0_label2id = dict({label: idx for idx, label in enumerate(label0_list)})
    label1_label2id = dict({label: idx for idx, label in enumerate(label1_list)})

    label0_to_label1_mapping = dict()
    for k, v in slot2value.items():
        if flag:
            v = [i-7 for i in v]
        label0_to_label1_mapping[k] = list(v)

    label1_to_label0_mapping = {}
    for key, value in label0_to_label1_mapping.items():
        for v in value:
            label1_to_label0_mapping[v] = key
    return label0_list, label1_list, label0_label2id, label1_label2id, label0_to_label1_mapping, label1_to_label0_mapping

def write_formatted_data(type):
    print(f"write_formatted_data: {type}")
    with open(os.path.join(root_path, f"wos_{type}.json"), 'r', encoding='utf-8') as fp:
        lines = [line.strip() for line in list(fp)]
    datas = []
    for line in lines:
        datas.append(json.loads(line))

    source = []

    for data in tqdm(datas):
        sentence = data['doc_token']
        labels = data['doc_label']
        label_0 = labels[0]
        label_1 = labels[1]
        tmp_str = sentence + "\t" + str(label0_label2id[label_0]) + "\t" + str(label1_label2id[label_1])
        source.append(tmp_str)

    with open(os.path.join(root_path, "formatted_data", f"{type}.txt"), 'w', encoding='utf-8') as fp:
        fp.write("\n".join(source))


if __name__ == "__main__":
    get_mapping(True)
    with open(os.path.join(root_path, "wos_train.json"), 'r', encoding='utf-8') as fp:
        lines = [line.strip() for line in list(fp)]

    datas = []
    for line in lines:
        datas.append(json.loads(line))
    label0_list, label1_list, label0_label2id, label1_label2id, label0_to_label1_mapping, label1_to_label0_mapping = get_mapping()

    if not os.path.exists(os.path.join(root_path, "formatted_data")):
        os.mkdir(os.path.join(root_path, "formatted_data"))

    with open(os.path.join(root_path, "formatted_data", "label0.txt"), 'w', encoding='utf-8') as fp:
        fp.write("\n".join(label0_list))
    with open(os.path.join(root_path, "formatted_data", "label1.txt"), 'w', encoding='utf-8') as fp:
        fp.write("\n".join(label1_list))

    # write_formatted_data("train")
    # write_formatted_data("val")
    # write_formatted_data("test")
