import openprompt
from openprompt import PromptForClassification
from openprompt.prompt_base import Template, Verbalizer
import torch
from typing import List
from transformers.utils.dummy_pt_objects import PreTrainedModel
from tqdm import tqdm
from transformers import BertTokenizer
from util.utils import _mask_tokens
from util.eval import compute_score, compute_based_on_path
from models.loss import constraint_multi_depth_loss_func, flat_contrastive_loss_func,sim
import pickle
import random
import json
from typing import Optional
import math
import os

import torch.nn as nn
import torch.nn.functional as F

class HierVerbPromptForClassification(PromptForClassification):
    def __init__(self,
                 plm: PreTrainedModel,
                 template: Template,
                 verbalizer_list: List[Verbalizer],
                 tokenizer,
                 freeze_plm: bool = False,
                 plm_eval_mode: bool = False,
                 verbalizer_mode=False,
                 args=None,
                 processor=None,
                 logger=None,
                 use_cuda=True,
                 image_provider: Optional[callable] = None,
                 ):
        super().__init__(plm=plm, template=template, verbalizer=verbalizer_list[0], freeze_plm=freeze_plm,
                         plm_eval_mode=plm_eval_mode)
        self.verbalizer_list = verbalizer_list
        self.verbLength = len(self.verbalizer_list)
        self.verbalizer_mode = verbalizer_mode
        self._tokenizer = tokenizer
        self.image_provider = image_provider

        for idx, verbalizer in enumerate(self.verbalizer_list):
            self.__setattr__(f"verbalizer{idx}", verbalizer)
        self.args = args
        self.processor = processor
        self.use_cuda = use_cuda
        self.logger = logger
        self.use_multi_gpu = hasattr(args, 'use_multi_gpu') and args.use_multi_gpu
        # image & fusion config
        self.use_image = hasattr(self.args, 'use_image') and bool(self.args.use_image)
        self.image_model_name = getattr(self.args, 'image_model_name', '/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/VIT_model/vit-base-patch16-224-in21k')
        self.image_freeze = getattr(self.args, 'image_freeze', 1)

        if self.use_image:
            try:
                from transformers import ViTModel, ViTConfig
                self.vit = ViTModel.from_pretrained(self.image_model_name)
                vit_hidden = self.vit.config.hidden_size
            except Exception:
                # 回退：随机初始化一个小ViT（仅用于占位，建议提供本地权重）
                from transformers import ViTConfig, ViTModel
                _cfg = ViTConfig(hidden_size=768)
                self.vit = ViTModel(_cfg)
                vit_hidden = self.vit.config.hidden_size

            if self.image_freeze:
                for p in self.vit.parameters():
                    p.requires_grad = False

            # 将图像CLS投影到文本隐空间
            txt_hidden = self.plm.config.hidden_size if hasattr(self.plm, 'config') else 768
            self.img_proj = nn.Linear(vit_hidden, txt_hidden)

            # 先进的多头交叉注意力机制：文本作为Query，图像作为Key/Value
            self.num_attention_heads = 8
            self.cross_attention = nn.MultiheadAttention(
                embed_dim=txt_hidden,
                num_heads=self.num_attention_heads,
                dropout=0.1,
                batch_first=True
            )

            # 位置编码：为多张图片提供位置信息
            self.max_images = 16  # 支持最多16张图片
            self.img_pos_embedding = nn.Embedding(self.max_images, txt_hidden)

            # 图像数量自适应权重：让模型学会根据图片数量调整融合强度
            self.img_count_proj = nn.Linear(1, txt_hidden)

            # 门控融合与层归一化
            self.fuse_gate = nn.Linear(txt_hidden * 2, txt_hidden)
            self.fuse_proj = nn.Linear(txt_hidden, txt_hidden)
            self.fuse_ln = nn.LayerNorm(txt_hidden)
            self.cross_attn_ln = nn.LayerNorm(txt_hidden)

            # 可学习的占位向量：无图时使用
            self.img_null = nn.Parameter(torch.zeros(txt_hidden))

        # 图文对比学习超参
        self.itc_enabled = getattr(self.args, 'image_text_contrastive', 0) == 1
        self.itc_alpha = getattr(self.args, 'contrastive_alpha', 0.5)
        self.itc_temp = getattr(self.args, 'contrastive_temp', 0.07)

        self.use_precomputed = getattr(self.args, 'image_use_precomputed', 0) == 1
        # 预编码特征目录（与 ImageCacheProvider 一致）
        self.precomputed_dir = os.path.join('DCL', 'dataset', 'WebOfScience', 'ImageFeats')

    def _ultimate_image_semantic_injection(self, outputs_at_mask, batch):
        """
        🎯 最佳方案：图像语义直接注入
        专门解决"如图所示，问你有什么启发？"这类图像依赖型文本分类问题

        核心思想：
        1. 提取图像的高级语义特征
        2. 将图像语义直接融合到每个分类层级的特征中
        3. 让每个层级都能"看懂"图像内容
        """
        try:
            # 获取图像
            batch_images = self.image_provider(batch['guid'])
            if batch_images is None:
                return outputs_at_mask

            device = outputs_at_mask.device
            B, C, H = outputs_at_mask.shape

            # 调试信息
            if not hasattr(self, '_ultimate_debug_once'):
                self._ultimate_debug_once = True
                total_imgs = sum(len([img for img in sample_imgs if img is not None])
                               for sample_imgs in batch_images if sample_imgs)
                samples_with_imgs = len([s for s in batch_images if s])
                print(f"[ULTIMATE] 🎯 Image semantic injection: {total_imgs} images from {samples_with_imgs}/{B} samples")

            # 为每个样本提取图像语义
            enhanced_features = []

            for sample_idx, sample_images in enumerate(batch_images):
                text_features = outputs_at_mask[sample_idx]  # [C, H] - 所有层级的文本特征

                if not sample_images:
                    # 无图样本：保持原始特征
                    enhanced_features.append(text_features)
                    continue

                # 🎯 关键：提取图像的高级语义特征
                img_semantics = self._extract_image_semantics(sample_images, device)

                if img_semantics is not None:
                    # 🎯 核心：将图像语义注入到每个层级
                    # 策略：图像语义作为"上下文增强"，帮助理解"如图所示"

                    # 1. 图像语义扩展到所有层级
                    img_context = img_semantics.unsqueeze(0).expand(C, -1)  # [C, H]

                    # 2. 自适应融合权重：根据文本内容决定图像的重要性
                    # 如果文本中包含"如图"、"图中"等词，图像权重更高
                    fusion_weight = self._compute_adaptive_fusion_weight(text_features, img_semantics)

                    # 3. 语义融合：不是简单相加，而是语义级别的融合
                    enhanced_text = self._semantic_fusion(text_features, img_context, fusion_weight)

                    enhanced_features.append(enhanced_text)
                else:
                    # 图像提取失败，保持原始特征
                    enhanced_features.append(text_features)

            # 返回增强后的特征
            enhanced_outputs = torch.stack(enhanced_features)

            # 成功日志
            if not hasattr(self, '_ultimate_success_once'):
                self._ultimate_success_once = True
                print(f"[ULTIMATE] ✅ Successfully injected image semantics into text features")
                print(f"  Shape: {outputs_at_mask.shape} -> {enhanced_outputs.shape}")

            return enhanced_outputs

        except Exception as e:
            if not hasattr(self, '_ultimate_error_once'):
                self._ultimate_error_once = True
                print(f"[ULTIMATE] ❌ Image semantic injection failed: {e}")
            return outputs_at_mask

    def _extract_image_semantics(self, sample_images, device):
        """提取图像的高级语义特征"""
        try:
            # 确保模块在正确设备上
            self.vit = self.vit.to(device)
            self.img_proj = self.img_proj.to(device)

            img_features = []
            for img_item in sample_images:
                if img_item is None:
                    continue

                # 处理不同格式
                if isinstance(img_item, tuple):
                    pixel, cache_key = img_item
                    # 优先使用预编码特征
                    if self.use_precomputed:
                        feat_path = os.path.join(self.precomputed_dir, f"{cache_key}.pt")
                        if os.path.exists(feat_path):
                            img_feat = torch.load(feat_path, map_location=device)
                            img_features.append(img_feat)
                            continue
                    pixel = pixel
                else:
                    pixel = img_item

                # 在线提取
                pixel = pixel.unsqueeze(0).to(device)
                with torch.no_grad() if self.image_freeze else torch.enable_grad():
                    vit_out = self.vit(pixel_values=pixel)
                    if hasattr(vit_out, 'pooler_output') and vit_out.pooler_output is not None:
                        raw_feat = vit_out.pooler_output[0]
                    else:
                        raw_feat = vit_out.last_hidden_state[0, 0, :]
                    img_feat = self.img_proj(raw_feat)
                    img_features.append(img_feat)

            if img_features:
                # 🎯 关键：多图语义聚合 - 不是简单平均，而是语义级聚合
                img_stack = torch.stack(img_features)  # [N, H]
                # 使用注意力机制选择最重要的图像特征
                attn_weights = torch.softmax(img_stack @ img_stack.mean(dim=0), dim=0)
                img_semantics = (img_stack * attn_weights.unsqueeze(-1)).sum(dim=0)
                return img_semantics
            else:
                return None

        except Exception as e:
            print(f"[ULTIMATE] Image extraction failed: {e}")
            return None

    def _compute_adaptive_fusion_weight(self, text_features, img_semantics):
        """计算自适应融合权重：根据文本内容决定图像的重要性"""
        # 🎯 核心思想：如果文本信息不足（如"如图所示"），图像权重应该更高

        # 简单版本：固定权重，但可以根据文本-图像相似度调整
        try:
            # 计算文本和图像的语义相似度
            text_agg = text_features.mean(dim=0)  # [H]
            similarity = torch.cosine_similarity(text_agg, img_semantics, dim=0)

            # 相似度越高，说明图像和文本越相关，图像权重应该更高
            base_weight = 0.4  # 基础图像权重
            adaptive_weight = base_weight + 0.3 * torch.sigmoid(similarity)

            return adaptive_weight.item()
        except Exception:
            return 0.4  # 默认权重

    def _semantic_fusion(self, text_features, img_context, fusion_weight):
        """语义级融合：不是简单相加，而是深度语义融合"""
        try:
            # 🎯 核心：使用门控机制进行语义融合
            # 让模型学会何时依赖图像，何时依赖文本

            # 1. 计算融合门控
            concat_features = torch.cat([text_features, img_context], dim=-1)  # [C, 2H]

            # 确保门控层在正确设备上
            if not hasattr(self, 'semantic_gate'):
                self.semantic_gate = nn.Linear(2 * text_features.size(-1), text_features.size(-1)).to(text_features.device)
            else:
                self.semantic_gate = self.semantic_gate.to(text_features.device)

            gate = torch.sigmoid(self.semantic_gate(concat_features))  # [C, H]

            # 2. 门控融合
            enhanced = text_features + gate * fusion_weight * (img_context - text_features)

            return enhanced

        except Exception as e:
            print(f"[ULTIMATE] Semantic fusion failed: {e}")
            # 回退到简单加权融合
            return (1 - fusion_weight) * text_features + fusion_weight * img_context
    def _inject_image_into_text(self, batch):
        """
        🎯 核心方法：将图像信息注入到文本编码中
        针对"图像依赖型文本分类"场景，让模型能"看懂"图片内容
        """
        try:
            # 获取图像信息
            batch_images = self.image_provider(batch['guid'])
            if batch_images is None:
                return batch

            # 调试信息
            if not hasattr(self, '_inject_debug_once'):
                self._inject_debug_once = True
                total_imgs = sum(len([img for img in sample_imgs if img is not None])
                               for sample_imgs in batch_images if sample_imgs)
                samples_with_imgs = len([s for s in batch_images if s])
                print(f"[IMAGE_INJECT] Processing {total_imgs} images from {samples_with_imgs}/{len(batch_images)} samples")

            # 为每个样本提取图像特征
            image_features = []  # [B, H] 每个样本的图像聚合特征

            for sample_idx, sample_images in enumerate(batch_images):
                if not sample_images:
                    # 无图样本：使用零向量
                    image_features.append(torch.zeros(768, device=batch['input_ids'].device))
                    continue

                # 提取该样本的所有图像特征
                sample_feats = []
                for img_item in sample_images:
                    if img_item is None:
                        continue

                    # 兼容 (pixel, cache_key) 格式
                    if isinstance(img_item, tuple):
                        pixel, cache_key = img_item
                        # 优先使用预编码特征
                        if self.use_precomputed:
                            feat_path = os.path.join(self.precomputed_dir, f"{cache_key}.pt")
                            if os.path.exists(feat_path):
                                img_feat = torch.load(feat_path, map_location='cpu').to(batch['input_ids'].device)
                                sample_feats.append(img_feat)
                                continue
                        pixel = pixel
                    else:
                        pixel = img_item

                    # 在线提取特征
                    pixel = pixel.unsqueeze(0).to(batch['input_ids'].device)
                    self.vit = self.vit.to(batch['input_ids'].device)
                    with torch.no_grad() if self.image_freeze else torch.enable_grad():
                        vit_out = self.vit(pixel_values=pixel)
                        if hasattr(vit_out, 'pooler_output') and vit_out.pooler_output is not None:
                            img_feat = vit_out.pooler_output[0]
                        else:
                            img_feat = vit_out.last_hidden_state[0, 0, :]
                        img_feat = self.img_proj(img_feat)
                        sample_feats.append(img_feat)

                # 聚合该样本的多图特征
                if sample_feats:
                    # 多图加权平均（可以改为注意力加权）
                    sample_feat = torch.stack(sample_feats).mean(dim=0)
                else:
                    sample_feat = torch.zeros(768, device=batch['input_ids'].device)

                image_features.append(sample_feat)

            # 将图像特征注入到文本中
            image_features = torch.stack(image_features)  # [B, H]
            batch = self._fuse_image_to_text_tokens(batch, image_features)

        except Exception as e:
            if not hasattr(self, '_inject_error_once'):
                self._inject_error_once = True
                print(f"[WARN] Image injection failed: {e}")

        return batch

    def _fuse_image_to_text_tokens(self, batch, image_features):
        """
        🎯 将图像特征融合到文本 token 中
        策略：在 [CLS] 位置后插入图像 token，让 BERT 能感知图像信息
        """
        # 获取原始 input_ids 和 attention_mask
        input_ids = batch['input_ids']  # [B, L]
        attention_mask = batch['attention_mask']  # [B, L]
        B, L = input_ids.shape

        # 创建图像 token ID（使用 [unused1] 作为图像占位符）
        image_token_id = 1  # [unused1] 的 token id

        # 在每个序列的 [CLS] 后插入图像 token
        new_input_ids = []
        new_attention_mask = []

        for i in range(B):
            # 原序列：[CLS] text [SEP] [PAD]...
            # 新序列：[CLS] [IMG] text [SEP] [PAD]...
            seq = input_ids[i]  # [L]
            mask = attention_mask[i]  # [L]

            # 找到第一个非 [CLS] 位置（通常是位置1）
            insert_pos = 1

            # 插入图像 token
            new_seq = torch.cat([
                seq[:insert_pos],  # [CLS]
                torch.tensor([image_token_id], device=seq.device),  # [IMG]
                seq[insert_pos:-1]  # text [SEP] [PAD]... (去掉最后一个以保持长度)
            ])

            new_mask = torch.cat([
                mask[:insert_pos],  # [CLS]
                torch.tensor([1], device=mask.device),  # [IMG] 的 attention
                mask[insert_pos:-1]  # text [SEP] [PAD]... (去掉最后一个以保持长度)
            ])

            new_input_ids.append(new_seq)
            new_attention_mask.append(new_mask)

        # 更新 batch
        batch['input_ids'] = torch.stack(new_input_ids)
        batch['attention_mask'] = torch.stack(new_attention_mask)

        # 保存图像特征，在 embedding 层会用到
        batch['image_features'] = image_features  # [B, H]
        batch['image_token_id'] = image_token_id

        return batch
    def _forward_with_image_injection(self, batch):
        """
        🎯 带图像注入的前向传播
        如果 batch 中包含图像特征，则在 embedding 层注入图像信息
        """
        if 'image_features' in batch:
            # 保存原始的 forward 方法 - 兼容多GPU模式
            try:
                # 尝试直接访问 embeddings
                embeddings_module = self.prompt_model.plm.embeddings
            except AttributeError:
                # 多GPU模式下，可能需要通过 bert 访问
                try:
                    embeddings_module = self.prompt_model.plm.bert.embeddings
                except AttributeError:
                    # 如果都失败，跳过图像注入
                    print("[WARN] Cannot access embeddings module, skipping image injection")
                    return self.prompt_model(batch)

            original_forward = embeddings_module.forward
            image_features = batch['image_features']  # [B, H]
            image_token_id = batch['image_token_id']

            def embedding_forward_with_image(input_ids, token_type_ids=None, position_ids=None, inputs_embeds=None, past_key_values_length=0):
                # 调用原始 embedding
                embeddings = original_forward(input_ids, token_type_ids, position_ids, inputs_embeds, past_key_values_length)

                # 找到图像 token 位置并替换为图像特征
                B, L, H = embeddings.shape
                for i in range(B):
                    img_positions = (input_ids[i] == image_token_id).nonzero(as_tuple=True)[0]
                    for pos in img_positions:
                        # 将图像特征投影到 embedding 维度
                        img_feat = image_features[i]  # [H]
                        if img_feat.shape[0] != H:
                            # 如果维度不匹配，使用线性投影
                            if not hasattr(self, 'img_to_embed_proj'):
                                self.img_to_embed_proj = nn.Linear(img_feat.shape[0], H).to(img_feat.device)
                            img_feat = self.img_to_embed_proj(img_feat)
                        embeddings[i, pos] = img_feat

                return embeddings

            # 临时替换 embedding 的 forward 方法
            embeddings_module.forward = embedding_forward_with_image

            try:
                outputs = self.prompt_model(batch)
            finally:
                # 恢复原始 forward 方法
                embeddings_module.forward = original_forward

            return outputs
        else:
            # 没有图像特征，正常前向传播
            return self.prompt_model(batch)

    def load_label_sim(self,path):
        label_des = json.load(open(path))
        emb = torch.zeros(len(label_des), 768)
        for l in label_des:
            output = self.tokenizer(l, max_length=512, add_special_tokens=True, truncation=True, padding=True, return_tensors="pt")
            # emb[l] += output.pooled_output
        return torch.cdist(emb, emb)


    def forward(self, batch) -> torch.Tensor:
        r"""
        Get the logits of label words.

        Args:
            batch (:obj:`Union[Dict, InputFeatures]`): The original batch

        Returns:
            :obj:`torch.Tensor`: The logits of the lable words (obtained by the current verbalizer).
        """

        # debug
        loss = 0
        loss_details = [0, 0, 0, 0]
        lm_loss = None
        constraint_loss = None
        contrastive_loss = None
        args = self.args
        if args.use_dropout_sim and self.training:
            if not self.flag_contrastive_logits:
                print("using contrastive_logits")
                self.flag_contrastive_logits = True
            contrastive_batch = dict()
            for k, v in batch.items():
                tmp = []
                for i in v:
                    tmp.append(i)
                    tmp.append(i)
                contrastive_batch[k] = torch.stack(tmp) if isinstance(tmp[0], torch.Tensor) else tmp
                contrastive_batch[k] = contrastive_batch[k].to("cuda:0")
            batch = contrastive_batch

        # 🎯 图像增强的文本编码：在文本编码前注入图像信息
        if self.use_image and self.image_provider is not None and 'guid' in batch:
            batch = self._inject_image_into_text(batch)

        outputs = self._forward_with_image_injection(batch)
        outputs = self.verbalizer_list[0].gather_outputs(outputs)
        # outputs = self.verbalizer1.gather_outputs(outputs)

        if isinstance(outputs, tuple):
            outputs_at_mask = [self.extract_at_mask(output, batch) for output in outputs]
        else:
            outputs_at_mask = self.extract_at_mask(outputs, batch)

        # 🎯 最佳方案：图像语义直接注入 - 专门解决"如图所示"类问题
        if self.use_image and self.image_provider is not None and 'guid' in batch:
            outputs_at_mask = self._ultimate_image_semantic_injection(outputs_at_mask, batch)

        logits = []
        for idx in range(self.verbLength):
            # 确保 verbalizer 在正确的设备上
            verbalizer = self.__getattr__(f"verbalizer{idx}")
            target_device = outputs_at_mask.device if isinstance(outputs_at_mask, torch.Tensor) else batch['input_ids'].device

            # 移动 verbalizer 到目标设备
            try:
                if hasattr(verbalizer, 'to'):
                    verbalizer = verbalizer.to(target_device)
                # 特别处理 SoftVerbalizer 的 head 组件
                if hasattr(verbalizer, 'head') and hasattr(verbalizer.head, 'to'):
                    verbalizer.head = verbalizer.head.to(target_device)
            except Exception as e:
                if not hasattr(self, f'_verbalizer_device_warn_{idx}'):
                    setattr(self, f'_verbalizer_device_warn_{idx}', True)
                    print(f"[WARN] Failed to move verbalizer{idx} to {target_device}: {e}")

            label_words_logtis = verbalizer.process_outputs(outputs_at_mask[:, idx, :], batch=batch)
            # 图文对比学习：InfoNCE（与现有层级 DCL 独立开关，可共存）
            if self.training and getattr(self, 'itc_enabled', False) and len(locals().get('image_embeds', [])) > 0:
                try:
                    img_mat = torch.stack(image_embeds, dim=0)  # [B,H]
                    txt_mat = outputs_at_mask.mean(dim=1)       # [B,H] 跨 mask 聚合
                    # 温度归一化
                    img_mat = F.normalize(img_mat, dim=-1)
                    txt_mat = F.normalize(txt_mat, dim=-1)
                    logits_itc = txt_mat @ img_mat.t() / self.itc_temp  # [B,B]
                    labels = torch.arange(logits_itc.size(0), device=logits_itc.device)
                    itc_loss = F.cross_entropy(logits_itc, labels)
                    # 对称项（可选）：img->txt
                    logits_itc_t = img_mat @ txt_mat.t() / self.itc_temp
                    itc_loss = (itc_loss + F.cross_entropy(logits_itc_t, labels)) * 0.5
                except Exception as _e:
                    itc_loss = None
                    if not hasattr(self, '_itc_err_once'):
                        self._itc_err_once = True
                        print(f"[WARN] ITC compute failed: {_e}")
            else:
                itc_loss = None

            logits.append(label_words_logtis)

        if self.training:

            labels = batch['label']

            hier_labels = []
            hier_labels.insert(0, labels)
            for idx in range(args.depth - 2, -1, -1):
                cur_depth_labels = torch.zeros_like(labels)
                for i in range(len(labels)):
                    # cur_depth_labels[i] = label1_to_label0_mapping[labels[i].tolist()]
                    # hierVerb.py 修改建议
                    # label_index = hier_labels[0][i].tolist()
                    # if label_index in self.processor.hier_mapping[idx][1]:
                    #     cur_depth_labels[i] = self.processor.hier_mapping[idx][1][label_index]
                    # else:
                    #     # 处理无效索引：记录警告/使用默认值
                    #     cur_depth_labels[i] = 0
                    cur_depth_labels[i] = self.processor.hier_mapping[idx][1][hier_labels[0][i].tolist()]
                hier_labels.insert(0, cur_depth_labels)

            ## MLM loss
            if args.lm_training:

                input_ids = batch['input_ids']
                input_ids, labels = _mask_tokens(self.tokenizer, input_ids.cpu())

                lm_inputs = {"input_ids": input_ids, "attention_mask": batch['attention_mask'], "labels": labels}

                for k, v in lm_inputs.items():
                    if v is not None:
                        lm_inputs[k] = v.to(self.device)
                lm_loss = self.plm(**lm_inputs)[0]

            if args.multi_label:
                loss_func = torch.nn.BCEWithLogitsLoss()
            else:
                loss_func = torch.nn.CrossEntropyLoss()

            for idx, cur_depth_label in enumerate(hier_labels):
                cur_depth_logits = logits[idx]
                if args.multi_label:
                    cur_multi_label = torch.zeros_like(cur_depth_logits)

                    for i in range(cur_multi_label.shape[0]):
                        cur_multi_label[i][cur_depth_label[i]] = 1
                    cur_depth_label = cur_multi_label
                loss += loss_func(cur_depth_logits, cur_depth_label)

            loss_details[0] += loss.item()  # 层级二loss

            if args.contrastive_loss:
                if not self.flag_contrastive_loss:
                    print(f"using DCL loss with alpha {args.contrastive_alpha}")
                    if args.use_dropout_sim:
                        print("using use_dropout_sim")
                    self.flag_contrastive_loss = True
                # 从batch中提取input_ids作为文本内容的代理
                text_contents = None
                if 'input_ids' in batch:
                    # 将input_ids转换为字符串用于比较文本相似性
                    text_contents = [str(ids.tolist()) for ids in batch['input_ids']]

                contrastive_loss = flat_contrastive_loss_func(None, hier_labels, self.processor,
                                                                            outputs_at_mask,
                                                                            imbalanced_weight=args.imbalanced_weight,
                                                                            contrastive_level=args.contrastive_level,
                                                                            imbalanced_weight_reverse=args.imbalanced_weight_reverse,
                                                                            depth=args.depth,
                                                                            use_cuda=self.use_cuda,
                                                                            text_contents=text_contents)


            # 叠加图文 InfoNCE（若启用且可计算）
            if itc_loss is not None:
                if args.contrastive_loss:
                    contrastive_loss = (contrastive_loss or 0) + self.itc_alpha * itc_loss
                else:
                    contrastive_loss = self.itc_alpha * itc_loss

            ####
            # cur_batch_size = outputs_at_mask.shape[0]
            # contrastive_loss = 0
            # for idx, cur_depth_label in enumerate(hier_labels):
            #     sim_score = sim(outputs_at_mask[:,idx,:], outputs_at_mask[:,idx,:])
            #     sim_score = torch.exp(sim_score)
            #     cur_hier_matrix = torch.zeros(cur_batch_size, cur_batch_size)
            #     for i in range(len(cur_depth_label)):
            #         for j in range(len(cur_depth_label)):
            #             if cur_depth_label[i] == cur_depth_label[j]:
            #                 cur_hier_matrix[i][j] = 1
            #             else:
            #                 cur_hier_matrix[i][j] = 0

            #     pos_sim = sim_score[cur_hier_matrix != 0].sum()
            #     neg_sim = sim_score[cur_hier_matrix == 0].sum()
            #     contrastive_loss += - torch.log(pos_sim / (pos_sim + neg_sim))

            #####
            if lm_loss is not None:
                if args.lm_alpha != -1:
                    loss = loss * args.lm_alpha + (1 - args.lm_alpha) * lm_loss
                else:
                    loss += lm_loss
                loss_details[1] += lm_loss.item()

            if constraint_loss is not None:
                if args.constraint_alpha != -1:
                    loss = loss * args.constraint_alpha + (1 - args.constraint_alpha) * constraint_loss
                else:
                    loss += constraint_loss
                loss_details[2] += constraint_loss.item()

            if contrastive_loss is not None:
                if args.contrastive_alpha != -1:
                    # loss = loss * contrastive_alpha + (1 - contrastive_alpha) * contrastive_loss
                    loss += (1 - args.contrastive_alpha) * contrastive_loss
                else:
                    loss += contrastive_loss
                loss_details[3] += contrastive_loss.item()

            return logits, loss, loss_details
        else:
            return logits, outputs_at_mask

    def init_embeddings(self):
        self.print_info("using label emb for soft verbalizer")

        label_emb_list = []
        # label_name_list = []
        for idx in range(self.args.depth):

            label_dict = self.processor.label_list[idx]
            label_dict = dict({idx: v for idx, v in enumerate(label_dict)})
            label_dict = {i: self.tokenizer.encode(v) for i, v in label_dict.items()}
            label_emb = []
            # label_name = []
            input_embeds = self.plm.get_input_embeddings()

            for i in range(len(label_dict)):
                label_emb.append(
                    input_embeds.weight.index_select(0, torch.tensor(label_dict[i], device=self.device)).mean(dim=0))
                # label_name.append(label_dict[i])
            label_emb = torch.stack(label_emb)
            label_emb_list.append(label_emb)
            # label_name_list.append(label_name)
        if self.args.use_hier_mean:
            for depth_idx in range(self.args.depth - 2, -1, -1):
                cur_label_emb = label_emb_list[depth_idx]
                # cur_label_name = label_name_list[depth_idx]
                cur_depth_length = len(self.processor.label_list[depth_idx])

                for i in range(cur_depth_length):
                    # 检查映射索引是否有效
                    mapped_indices = self.processor.hier_mapping[depth_idx][0][i]
                    next_level_size = len(label_emb_list[depth_idx + 1])

                    # 确保所有映射索引都在下一层级的范围内
                    if not all(0 <= idx < next_level_size for idx in mapped_indices):
                        self.print_info(f"Warning: Invalid mapping at depth {depth_idx}, index {i} -> {mapped_indices}")
                        continue

                    # 计算有效索引的平均值
                    cur_label_emb[i] += label_emb_list[depth_idx + 1][mapped_indices, :].mean(dim=0)
                label_emb_list[depth_idx] = cur_label_emb
                # label_name_list[depth_idx] = cur_label_name
        for idx in range(self.args.depth):
            label_emb = label_emb_list[idx]
            self.print_info(f"depth {idx}: {label_emb.shape}")
            if "0.1.2" in openprompt.__path__[0]:
                self.__getattr__(f"verbalizer{idx}").head_last_layer.weight.data = label_emb
                self.__getattr__(f"verbalizer{idx}").head_last_layer.weight.data.requires_grad = True
            else:
                getattr(self.__getattr__(f"verbalizer{idx}").head.predictions,
                        'decoder').weight.data = label_emb
                getattr(self.__getattr__(f"verbalizer{idx}").head.predictions,
                        'decoder').weight.data.requires_grad = True

    def evaluate(self, dataloader, processor, desc="Valid", mode=0, device="cuda:0", args=None):
        self.eval()
        pred = []
        truth = []
        pbar = tqdm(dataloader, desc=desc)
        hier_mapping = processor.hier_mapping
        depth = len(hier_mapping) + 1
        all_length = len(processor.all_labels)
        for step, batch in enumerate(pbar):
            if hasattr(batch, 'cuda'):
                batch = batch.cuda()
            else:
                # 多GPU模式下，让模型自动处理数据分配
                if self.use_multi_gpu:
                    batch = tuple(t.cuda() if isinstance(t, torch.Tensor) else t for t in batch)
                else:
                    batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
                batch = {"input_ids": batch[0], "attention_mask": batch[1],
                         "label": batch[2], "loss_ids": batch[3], "guid": batch[4]}
            # 切换 image provider 的 split，便于按 guid 正确取样
            if hasattr(self, 'image_provider') and self.image_provider is not None:
                try:
                    self.image_provider.set_split('val' if desc.lower().startswith('valid') else 'test')
                except Exception:
                    pass
            logits, leaf_embed = self(batch)
            leaf_labels = batch['label']
            hier_labels = []
            hier_labels.insert(0, leaf_labels)
            for idx in range(depth - 2, -1, -1):
                cur_depth_labels = torch.zeros_like(leaf_labels)
                for i in range(len(leaf_labels)):
                    label_index = hier_labels[0][i].tolist()
                    if label_index in self.processor.hier_mapping[idx][1]:
                        cur_depth_labels[i] = self.processor.hier_mapping[idx][1][label_index]
                    else:
                        # 处理无效索引：记录警告/使用默认值
                        cur_depth_labels[i] = 0
                    # cur_depth_labels[i] = hier_mapping[idx][1][hier_labels[0][i].tolist()]
                hier_labels.insert(0, cur_depth_labels)

            if isinstance(logits, list):
                leaf_logits = logits[-1]
            elif isinstance(logits, torch.Tensor):
                leaf_logits = logits[:, -1, :]
            leaf_logits = torch.softmax(leaf_logits, dim=-1)
            batch_preds = []
            batch_golds = []

            leaf_preds = torch.argmax(leaf_logits, dim=-1).cpu().tolist()
            leaf_labels = leaf_labels.cpu().tolist()

            batch_preds.insert(0, leaf_preds)
            batch_golds.insert(0, leaf_labels)

            batch_s = leaf_logits.shape[0]
            flat_slot2value = processor.flat_slot2value
            hier_logits = []
            hier_logits.insert(0, leaf_logits)

            for depth_idx in range(depth - 2, -1, -1):
                ori_logits = torch.softmax(logits[depth_idx], dim=-1)

                if ori_logits.shape[-1] != all_length:
                    cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))
                    for i in range(cur_logits.shape[-1]):
                        # 检查映射索引是否存在且有效
                        if depth_idx not in hier_mapping or \
                           i not in hier_mapping[depth_idx][0]:
                            # self.print_info(f"Warning: Missing mapping at depth {depth_idx}, index {i}")
                            continue

                        mapped_indices = hier_mapping[depth_idx][0][i]
                        # 确保所有索引都在hier_logits的范围内
                        valid_indices = [idx for idx in mapped_indices if idx < hier_logits[0].shape[-1]]
                        if not valid_indices:
                            self.print_info(f"Warning: No valid indices at depth {depth_idx}, index {i}")
                            continue

                        cur_logits[:, i] = torch.mean(hier_logits[0][:, valid_indices], dim=-1)
                else:
                    cur_logits = torch.zeros(batch_s, all_length)
                    cd_labels = processor.depth2label[depth_idx]
                    for i in range(all_length):
                        if i in cd_labels:
                            cur_logits[:, i] = torch.sum(hier_logits[0][:, list(flat_slot2value[i])], dim=-1)

                cur_logits = cur_logits.to(device)

                # for i in range(cur_label_size):
                #     cur_logits[:, i] = torch.sum(hier_logits[0][:, cur_mapping[0][i]], dim=-1)
                if mode == 0:
                    softmax_label_logits = ori_logits
                elif mode == 1:
                    softmax_label_logits = torch.softmax(cur_logits, dim=-1)
                elif mode == 2:
                    softmax_label_logits = torch.softmax(cur_logits, dim=-1) + ori_logits
                    softmax_label_logits = torch.softmax(softmax_label_logits, dim=-1)

                cur_preds = torch.argmax(softmax_label_logits, dim=-1).cpu().tolist()
                cur_golds = hier_labels[depth_idx].cpu().tolist()

                hier_logits.insert(0, softmax_label_logits)
                batch_preds.insert(0, cur_preds)
                batch_golds.insert(0, cur_golds)
            batch_preds = torch.tensor(batch_preds).transpose(1, 0).cpu().tolist()
            batch_golds = torch.tensor(batch_golds).transpose(1, 0).cpu().tolist()

            # for i in range(batch_s):
            #     sub_preds = []
            #     sub_golds = []
            #     prev_label_size = 0
            #     for depth_idx in range(depth):

            #         if depth_idx == 0:
            #             sub_preds.append(batch_preds[i][depth_idx])
            #             sub_golds.append(batch_golds[i][depth_idx])

            #             continue
            #         prev_mapping = hier_mapping[depth_idx - 1]
            #         prev_label_size = len(prev_mapping[0]) + prev_label_size
            #         if leaf_logits.shape[-1] == all_length:
            #             sub_preds.append(batch_preds[i][depth_idx])
            #         else:
            #             sub_preds.append(batch_preds[i][depth_idx] + prev_label_size)
            #         sub_golds.append(batch_golds[i][depth_idx] + prev_label_size)
            #     pred.append(sub_preds)
            #     truth.append(sub_golds)

            for i in range(batch_s):
                # 我们不再需要 prev_label_size 来修改 gold label
                # sub_preds 的逻辑可能也需要重新审视，但为了解决当前报错，我们主要关注 sub_golds
                sub_golds = batch_golds[i] # batch_golds[i] 已经是一个包含所有层级gold label的列表
                sub_preds = batch_preds[i] # 同理，preds也直接使用

                pred.append(sub_preds)
                truth.append(sub_golds)

        label_dict = dict({idx: label for idx, label in enumerate(processor.all_labels)})
        if args is None:
            scores = compute_score(pred, truth, label_dict)
        else:
            scores = compute_based_on_path(pred, truth, label_dict, processor, args)
        return scores

    def print_info(self, info):
        if self.logger is not None:
            self.logger.info(info)
        else:
            print(info)

    def state_dict(self, *args, **kwargs):
        """ Save the model using template, plm and verbalizer's save methods."""
        _state_dict = {}
        if not self.prompt_model.freeze_plm:
            _state_dict['plm'] = self.plm.state_dict(*args, **kwargs)
        _state_dict['template'] = self.template.state_dict(*args, **kwargs)
        for idx in range(self.verbLength):
            _state_dict[f'verbalizer{idx}'] = self.__getattr__(f"verbalizer{idx}").state_dict(*args, **kwargs)
        return _state_dict

    def load_state_dict(self, state_dict, *args, **kwargs):
        """ Load the model using template, plm and verbalizer's load methods."""
        if 'plm' in state_dict and not self.prompt_model.freeze_plm:
            self.plm.load_state_dict(state_dict['plm'], *args, **kwargs)
        self.template.load_state_dict(state_dict['template'], *args, **kwargs)

        for idx in range(self.verbLength):
            self.__getattr__(f"verbalizer{idx}").load_state_dict(state_dict[f'verbalizer{idx}'], *args, **kwargs)

    # def create_dcl_inputs(
    #     self,
    #     samples,
    #     tensorizer,
    #     insert_title: bool,
    #     num_hard_negatives: int = 0,
    #     num_other_negatives: int = 0,
    #     shuffle: bool = True,
    #     shuffle_positives: bool = False,
    #     hard_neg_fallback: bool = True,
    #     query_token: str = None,
    # ):
    #     """
    #     Creates a batch of the biencoder training tuple.
    #     :param samples: list of BiEncoderSample-s to create the batch for
    #     :param tensorizer: components to create model input tensors from a text sequence
    #     :param insert_title: enables title insertion at the beginning of the context sequences
    #     :param num_hard_negatives: amount of hard negatives per question (taken from samples' pools)
    #     :param num_other_negatives: amount of other negatives per question (taken from samples' pools)
    #     :param shuffle: shuffles negative passages pools
    #     :param shuffle_positives: shuffles positive passages pools
    #     :return: BiEncoderBatch tuple
    #     """
    #     question_tensors = []
    #     ctx_tensors = []
    #     positive_ctx_indices = []
    #     hard_neg_ctx_indices = []

    #     for sample in samples:
    #         # ctx+ & [ctx-] composition
    #         # as of now, take the first(gold) ctx+ only

    #         if shuffle and shuffle_positives:
    #             positive_ctxs = sample.positive_passages
    #             positive_ctx = positive_ctxs[np.random.choice(len(positive_ctxs))]
    #         else:
    #             positive_ctx = sample.positive_passages[0]

    #         neg_ctxs = sample.negative_passages
    #         hard_neg_ctxs = sample.hard_negative_passages
    #         question = sample.query
    #         # question = normalize_question(sample.query)

    #         if shuffle:
    #             random.shuffle(neg_ctxs)
    #             random.shuffle(hard_neg_ctxs)

    #         if hard_neg_fallback and len(hard_neg_ctxs) == 0:
    #             hard_neg_ctxs = neg_ctxs[0:num_hard_negatives]

    #         neg_ctxs = neg_ctxs[0:num_other_negatives]
    #         hard_neg_ctxs = hard_neg_ctxs[0:num_hard_negatives]

    #         all_ctxs = [positive_ctx] + neg_ctxs + hard_neg_ctxs
    #         hard_negatives_start_idx = 1
    #         hard_negatives_end_idx = 1 + len(hard_neg_ctxs)

    #         current_ctxs_len = len(ctx_tensors)

    #         sample_ctxs_tensors = [
    #             tensorizer.text_to_tensor(
    #                 ctx.text, title=ctx.title if (insert_title and ctx.title) else None
    #             )
    #             for ctx in all_ctxs
    #         ]

    #         ctx_tensors.extend(sample_ctxs_tensors)
    #         positive_ctx_indices.append(current_ctxs_len)
    #         hard_neg_ctx_indices.append(
    #             [
    #                 i
    #                 for i in range(
    #                     current_ctxs_len + hard_negatives_start_idx,
    #                     current_ctxs_len + hard_negatives_end_idx,
    #                 )
    #             ]
    #         )

    #         if query_token:
    #             # TODO: tmp workaround for EL, remove or revise
    #             if query_token == "[START_ENT]":
    #                 query_span = _select_span_with_token(
    #                     question, tensorizer, token_str=query_token
    #                 )
    #                 question_tensors.append(query_span)
    #             else:
    #                 question_tensors.append(
    #                     tensorizer.text_to_tensor(" ".join([query_token, question]))
    #                 )
    #         else:
    #             question_tensors.append(tensorizer.text_to_tensor(question))

    #     ctxs_tensor = torch.cat([ctx.view(1, -1) for ctx in ctx_tensors], dim=0)
    #     questions_tensor = torch.cat([q.view(1, -1) for q in question_tensors], dim=0)

    #     ctx_segments = torch.zeros_like(ctxs_tensor)
    #     question_segments = torch.zeros_like(questions_tensor)

    #     return (
    #         questions_tensor,
    #         question_segments,
    #         ctxs_tensor,
    #         ctx_segments,
    #         positive_ctx_indices,
    #         hard_neg_ctx_indices,
    #         "question",
    #     )