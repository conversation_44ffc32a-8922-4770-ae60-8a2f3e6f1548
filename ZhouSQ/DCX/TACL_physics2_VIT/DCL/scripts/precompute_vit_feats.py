#!/usr/bin/env python3
import os
import json
import argparse
import hashlib
from tqdm import tqdm
from PIL import Image
import torch

from transformers import ViTModel, AutoImageProcessor


def url_to_key(url: str) -> str:
    return hashlib.md5(url.encode('utf-8')).hexdigest()


def extract_urls_from_json(json_path: str):
    data = json.load(open(json_path, 'r'))
    for item in data:
        doc = item.get('doc_token', '')
        # very simple regex-free extraction to avoid deps
        # just find occurrences of 'src="..."'
        start = 0
        while True:
            i = doc.find('src="', start)
            if i == -1:
                break
            j = doc.find('"', i + 5)
            if j == -1:
                break
            yield doc[i+5:j]
            start = j + 1


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--dataset_root', default='DCL/dataset/WebOfScience')
    ap.add_argument('--splits', nargs='+', default=['wos_train.json','wos_val.json','wos_test.json'])
    ap.add_argument('--vit_name', default='/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/VIT_model/vit-base-patch16-224-in21k')
    ap.add_argument('--out_dir', default=None)
    ap.add_argument('--debug_images', default=0, type=int, help='Save first N images for manual inspection')
    args = ap.parse_args()

    out_dir = args.out_dir or os.path.join(args.dataset_root, 'ImageFeats')
    os.makedirs(out_dir, exist_ok=True)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    processor = AutoImageProcessor.from_pretrained(args.vit_name)
    vit = ViTModel.from_pretrained(args.vit_name).to(device)
    vit.eval()

    debug_count = 0
    for sp in args.splits:
        json_path = os.path.join(args.dataset_root, sp)
        urls = list(extract_urls_from_json(json_path))
        print(f"[{sp}] total urls: {len(urls)}")
        for url in tqdm(urls):
            key = url_to_key(url)
            out_path = os.path.join(out_dir, f"{key}.pt")
            if os.path.exists(out_path):
                continue
            # download image
            try:
                import requests, io
                r = requests.get(url, timeout=15)
                r.raise_for_status()
                img = Image.open(io.BytesIO(r.content)).convert('RGB')

                # Debug: save first N images for manual inspection
                if args.debug_images > 0 and debug_count < args.debug_images:
                    debug_path = os.path.join(out_dir, f"debug_{debug_count}_{key}.jpg")
                    img.save(debug_path)
                    print(f"[DEBUG] Saved image {debug_count}: {debug_path}")
                    print(f"  URL: {url}")
                    print(f"  Size: {img.size}")
                    debug_count += 1

            except Exception as e:
                if args.debug_images > 0:
                    print(f"[DEBUG] Failed to download: {url} - {e}")
                continue
            # to pixel_values
            pixel = processor(images=img, return_tensors='pt').pixel_values.to(device)
            with torch.no_grad():
                out = vit(pixel_values=pixel)
                if hasattr(out, 'pooler_output') and out.pooler_output is not None:
                    feat = out.pooler_output[0].detach().cpu()  # [H]
                else:
                    feat = out.last_hidden_state[0,0,:].detach().cpu()
            torch.save(feat, out_path)

    print('Done precomputing image features.')

if __name__ == '__main__':
    main()

