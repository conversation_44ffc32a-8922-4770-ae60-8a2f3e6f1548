#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线统计脚本：统计每个 split (train/val/test) 的含图比例与缓存完整度。
使用方法：
    python DCL/tools/check_image_cache.py --dataset_root DCL/dataset/WebOfScience \
        --splits wos_train.json wos_val.json wos_test.json [--all]

说明：
- 默认统计首图（与训练时默认使用的图一致）
- 指定 --all 时，逐样本统计所有图片链接的缓存命中率，并输出缺失明细
"""
import os
import json
import argparse
import hashlib
from typing import List, Tuple

IMG_RE = __import__('re').compile(r"<img[^>]*src=\"([^\"]+)\"", __import__('re').IGNORECASE)

def extract_first_url(text: str):
    if not text:
        return None
    m = IMG_RE.search(text)
    return m.group(1) if m else None

def extract_all_urls(text: str) -> List[str]:
    if not text:
        return []
    return IMG_RE.findall(text)

def url_to_cache_path(url: str, cache_dir: str) -> str:
    h = hashlib.md5(url.encode('utf-8')).hexdigest()
    ext = os.path.splitext(url)[1]
    if len(ext) > 10 or not ext:
        ext = '.jpg'
    return os.path.join(cache_dir, f"{h}{ext}")

def stats_for_split_first(json_path: str, cache_dir: str) -> Tuple[int, int, int]:
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    total = len(data)
    with_img = 0
    cached = 0
    for item in data:
        url = extract_first_url(item.get('doc_token', ''))
        if url:
            with_img += 1
            if os.path.exists(url_to_cache_path(url, cache_dir)):
                cached += 1
    return total, with_img, cached

def stats_for_split_all(json_path: str, cache_dir: str) -> Tuple[int, int, int, int]:
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    total = len(data)
    sample_with_imgs = 0
    total_urls = 0
    cached_urls = 0
    for item in data:
        urls = extract_all_urls(item.get('doc_token', ''))
        if urls:
            sample_with_imgs += 1
            for url in urls:
                total_urls += 1
                if os.path.exists(url_to_cache_path(url, cache_dir)):
                    cached_urls += 1
    return total, sample_with_imgs, total_urls, cached_urls

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--dataset_root', type=str, default=os.path.join('DCL', 'dataset', 'WebOfScience'))
    ap.add_argument('--splits', nargs='+', default=['wos_train.json', 'wos_val.json', 'wos_test.json'])
    ap.add_argument('--cache_dirname', type=str, default='Images')
    ap.add_argument('--all', action='store_true', help='统计每条样本所有图片链接的缓存情况，并输出缺失明细')
    args = ap.parse_args()

    cache_dir = os.path.join(args.dataset_root, args.cache_dirname)
    os.makedirs(cache_dir, exist_ok=True)

    print(f"Dataset root: {args.dataset_root}")
    print(f"Cache dir   : {cache_dir}")
    print("\nSplit stats:")
    for sp in args.splits:
        jp = os.path.join(args.dataset_root, sp)
        if not os.path.exists(jp):
            print(f" - {sp}: MISSING file")
            continue
        if not args.all:
            total, with_img, cached = stats_for_split_first(jp, cache_dir)
            pct_has = 100.0 * with_img / max(1, total)
            pct_cached = 100.0 * cached / max(1, with_img)
            print(f" - {sp}: total={total}, with_img(first)={with_img} ({pct_has:.1f}%), cached(first)={cached} ({pct_cached:.1f}% of with_img)")
        else:
            total, sample_with_imgs, total_urls, cached_urls = stats_for_split_all(jp, cache_dir)
            pct_has = 100.0 * sample_with_imgs / max(1, total)
            pct_cached = 100.0 * cached_urls / max(1, total_urls)
            print(f" - {sp}: total={total}, sample_with_imgs={sample_with_imgs} ({pct_has:.1f}%), all_urls={total_urls}, cached_urls={cached_urls} ({pct_cached:.1f}% of all_urls)")

if __name__ == '__main__':
    main()

