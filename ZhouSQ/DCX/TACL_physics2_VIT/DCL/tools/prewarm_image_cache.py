#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预热图片缓存脚本：并发下载 train/val/test 中 doc_token 的所有 <img src> 图片，
使用与训练时相同的缓存命名规则，便于训练阶段直接命中缓存。

用法示例：
  python DCL/tools/prewarm_image_cache.py \
      --dataset_root DCL/dataset/WebOfScience \
      --splits wos_train.json wos_val.json wos_test.json \
      --num_workers 16 --timeout 12 --retries 3

注意：
- 下载每条样本中的所有图片链接到缓存目录（对 URL 去重）
- 训练/推理阶段：ImageCacheProvider 优先使用第一张，若第一张不可用则回退到后续图片
- 对无法下载的 URL 会记录到 fail_list.txt 便于后续排查/重试
"""
import os
import re
import io
import json
import time
import queue
import argparse
import hashlib
import threading
from typing import List, Set
from urllib.parse import urlparse

import requests
from PIL import Image

IMG_RE = re.compile(r"<img[^>]*src=\"([^\"]+)\"", re.IGNORECASE)

def extract_all_urls(text: str):
    if not text:
        return []
    return IMG_RE.findall(text)

def url_to_cache_path(url: str, cache_dir: str) -> str:
    h = hashlib.md5(url.encode('utf-8')).hexdigest()
    ext = os.path.splitext(url)[1]
    if len(ext) > 10 or not ext:
        ext = '.jpg'
    return os.path.join(cache_dir, f"{h}{ext}")

def build_url_set(dataset_root: str, splits: List[str]) -> List[str]:
    urls: Set[str] = set()
    for sp in splits:
        jp = os.path.join(dataset_root, sp)
        if not os.path.exists(jp):
            print(f"[WARN] missing split file: {jp}")
            continue
        with open(jp, 'r', encoding='utf-8') as f:
            data = json.load(f)
        for item in data:
            for url in extract_all_urls(item.get('doc_token', '')):
                urls.add(url)
    return sorted(urls)

def make_headers(user_agent: str, referer_from_url: bool, url: str):
    headers = {"User-Agent": user_agent}
    if referer_from_url:
        try:
            up = urlparse(url)
            headers["Referer"] = f"{up.scheme}://{up.netloc}"
        except Exception:
            pass
    return headers

def download_one(url: str, cache_path: str, timeout: int, retries: int, user_agent: str, referer_from_url: bool) -> bool:
    if os.path.exists(cache_path):
        return True
    os.makedirs(os.path.dirname(cache_path), exist_ok=True)
    backoff = 1.0
    for _ in range(retries):
        try:
            headers = make_headers(user_agent, referer_from_url, url)
            r = requests.get(url, headers=headers, timeout=timeout)
            r.raise_for_status()
            img = Image.open(io.BytesIO(r.content)).convert('RGB')
            img.save(cache_path)
            return True
        except Exception:
            time.sleep(backoff)
            backoff = min(backoff * 2, 8.0)
    return False

def worker(q: "queue.Queue[str]", cache_dir: str, timeout: int, retries: int, user_agent: str, referer_from_url: bool, ok_counter: list, fail_list: list):
    while True:
        try:
            url = q.get_nowait()
        except queue.Empty:
            return
        try:
            cp = url_to_cache_path(url, cache_dir)
            ok = download_one(url, cp, timeout, retries, user_agent, referer_from_url)
            if ok:
                ok_counter[0] += 1
            else:
                fail_list.append(url)
        finally:
            q.task_done()

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--dataset_root', type=str, default=os.path.join('DCL', 'dataset', 'WebOfScience'))
    ap.add_argument('--splits', nargs='+', default=['wos_train.json', 'wos_val.json', 'wos_test.json'])
    ap.add_argument('--cache_dirname', type=str, default='Images')
    ap.add_argument('--num_workers', type=int, default=16)
    ap.add_argument('--timeout', type=int, default=12)
    ap.add_argument('--retries', type=int, default=3)
    ap.add_argument('--user_agent', type=str, default='Mozilla/5.0')
    ap.add_argument('--referer_from_url', action='store_true', help='自动添加 Referer=域名，缓解部分站点防盗链')
    args = ap.parse_args()

    cache_dir = os.path.join(args.dataset_root, args.cache_dirname)
    urls = build_url_set(args.dataset_root, args.splits)
    print(f"Total unique URLs: {len(urls)}")

    q: "queue.Queue[str]" = queue.Queue()
    for u in urls:
        q.put(u)

    ok_counter = [0]
    fail_list: List[str] = []

    threads: List[threading.Thread] = []
    for _ in range(max(1, args.num_workers)):
        t = threading.Thread(target=worker, args=(q, cache_dir, args.timeout, args.retries, args.user_agent, args.referer_from_url, ok_counter, fail_list))
        t.daemon = True
        t.start()
        threads.append(t)

    q.join()

    print(f"Downloaded ok: {ok_counter[0]} / {len(urls)}")
    if fail_list:
        fail_path = os.path.join(args.dataset_root, 'fail_list.txt')
        with open(fail_path, 'w', encoding='utf-8') as f:
            for u in fail_list:
                f.write(u + '\n')
        print(f"Failed: {len(fail_list)} (saved to {fail_path})")
    else:
        print("All cached successfully.")

if __name__ == '__main__':
    main()

